import numpy as np
import pandas as pd
import os
import networkx as nx
import matplotlib.pyplot as plt
import argparse
from pathlib import Path

class DataGenerator:
    def __init__(self, config=None):
        """
        初始化数据生成器

        参数:
            config: 配置参数字典，可以包含以下内容：
                - memory_limit: 边缘设备基准内存限制(GB)
                - memory_heterogeneous: 是否生成异构内存大小的边缘设备
                - memory_range_factor: 边缘设备内存大小范围因子，格式为(min, max)
        """
        # 基本参数设置
        self.config =  {
            # 任务设置 5 10 15 20 25 30
            'num_tasks' : 30,
            'num_tasks_range': (10, 10),  # 每个DAG中的任务数量范围
            'num_datasets': 200,         # 生成的数据集数量 200
            'llm_ratio': 0.4,            # LLM任务比例

            # 任务特征范围
            'cpu_cycles_range': {
                'normal': (2e9, 3e10),    # 普通任务的CPU周期范围
                'llm': (5e8, 2e9)        # LLM任务的CPU周期范围
            },
            'data_transfer_range': {
                'image': (0.5, 10),      # 图片数据大小范围 (MB)
                'video': (10, 50),      # 视频数据大小范围 (MB)
                'text': (0.01, 5),       # 文本数据大小范围 (MB)
                'llm': (0.1, 5)          # LLM数据大小范围 (MB)
            },
            'token_count_range': (500, 2000),  # LLM模型token数量范围
            'memory_req_range': {
                'normal': (0.1, 10),      # 普通任务内存需求范围 (GB)
                'llm': (12, 14)            # LLM任务内存需求范围 (GB)
            },

            # 系统资源设置
            'num_edge_servers': 5,       # 边缘服务器数量 2 3 4 5 6
            'local_cpu_freq': 3.0,       # 本地设备CPU频率 (GHz)
            'local_memory_limit': 32,        # 本地设备内存限制 (GB)'
            'edge_cpu_freq_range': (15.0, 20.0),  # 边缘服务器CPU频率范围 (GHz)
            'memory_limit': 32,          # 边缘设备基准内存限制 (GB)  24 32 48 64
            'memory_heterogeneous': True,    # 是否生成异构内存大小的边缘设备
            'memory_range_factor': (0.5, 1),  # 边缘设备内存大小范围因子
            'comm_speed_range': {
                'local_to_edge': (50, 100),    # 本地到边缘通信速度范围 (MB/s)
                'edge_to_edge': (50, 100)    # 边缘到边缘通信速度范围 (MB/s)
            },
            # LLM执行参数
            'token_per_second_range': (0.01, 0.08),  # 每秒生成token数量范围
            'base_execution_time_range': (0.2, 1),  # 生成首个token的时间范围（秒）
            'local_token_per_second_factor': 1,  # 本地设备token生成时间因子（越大越慢）
            'local_base_execution_time_factor': 1,  # 本地设备基础执行时间因子（越大越慢）

            # DAG结构参数
            'dag_density_range': (0.2, 0.6),  # DAG图的密度范围

            # 输出目录
            'output_dir': 'dataset/training'
        }

        # 如果提供了配置，则更新默认配置
        if config:
            self.config.update(config)

        # 创建根输出目录
        os.makedirs(self.config['output_dir'], exist_ok=True)

        # 根据参数创建子目录
        self._create_output_directory()

    def _create_output_directory(self):
        """根据关键参数创建输出目录"""
        # 提取关键参数
        num_tasks = self.config['num_tasks']
        num_edge_servers = self.config['num_edge_servers']
        memory_limit = self.config['memory_limit']
        dag_density = self.config['dag_density_range']
        dag_density_str = f"{dag_density[0]:.1f}_{dag_density[1]:.1f}"

        # 添加内存异构信息到目录名
        if self.config['memory_heterogeneous']:
            memory_range = self.config['memory_range_factor']
            memory_min = int(memory_limit * memory_range[0])
            memory_max = int(memory_limit * memory_range[1])
            memory_str = f"{memory_min}-{memory_max}"
        else:
            memory_str = str(memory_limit)

        # 创建带参数名称的目录名
        dir_name = f"DAG_{num_tasks}_edges_{num_edge_servers}_mem_{memory_str}GB_density_{dag_density_str}"

        # 更新完整输出路径
        self.config['full_output_dir'] = os.path.join(self.config['output_dir'], dir_name)

        # 创建目录
        os.makedirs(self.config['full_output_dir'], exist_ok=True)
        print(f"数据将保存到目录: {self.config['full_output_dir']}")

    def generate_task_features(self, num_tasks):
        """
        生成任务特征

        参数:
            num_tasks: 任务数量

        返回:
            task_features: 任务特征数组 [特征数, 任务数]
        """
        # 计算LLM任务数量
        llm_count = int(num_tasks * self.config['llm_ratio'])
        normal_count = num_tasks - llm_count

        # 任务类型编号：0为普通任务，1为LLM任务
        task_types = np.concatenate([
            np.zeros(normal_count),
            np.ones(llm_count)
        ])
        np.random.shuffle(task_types)  # 随机打乱任务类型

        # 初始化特征数组
        cpu_cycles = np.zeros(num_tasks)
        data_transfer = np.zeros(num_tasks)
        is_llm = np.zeros(num_tasks)
        token_count = np.zeros(num_tasks)
        memory_req = np.zeros(num_tasks)

        for i in range(num_tasks):
            is_llm[i] = task_types[i]

            if is_llm[i] == 1:  # LLM任务
                # CPU周期 - LLM任务通常需要更多计算
                cpu_cycles[i] = np.random.uniform(*self.config['cpu_cycles_range']['llm'])

                # 数据传输大小 - LLM通常是文本输入输出
                data_transfer[i] = np.random.uniform(*self.config['data_transfer_range']['llm'])

                # Token数量
                token_count[i] = np.random.randint(*self.config['token_count_range'])

                # 内存需求 - LLM通常需要更多内存
                memory_req[i] = np.random.uniform(*self.config['memory_req_range']['llm'])
            else:  # 普通任务
                # CPU周期
                cpu_cycles[i] = np.random.uniform(*self.config['cpu_cycles_range']['normal'])

                # 数据传输大小 - 随机选择一种数据类型
                data_type = np.random.choice(['image', 'video', 'text'])
                data_transfer[i] = np.random.uniform(*self.config['data_transfer_range'][data_type])

                # 普通任务的token数为0
                token_count[i] = 0

                # 内存需求
                memory_req[i] = np.random.uniform(*self.config['memory_req_range']['normal'])

        # 构建特征数组
        task_features = np.vstack([
            cpu_cycles,
            data_transfer,
            is_llm,
            token_count,
            memory_req
        ])

        return task_features

    def generate_adjacency_matrix(self, num_tasks):
        """
        生成有向无环图(DAG)的邻接矩阵

        参数:
            num_tasks: 任务数量

        返回:
            adj_matrix: 邻接矩阵 [任务数, 任务数]
        """
        # 创建一个有向图
        G = nx.DiGraph()

        # 添加节点
        for i in range(num_tasks):
            G.add_node(i)

        # 按照拓扑顺序生成边，确保不形成环
        # 节点按顺序排列，只允许从低索引指向高索引
        density = np.random.uniform(*self.config['dag_density_range'])
        max_edges = num_tasks * (num_tasks - 1) // 2  # 最大可能的边数
        num_edges = int(density * max_edges)

        edges_added = 0
        while edges_added < num_edges:
            source = np.random.randint(0, num_tasks - 1)  # 源节点不能是最后一个
            target = np.random.randint(source + 1, num_tasks)  # 目标节点必须在源节点后面

            if not G.has_edge(source, target):
                G.add_edge(source, target)
                edges_added += 1

        # 确保没有孤立节点 - 每个节点至少有一条入边或出边
        for i in range(num_tasks):
            if G.in_degree(i) == 0 and G.out_degree(i) == 0:
                if i == 0:  # 如果是第一个节点，添加出边
                    if num_tasks > 1:
                        G.add_edge(0, 1)
                elif i == num_tasks - 1:  # 如果是最后一个节点，添加入边
                    G.add_edge(num_tasks - 2, num_tasks - 1)
                else:  # 其他节点随机添加入边或出边
                    if np.random.random() < 0.5:
                        G.add_edge(np.random.randint(0, i), i)
                    else:
                        G.add_edge(i, np.random.randint(i + 1, num_tasks))

        # 转换为邻接矩阵
        adj_matrix = nx.to_numpy_array(G).astype(int)

        return adj_matrix

    def generate_machine_resources(self):
        """
        生成机器资源数据

        返回:
            一个DataFrame包含机器资源信息
        """
        # 定义机器数量（本地设备 + 边缘服务器）
        num_machines = 1 + self.config['num_edge_servers']

        # 初始化CPU频率和可用内存
        cpu_freq = np.zeros(num_machines)
        memory_available = np.zeros(num_machines)
        token_per_second = np.zeros(num_machines)
        base_execution_time = np.zeros(num_machines)

        # 设置本地设备资源
        cpu_freq[0] = self.config['local_cpu_freq']  # 本地CPU频率较低
        memory_available[0] = self.config['local_memory_limit']  # 本地内存通常较小

        # 本地设备的LLM执行参数 - 通常比边缘设备慢
        base_token_per_second = np.random.uniform(*self.config['token_per_second_range'])
        token_per_second[0] = base_token_per_second * self.config['local_token_per_second_factor']  # 本地设备生成token更慢

        base_base_execution_time = np.random.uniform(*self.config['base_execution_time_range'])
        base_execution_time[0] = base_base_execution_time * self.config['local_base_execution_time_factor']  # 本地设备基础执行时间更长

        # 设置边缘服务器资源
        for i in range(1, num_machines):
            # CPU频率
            cpu_freq[i] = np.random.uniform(*self.config['edge_cpu_freq_range'])

            # 内存 - 边缘服务器内存通常较大，且有所差异
            if self.config['memory_heterogeneous']:
                # 生成异构内存大小
                memory_range = self.config['memory_range_factor']
                memory_available[i] = self.config['memory_limit'] * np.random.uniform(memory_range[0], memory_range[1])
            else:
                # 使用固定内存大小
                memory_available[i] = self.config['memory_limit'] * np.random.uniform(0.9, 1.0)

            # LLM执行参数 - 每个边缘设备有不同的性能
            token_per_second[i] = np.random.uniform(*self.config['token_per_second_range'])
            base_execution_time[i] = np.random.uniform(*self.config['base_execution_time_range'])

        # 创建DataFrame
        df = pd.DataFrame({
            'CPU_frequence': cpu_freq,
            'Memory_Available': memory_available,
            'token_per_second': token_per_second,
            'base_execution_time': base_execution_time
        })

        return df

    def generate_communication_speed(self):
        """
        生成通信速度矩阵

        返回:
            一个通信速度的DataFrame
        """
        # 定义机器数量（本地设备 + 边缘服务器）
        num_machines = 1 + self.config['num_edge_servers']

        # 初始化通信速度矩阵
        comm_speed = np.zeros((num_machines, num_machines))

        # 设置本地设备到边缘服务器的通信速度
        for i in range(1, num_machines):
            # 本地到边缘
            comm_speed[0, i] = np.random.uniform(*self.config['comm_speed_range']['local_to_edge'])
            # 边缘到本地
            comm_speed[i, 0] = np.random.uniform(*self.config['comm_speed_range']['local_to_edge'])

        # 设置边缘服务器之间的通信速度
        for i in range(1, num_machines):
            for j in range(i+1, num_machines):
                speed = np.random.uniform(*self.config['comm_speed_range']['edge_to_edge'])
                comm_speed[i, j] = speed
                comm_speed[j, i] = speed  # 对称

        # 创建DataFrame - 不使用索引和列名，确保只有纯数值
        df = pd.DataFrame(comm_speed)

        return df

    def visualize_dag(self, adj_matrix, task_features, idx):
        """
        可视化DAG图并保存

        参数:
            adj_matrix: 邻接矩阵
            task_features: 任务特征
            idx: 数据集索引
        """
        G = nx.DiGraph(adj_matrix)
        plt.figure(figsize=(12, 8))

        # 定位布局 - 分层结构
        pos = nx.nx_agraph.graphviz_layout(G, prog='dot')

        # 设置节点颜色 - 蓝色为普通任务，红色为LLM任务
        is_llm = task_features[2]
        colors = ['skyblue' if llm == 0 else 'salmon' for llm in is_llm]

        # 绘制节点和边
        nx.draw(G, pos, with_labels=True, node_color=colors,
                node_size=500, arrowsize=15, font_weight='bold')

        # 添加标题
        plt.title(f'Task Graph #{idx} - Blue: Normal Tasks, Red: LLM Tasks')

        # 保存图像
        vis_dir = os.path.join(self.config['full_output_dir'], 'visualizations')
        os.makedirs(vis_dir, exist_ok=True)
        plt.savefig(os.path.join(vis_dir, f'dag_{idx}.png'))
        plt.close()

    def save_dataset(self, idx, task_features, adj_matrix):
        """
        保存数据集到文件

        参数:
        idx: 数据集索引
        task_features: 任务特征
        adj_matrix: 邻接矩阵
        """
        # 保存任务特征 - 需要进行转置，使特征作为列而不是行
        feature_names = ['Cpu_Cycles', 'Data_Transfer_MB', 'Is_LLM', 'Token_Count', 'Memory_Req']
        task_ids = [str(i) for i in range(task_features.shape[1])]

        # 转置任务特征，使特征作为列
        task_features_df = pd.DataFrame(
            task_features.T,  # 转置，使每行是一个任务，每列是一个特征
            columns=feature_names,  # 将特征名称设为列名
            index=task_ids  # 将任务ID设为行索引
        )

        # 保存为Excel
        task_features_df.to_excel(os.path.join(self.config['full_output_dir'], f'task_{idx}_features.xlsx'))

        # 保存邻接矩阵
        np.save(os.path.join(self.config['full_output_dir'], f'adjacency_{idx}_matrix.npy'), adj_matrix)

    def generate_datasets(self):
        """生成完整的训练数据集"""
        # 生成和保存机器资源
        machine_resources = self.generate_machine_resources()
        machine_resources.to_excel(os.path.join(self.config['full_output_dir'], 'machines_resource1.xlsx'))

        # 生成和保存通信速度
        comm_speed = self.generate_communication_speed()
        # 保存时不包含索引和列名，确保Excel中只有纯数据
        comm_speed.to_excel(os.path.join(self.config['full_output_dir'], 'machine_commu_speed1.xlsx'),
                           index=False, header=False)

        # 生成参数配置文件
        self._save_config_file()

        # 生成任务数据集
        for i in range(self.config['num_datasets']):
            # 随机选择任务数量
            #num_tasks = np.random.randint(*self.config['num_tasks_range'])
            num_tasks = self.config['num_tasks']
            # 生成任务特征
            task_features = self.generate_task_features(num_tasks)

            # 生成邻接矩阵
            adj_matrix = self.generate_adjacency_matrix(num_tasks)

            # 可视化DAG图（可选）
            #if i < 10:  # 只为前10个任务生成可视化
                #self.visualize_dag(adj_matrix, task_features, i)

            # 保存数据集
            self.save_dataset(i, task_features, adj_matrix)

            if (i+1) % 10 == 0:
                print(f"已生成数据集 {i+1}/{self.config['num_datasets']}")

    def _save_config_file(self):
        """保存配置参数到文本文件"""
        config_path = os.path.join(self.config['full_output_dir'], 'dataset_config.txt')

        with open(config_path, 'w') as f:
            f.write("数据集生成配置参数\n")
            f.write("====================\n\n")

            # 记录基本参数
            f.write(f"任务数量范围: {self.config['num_tasks_range']}\n")
            f.write(f"数据集数量: {self.config['num_datasets']}\n")
            f.write(f"LLM任务比例: {self.config['llm_ratio']}\n")
            f.write(f"边缘服务器数量: {self.config['num_edge_servers']}\n")
            f.write(f"基准内存限制: {self.config['memory_limit']} GB\n")

            # 记录内存异构参数
            if self.config['memory_heterogeneous']:
                memory_range = self.config['memory_range_factor']
                memory_min = int(self.config['memory_limit'] * memory_range[0])
                memory_max = int(self.config['memory_limit'] * memory_range[1])
                f.write(f"内存异构: 是\n")
                f.write(f"内存范围: {memory_min}-{memory_max} GB (因子: {memory_range})\n")
            else:
                f.write(f"内存异构: 否\n")

            f.write(f"DAG密度范围: {self.config['dag_density_range']}\n\n")

            # 记录任务特征参数
            f.write("任务特征参数:\n")
            f.write(f"  CPU周期范围 (普通任务): {self.config['cpu_cycles_range']['normal']}\n")
            f.write(f"  CPU周期范围 (LLM任务): {self.config['cpu_cycles_range']['llm']}\n")
            f.write(f"  内存需求范围 (普通任务): {self.config['memory_req_range']['normal']} GB\n")
            f.write(f"  内存需求范围 (LLM任务): {self.config['memory_req_range']['llm']} GB\n")
            f.write(f"  Token数量范围: {self.config['token_count_range']}\n\n")

            # 记录系统参数
            f.write("系统参数:\n")
            f.write(f"  本地CPU频率: {self.config['local_cpu_freq']} GHz\n")
            f.write(f"  边缘服务器CPU频率范围: {self.config['edge_cpu_freq_range']} GHz\n")
            f.write(f"  本地到边缘通信速度: {self.config['comm_speed_range']['local_to_edge']} MB/s\n")
            f.write(f"  边缘到边缘通信速度: {self.config['comm_speed_range']['edge_to_edge']} MB/s\n\n")

            # 记录LLM执行参数
            f.write("LLM执行参数:\n")
            f.write(f"  Token生成速度范围: {self.config['token_per_second_range']} 秒/token\n")
            f.write(f"  基础执行时间范围: {self.config['base_execution_time_range']} 秒\n")
            f.write(f"  本地设备Token生成速度因子: {self.config['local_token_per_second_factor']}\n")
            f.write(f"  本地设备基础执行时间因子: {self.config['local_base_execution_time_factor']}\n")

        print(f"配置参数已保存到: {config_path}")

if __name__ == "__main__":
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='生成DAG调度数据集')
    parser.add_argument('--num_tasks', type=int, default=30, help='每个DAG的任务数量')
    parser.add_argument('--num_tasks_range', type=str, default='30', help='任务数量范围，用逗号分隔')
    parser.add_argument('--num_datasets', type=int, default=200, help='每个配置生成的数据集数量')
    parser.add_argument('--num_edge_servers', type=int, default=5, help='边缘服务器数量')
    parser.add_argument('--memory_limit', type=int, default=64, help='边缘设备基准内存限制(GB)')
    parser.add_argument('--memory_heterogeneous', default='True', action='store_true', help='是否生成异构内存大小的边缘设备')
    parser.add_argument('--memory_range', type=str, default='0.9,1', help='边缘设备内存大小范围因子，格式为min,max')
    parser.add_argument('--output_dir', type=str, default='dataset/training', help='输出目录')
    parser.add_argument('--dag_density', type=str, default='0.2,0.6', help='DAG密度范围，格式为min,max')
    parser.add_argument('--llm_ratio', type=float, default=0.5, help='LLM任务比例')
    parser.add_argument('--token_per_second_min', type=float, default=2, help='每秒生成token数量最小值')
    parser.add_argument('--token_per_second_max', type=float, default=8, help='每秒生成token数量最大值')
    parser.add_argument('--base_execution_time_min', type=float, default=0.2, help='生成首个token的时间最小值（秒）')
    parser.add_argument('--base_execution_time_max', type=float, default=0.8, help='生成首个token的时间最大值（秒）')
    parser.add_argument('--local_token_factor', type=float, default=1, help='本地设备token生成时间因子')
    parser.add_argument('--local_base_time_factor', type=float, default=1, help='本地设备基础执行时间因子')

    args = parser.parse_args()

    # 解析任务数量范围
    num_tasks_range = [int(x) for x in args.num_tasks_range.split(',')]

    # 解析DAG密度范围
    dag_density_min, dag_density_max = map(float, args.dag_density.split(','))

    # 解析内存范围因子
    memory_range_min, memory_range_max = map(float, args.memory_range.split(','))

    # 创建配置
    config = {
        'num_tasks': args.num_tasks,
        'num_tasks_range': num_tasks_range,
        'num_datasets': args.num_datasets,
        'num_edge_servers': args.num_edge_servers,
        'memory_limit': args.memory_limit,
        'memory_heterogeneous': args.memory_heterogeneous,
        'memory_range_factor': (memory_range_min, memory_range_max),
        'output_dir': args.output_dir,
        'dag_density_range': (dag_density_min, dag_density_max),
        'llm_ratio': args.llm_ratio,
        # LLM执行参数
        'token_per_second_range': (args.token_per_second_min, args.token_per_second_max),
        'base_execution_time_range': (args.base_execution_time_min, args.base_execution_time_max),
        'local_token_per_second_factor': args.local_token_factor,
        'local_base_execution_time_factor': args.local_base_time_factor
    }

    # 创建数据生成器并生成数据
    generator = DataGenerator(config)
    generator.generate_datasets()

    print("数据生成完成！")