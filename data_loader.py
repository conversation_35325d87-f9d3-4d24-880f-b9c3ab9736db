import numpy as np
import pandas as pd
import os
import time
from base_config import BaseConfig

# 奖励和惩罚常量 - 增加奖励缩放以改善训练收敛性
REWARD_TASK_COMPLETED = 10.0  # 任务完成的基础奖励 (增加10倍)
PENALTY_TASK_FAIL_BAD_ASSIGNMENT = -50.0  # 因错误分配导致任务失败的惩罚 (增加10倍)
PENALTY_TASK_TO_PENDING = -5.0  # 任务进入等待队列的惩罚 (增加10倍)
REWARD_TASK_LEAVES_PENDING = 5.0  # 任务离开等待队列的奖励 (增加10倍)
PENALTY_DEADLOCK = -100.0  # 死锁惩罚 (增加5倍)
REWARD_COMPLETION_BONUS = 200.0  # 所有任务完成的奖励 (增加4倍)
PENALTY_TIME_FACTOR = 0.001  # 时间惩罚因子 (增加10倍)

class DataLoader:
    def __init__(self, config=None):
        """
        初始化DataLoader

        参数:
            config: 配置对象，如果为None则使用BaseConfig
        """
        # 配置
        self.config = config if config is not None else BaseConfig()

        # 缓存
        self._adj_matrix_cache = {}
        self._task_features_cache = {}
        self._machine_resources = None
        self._comm_speed = None
        self._memory_status = None

        # 调试模式
        self.debug = False

        # 步骤状态初始化
        self._step_state = None

        # 添加缓存以避免重复加载资源数据
        self._machine_resources_cache = None
        self._comm_speed_cache = None
        self._memory_status_cache = None

        # 添加一个常量用于表示最大时间，替代无穷大
        self.MAX_TIME_VALUE = 1e4  # 降低最大时间值

        # 添加奖励缩放因子，使奖励数量级更合适
        self.REWARD_SCALE = 1.0  # 使用原始奖励值，奖励常量已经被放大

        # 添加调试标志
        self.debug = False

    def set_debug(self, enable=True):
        """设置调试模式"""
        self.debug = enable
        if enable:
            print("已启用调试模式，将显示详细信息")
        else:
            print("已禁用调试模式")

    def load_machine_commu_speed(self):
        """加载机器间通信速度数据"""
        if self._comm_speed_cache is None:
            # 获取通信速度文件路径
            file_path = self.config.MACHINE_COMMU_SPEED_PATH

            # 检查文件是否存在
            if not os.path.exists(file_path):
                print(f"警告: 文件 {file_path} 不存在")
                # 尝试使用默认路径
                base_path = f'dataset/training/DAG_{self.config.MAX_TASKS_NUM}_edges_{self.config.NUM_EDGE_SERVERS}_mem_{self.config.MEMORY_LIMIT}GB_density_0.4_0.6'
                file_path = os.path.join(base_path, 'machine_commu_speed1.xlsx')
                print(f"尝试使用默认路径: {file_path}")

                if not os.path.exists(file_path):
                    raise FileNotFoundError(f"无法找到通信速度文件: {file_path}")

            # 使用header=None和index_col=None来明确忽略表头和索引列
            self._comm_speed_cache = pd.read_excel(file_path, header=None, index_col=None).to_numpy()
            print(f"通信速度矩阵形状: {self._comm_speed_cache.shape}")
        return self._comm_speed_cache

    def load_machines_resource(self):
        """加载机器资源数据"""
        if self._machine_resources_cache is None:
            # 获取机器资源文件路径
            file_path = self.config.MACHINES_RESOURCE_PATH

            # 检查文件是否存在
            if not os.path.exists(file_path):
                print(f"警告: 文件 {file_path} 不存在")
                # 尝试使用默认路径
                base_path = f'dataset/training/DAG_{self.config.MAX_TASKS_NUM}_edges_{self.config.NUM_EDGE_SERVERS}_mem_{self.config.MEMORY_LIMIT}GB_density_0.4_0.6'
                file_path = os.path.join(base_path, 'machines_resource1.xlsx')
                print(f"尝试使用默认路径: {file_path}")

                if not os.path.exists(file_path):
                    raise FileNotFoundError(f"无法找到机器资源文件: {file_path}")

            # 读取Excel文件
            df = pd.read_excel(file_path)

            # 读取CPU频率
            if 'CPU_frequence' in df.columns:
                self._machine_resources_cache = df['CPU_frequence'].values
                print(f"已加载异构CPU频率参数: {self._machine_resources_cache}")
            else:
                # 使用默认值
                self._machine_resources_cache = np.ones(self.config.NUM_EDGE_SERVERS + 1) * 3.0
                self._machine_resources_cache[0] = self.config.LOCAL_CPU_FREQ  # 本地设备使用配置的CPU频率
                print(f"未找到CPU频率列，使用默认值")

            # 加载token_per_second参数（如果存在）
            if 'token_per_second' in df.columns:
                self.token_per_second = df['token_per_second'].values
                print(f"已加载异构token_per_second参数: {self.token_per_second}")
            else:
                # 使用默认值
                self.token_per_second = np.full(len(self._machine_resources_cache), self.config.TOKEN_PER_SECOND)
                print(f"使用默认token_per_second参数: {self.config.TOKEN_PER_SECOND}")

            # 加载base_execution_time参数（如果存在）
            if 'base_execution_time' in df.columns:
                self.base_execution_time = df['base_execution_time'].values
                print(f"已加载异构base_execution_time参数: {self.base_execution_time}")
            else:
                # 使用默认值
                self.base_execution_time = np.full(len(self._machine_resources_cache), self.config.BASE_EXECUTION_TIME)
                print(f"使用默认base_execution_time参数: {self.config.BASE_EXECUTION_TIME}")

        return self._machine_resources_cache

    def load_memory_status(self):
        """加载机器内存状态数据"""
        if self._memory_status_cache is None:
            try:
                # 获取机器资源文件路径
                file_path = self.config.MACHINES_RESOURCE_PATH

                # 检查文件是否存在
                if not os.path.exists(file_path):
                    print(f"警告: 文件 {file_path} 不存在")
                    # 尝试使用默认路径
                    base_path = f'dataset/training/DAG_{self.config.MAX_TASKS_NUM}_edges_{self.config.NUM_EDGE_SERVERS}_mem_{self.config.MEMORY_LIMIT}GB_density_0.4_0.6'
                    file_path = os.path.join(base_path, 'machines_resource1.xlsx')
                    print(f"尝试使用默认路径: {file_path}")

                    if not os.path.exists(file_path):
                        raise FileNotFoundError(f"无法找到机器资源文件: {file_path}")

                # 读取Excel文件
                df = pd.read_excel(file_path)

                # 检查是否存在Memory_Available列
                if 'Memory_Available' in df.columns:
                    self._memory_status_cache = df['Memory_Available'].values
                    print(f"已加载异构内存参数: {self._memory_status_cache}")

                    # 确保本地设备（索引0）使用本地内存限制
                    if len(self._memory_status_cache) > 0:
                        self._memory_status_cache[0] = self.config.LOCAL_MEMORY_LIMIT
                else:
                    # 如果没有内存数据，则使用默认内存限制
                    self._memory_status_cache = np.ones(self.config.NUM_EDGE_SERVERS + 1) * self.config.MEMORY_LIMIT
                    # 本地设备使用较小的内存限制
                    self._memory_status_cache[0] = self.config.LOCAL_MEMORY_LIMIT
                    print(f"未找到Memory_Available列，使用默认内存配置")
            except Exception as e:
                # 如果读取出错，则使用默认内存限制
                self._memory_status_cache = np.ones(self.config.NUM_EDGE_SERVERS + 1) * self.config.MEMORY_LIMIT
                # 本地设备使用较小的内存限制
                self._memory_status_cache[0] = self.config.LOCAL_MEMORY_LIMIT
                print(f"读取内存数据出错: {e}，使用默认内存配置")

        return self._memory_status_cache

    def load_task_features(self, task_idx):
        """加载任务特征数据"""
        # 使用配置中的训练数据路径
        file_path = os.path.join(self.config.TRAINING_DATA_PATH, f'task_{task_idx}_features.xlsx')

        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"警告: 文件 {file_path} 不存在")
            # 尝试使用默认路径
            base_path = f'dataset/training/DAG_{self.config.MAX_TASKS_NUM}_edges_{self.config.NUM_EDGE_SERVERS}_mem_{self.config.MEMORY_LIMIT}GB_density_0.4_0.6'
            file_path = os.path.join(base_path, f'task_{task_idx}_features.xlsx')
            print(f"尝试使用默认路径: {file_path}")

            if not os.path.exists(file_path):
                raise FileNotFoundError(f"无法找到任务特征文件: {file_path}")

        df = pd.read_excel(file_path)

        # 检查文件格式，适配不同的列名
        columns = df.columns.tolist()

        # 创建一个包含5个特征的数组
        features = np.zeros((5, df.shape[0]))

        # 根据不同的列名格式进行适配
        if 'Cpu_Cycles' in columns:
            # 原始格式
            df_selected = df[['Cpu_Cycles', 'Data_Transfer_MB', 'Is_LLM', 'Token_Count', 'Memory_Req']]
            return df_selected.T.to_numpy()
        else:
            # 测试数据集格式
            # 1. CPU周期 - 使用Execution_Time_s作为替代
            if 'Execution_Time_s' in columns:
                features[0, :] = df['Execution_Time_s'].values * 1e9  # 转换为周期数的近似值

            # 2. 数据传输
            if 'Data_Transfer_MB' in columns:
                features[1, :] = df['Data_Transfer_MB'].values

            # 3. 是否LLM任务 - 根据内存需求和执行时间推断
            if 'Peak_memory_MB' in columns and 'Execution_Time_s' in columns:
                # 如果内存需求大于1GB且执行时间较长，可能是LLM任务
                features[2, :] = ((df['Peak_memory_MB'].values > 1000) & (df['Execution_Time_s'].values > 1.0)).astype(float)

            # 4. Token数量 - 无法直接获取，使用执行时间的比例作为估计
            if 'Execution_Time_s' in columns:
                features[3, :] = df['Execution_Time_s'].values * 100  # 粗略估计

            # 5. 内存需求 - 转换为GB
            if 'Peak_memory_MB' in columns:
                features[4, :] = df['Peak_memory_MB'].values / 1024.0  # MB转GB

            return features

    def load_adjacency_matrix(self, task_idx):
        """加载邻接矩阵数据"""
        # 使用配置中的训练数据路径
        file_path = os.path.join(self.config.TRAINING_DATA_PATH, f'adjacency_{task_idx}_matrix.npy')

        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"警告: 文件 {file_path} 不存在")
            # 尝试使用默认路径
            base_path = f'dataset/training/DAG_{self.config.MAX_TASKS_NUM}_edges_{self.config.NUM_EDGE_SERVERS}_mem_{self.config.MEMORY_LIMIT}GB_density_0.4_0.6'
            file_path = os.path.join(base_path, f'adjacency_{task_idx}_matrix.npy')
            print(f"尝试使用默认路径: {file_path}")

            if not os.path.exists(file_path):
                raise FileNotFoundError(f"无法找到邻接矩阵文件: {file_path}")

        return np.load(file_path)

    def process_state(self, task_features, adj_matrix, machine_resources, comm_speed, memory_status=None):
        """
        处理状态数据，生成适合单任务卸载决策的状态表示

        参数:
            task_features: 任务特征数组，形状为 [特征数, 任务数]
            adj_matrix: 邻接矩阵，形状为 [任务数, 任务数]
            machine_resources: 机器资源数组
            comm_speed: 通信速度矩阵
            memory_status: 机器内存状态数组
        """
        # 获取任务数量
        num_tasks = task_features.shape[1]

        # 如果未提供内存状态，则加载默认内存状态
        if memory_status is None:
            memory_status = self.load_memory_status()

        # 计算任务特征统计信息
        task_cpu_cycles = np.mean(task_features[0])
        task_data_transfer = np.mean(task_features[1])
        llm_task_count = np.sum(task_features[2])
        token_count = np.sum(task_features[3])
        memory_req = np.mean(task_features[4])

        # 图结构特征
        in_degree = np.sum(adj_matrix, axis=0)
        out_degree = np.sum(adj_matrix, axis=1)
        avg_in_degree = np.mean(in_degree)
        avg_out_degree = np.mean(out_degree)

        # 关键路径计算
        task_priorities = self.calculate_task_priorities(adj_matrix)
        critical_path = self.find_critical_path(adj_matrix, task_features[0])
        critical_path_length = len(critical_path)

        # 内存和通信特征
        normalized_memory = memory_status / self.config.MEMORY_LIMIT
        avg_comm_speed = np.mean(comm_speed[comm_speed > 0]) if np.sum(comm_speed > 0) > 0 else 0

        # 计算前驱/后继关系
        pred_count = np.sum(in_degree)
        succ_count = np.sum(out_degree)

        # 合并为固定维度状态向量
        state = np.array([
            task_cpu_cycles,
            task_data_transfer,
            llm_task_count/max(1, num_tasks),
            token_count/max(1, 100*num_tasks),
            memory_req/self.config.MEMORY_LIMIT,
            num_tasks/self.config.MAX_TASKS_NUM,
            avg_in_degree/max(1, num_tasks),
            avg_out_degree/max(1, num_tasks),
            critical_path_length/max(1, num_tasks),
            pred_count/max(1, num_tasks*num_tasks),
            succ_count/max(1, num_tasks*num_tasks),
            avg_comm_speed,
            *normalized_memory,
        ])

        # 确保维度匹配配置中的STATE_DIM
        if len(state) < self.config.STATE_DIM:
            state = np.pad(state, (0, self.config.STATE_DIM - len(state)))
        else:
            state = state[:self.config.STATE_DIM]

        return state

    def process_state_as_sequence(self, task_features, adj_matrix, machine_resources, comm_speed, memory_status=None):
        """
        将状态处理为增强的序列形式，包含任务特征、设备状态和DAG结构信息

        参数:
            task_features: 任务特征矩阵 [特征数, 任务数]
            adj_matrix: 邻接矩阵 [任务数, 任务数]
            machine_resources: 机器资源 [机器数] (CPU频率)
            comm_speed: 通信速度矩阵 [机器数, 机器数]
            memory_status: 内存状态 [机器数]

        返回:
            增强的序列化状态 [序列长度, 特征维度]
        """
        num_tasks = adj_matrix.shape[0]
        num_machines = len(machine_resources)

        # 如果未提供内存状态，则加载默认内存状态
        if memory_status is None:
            memory_status = self.load_memory_status()

        # 1. 归一化任务特征
        # CPU周期归一化
        max_cpu = np.max(task_features[0]) if np.max(task_features[0]) > 0 else 1.0
        normalized_cpu = task_features[0] / (max_cpu + 1e-8)

        # 数据传输归一化
        max_transfer = np.max(task_features[1]) if np.max(task_features[1]) > 0 else 1.0
        normalized_transfer = task_features[1] / (max_transfer + 1e-8)

        # 内存需求归一化
        max_memory = np.max(task_features[4]) if np.max(task_features[4]) > 0 else 1.0
        normalized_memory_req = task_features[4] / (max_memory + 1e-8)

        # 2. 归一化通信成本
        max_comm = np.max(comm_speed) if np.max(comm_speed) > 0 else 1.0
        normalized_comm = comm_speed / (max_comm + 1e-8)

        # 3. 归一化内存状态
        normalized_memory = memory_status / self.config.MEMORY_LIMIT

        # 4. 归一化计算能力
        max_compute = np.max(machine_resources) if np.max(machine_resources) > 0 else 1.0
        normalized_compute = machine_resources / (max_compute + 1e-8)

        # 5. 获取前继和后继任务
        # 为每个任务找出前继任务
        predecessors = [[] for _ in range(num_tasks)]
        for i in range(num_tasks):
            for j in range(num_tasks):
                if adj_matrix[j, i] == 1:  # j是i的前继
                    predecessors[i].append(j)

        # 为每个任务找出后继任务
        successors = [[] for _ in range(num_tasks)]
        for i in range(num_tasks):
            for j in range(num_tasks):
                if adj_matrix[i, j] == 1:  # j是i的后继
                    successors[i].append(j)

        # 6. 构建增强的序列化状态
        sequence_length = self.config.SEQ_LEN

        # 如果配置中没有MAX_PREDECESSORS和MAX_SUCCESSORS，使用默认值
        max_predecessors = 3
        max_successors = 3

        if hasattr(self.config, 'MAX_PREDECESSORS'):
            max_predecessors = self.config.MAX_PREDECESSORS

        if hasattr(self.config, 'MAX_SUCCESSORS'):
            max_successors = self.config.MAX_SUCCESSORS

        # 计算特征维度:
        # 任务特征(5) + 通信特征(1) + 内存状态(num_machines) + 计算能力(num_machines) +
        # TTFT(num_machines) + TPOT(num_machines) +
        # 前继任务(max_predecessors) + 后继任务(max_successors) + 掩码(1)
        calculated_dim = (5 + 1 +
                         num_machines +  # 内存状态
                         num_machines +  # 计算能力
                         num_machines +  # TTFT
                         num_machines +  # TPOT
                         max_predecessors + max_successors + 1)

        # 打印详细的维度计算
        print(f"特征维度计算: 5(任务) + 1(通信) + {num_machines}(内存) + {num_machines}(CPU) + {num_machines}(TTFT) + {num_machines}(TPOT) + {max_predecessors}(前继) + {max_successors}(后继) + 1(掩码) = {calculated_dim}")

        # 强制使用计算的维度，确保一致性
        feature_dim = calculated_dim

        # 更新配置中的特征维度
        if hasattr(self.config, 'TASK_FEATURE_DIM'):
            old_dim = self.config.TASK_FEATURE_DIM
            if old_dim != calculated_dim:
                print(f"[WARNING] 更新特征维度: {old_dim} -> {calculated_dim}")
            self.config.TASK_FEATURE_DIM = calculated_dim

        print(f"[INFO] 使用特征维度: {feature_dim} (机器数: {num_machines}, 前继: {max_predecessors}, 后继: {max_successors})")

        # 初始化序列状态矩阵
        sequence_state = np.zeros((sequence_length, feature_dim))

        # 填充序列状态
        for i in range(num_tasks):
            if i >= sequence_length:
                break

            # 当前特征索引
            idx = 0

            # 任务特征
            sequence_state[i, idx] = normalized_cpu[i]  # 归一化CPU周期
            idx += 1
            sequence_state[i, idx] = normalized_transfer[i]  # 归一化数据传输
            idx += 1
            sequence_state[i, idx] = task_features[2, i]  # 是否LLM任务 (0/1)
            idx += 1
            sequence_state[i, idx] = task_features[3, i] / self.config.MAX_TOKEN_COUNT  # 归一化token数
            idx += 1
            sequence_state[i, idx] = normalized_memory_req[i]  # 归一化内存需求
            idx += 1

            # 通信特征
            sequence_state[i, idx] = np.mean(normalized_comm)  # 平均通信速度
            idx += 1

            # 内存状态
            mem_end_idx = min(idx + num_machines, feature_dim - 1)
            sequence_state[i, idx:mem_end_idx] = normalized_memory[:mem_end_idx-idx]  # 归一化内存状态
            idx = mem_end_idx

            # 计算能力
            compute_end_idx = min(idx + num_machines, feature_dim - 1)
            sequence_state[i, idx:compute_end_idx] = normalized_compute[:compute_end_idx-idx]  # 归一化计算能力
            idx = compute_end_idx

            # TTFT (Time To First Token)
            # 加载TTFT信息，如果没有则使用默认值
            ttft_values = np.ones(num_machines) * self.config.BASE_EXECUTION_TIME
            # 如果有异构base_execution_time参数，则使用它
            if hasattr(self, 'base_execution_time') and self.base_execution_time is not None:
                ttft_values = self.base_execution_time
            # 归一化TTFT值（假设范围在0.1-1.0秒之间）
            normalized_ttft = ttft_values / 1.0  # 归一化到0-1范围
            ttft_end_idx = min(idx + num_machines, feature_dim - 1)
            sequence_state[i, idx:ttft_end_idx] = normalized_ttft[:ttft_end_idx-idx]
            idx = ttft_end_idx

            # TPOT (Time Per Output Token)
            # 加载TPOT信息，如果没有则使用默认值
            tpot_values = np.ones(num_machines) * 0.03  # 默认每token 0.03秒
            # 如果有异构token_per_second参数，则使用它的倒数（秒/token）
            if hasattr(self, 'token_per_second') and self.token_per_second is not None:
                # 避免除以零
                token_per_second = np.maximum(self.token_per_second, 0.001)
                tpot_values = 1.0 / token_per_second
            # 归一化TPOT值（秒/token）
            # 找出最大值用于归一化
            max_tpot = np.max(tpot_values) if np.max(tpot_values) > 0 else 1.0
            normalized_tpot = tpot_values / max_tpot  # 直接归一化，保留相对关系
            tpot_end_idx = min(idx + num_machines, feature_dim - 1)
            sequence_state[i, idx:tpot_end_idx] = normalized_tpot[:tpot_end_idx-idx]
            idx = tpot_end_idx

            # 前继任务索引 (最多max_predecessors个)
            pred_end_idx = min(idx + max_predecessors, feature_dim - 1)
            pred_list = predecessors[i][:pred_end_idx-idx]  # 取前max_predecessors个前继
            for j, pred in enumerate(pred_list):
                if idx + j < pred_end_idx:
                    sequence_state[i, idx+j] = pred / num_tasks  # 归一化任务索引
            idx = pred_end_idx

            # 后继任务索引 (最多max_successors个)
            succ_end_idx = min(idx + max_successors, feature_dim - 1)
            succ_list = successors[i][:succ_end_idx-idx]  # 取前max_successors个后继
            for j, succ in enumerate(succ_list):
                if idx + j < succ_end_idx:
                    sequence_state[i, idx+j] = succ / num_tasks  # 归一化任务索引
            idx = succ_end_idx

            # 掩码
            if idx < feature_dim:
                sequence_state[i, -1] = 1.0  # 有效任务掩码

        if self.debug:
            print(f"序列状态形状: {sequence_state.shape}")
            print(f"特征维度: {feature_dim}")
            print(f"任务数: {num_tasks}")
            print(f"机器数: {num_machines}")

        return sequence_state

    def calculate_task_priorities(self, adj_matrix):
        """
        计算任务的优先级，基于拓扑排序和关键路径

        参数:
            adj_matrix: 邻接矩阵，形状为 [任务数, 任务数]

        返回:
            任务优先级数组
        """
        num_tasks = adj_matrix.shape[0]

        # 计算每个任务的入度
        in_degree = np.sum(adj_matrix, axis=0)

        # 计算每个任务的出度
        out_degree = np.sum(adj_matrix, axis=1)

        # 初始化优先级
        priorities = np.zeros(num_tasks)

        # 使用拓扑级别和关键路径作为优先级指标
        # 入度为0的任务为起始任务，优先级高
        # 出度为0的任务为终止任务，优先级低
        for i in range(num_tasks):
            level = num_tasks - in_degree[i]  # 拓扑级别
            criticality = out_degree[i]       # 关键程度
            priorities[i] = level + 0.1 * criticality

        return priorities

    def find_critical_path(self, adj_matrix, task_weights):
        """
        找出DAG中的关键路径

        参数:
            adj_matrix: 邻接矩阵
            task_weights: 任务权重（例如，CPU周期）

        返回:
            关键路径上的节点列表
        """
        num_tasks = adj_matrix.shape[0]

        # 初始化最早完成时间和最晚完成时间
        earliest_finish = np.zeros(num_tasks)
        latest_finish = np.zeros(num_tasks)

        # 计算拓扑排序
        topo_order = self.topological_sort(adj_matrix)

        # 正向传播：计算最早完成时间
        for task in topo_order:
            # 找出所有前驱任务
            earliest_start = 0
            for pred in range(num_tasks):
                if adj_matrix[pred, task] == 1:
                    earliest_start = max(earliest_start, earliest_finish[pred])

            earliest_finish[task] = earliest_start + task_weights[task]

        # 计算总项目持续时间
        project_duration = np.max(earliest_finish)

        # 初始化最晚完成时间为项目持续时间
        latest_finish.fill(project_duration)

        # 反向传播：计算最晚完成时间
        for task in reversed(topo_order):
            # 找出所有后继任务
            latest_finish_temp = project_duration
            for succ in range(num_tasks):
                if adj_matrix[task, succ] == 1:
                    latest_finish_temp = min(latest_finish_temp, latest_finish[succ] - task_weights[succ])

            latest_finish[task] = latest_finish_temp

        # 计算关键路径：最早完成时间=最晚完成时间的任务
        critical_path = []
        for task in range(num_tasks):
            if abs(earliest_finish[task] - latest_finish[task]) < 1e-6:
                critical_path.append(task)

        return critical_path

    def calculate_time_penalty(self, start_time, finish_time, task_idx):
        """计算时间惩罚"""
        execution_time = finish_time - start_time
        return execution_time * PENALTY_TIME_FACTOR

    def topological_sort(self, adj_matrix):
        """
        对DAG执行拓扑排序

        参数:
            adj_matrix: 邻接矩阵

        返回:
            拓扑排序的任务列表
        """
        num_tasks = adj_matrix.shape[0]
        in_degree = np.sum(adj_matrix, axis=0)  # 计算每个任务的入度
        zero_in_degree = [i for i in range(num_tasks) if in_degree[i] == 0]  # 入度为0的任务
        execution_order = []

        while zero_in_degree:
            task = zero_in_degree.pop(0)
            execution_order.append(task)

            # 更新邻接矩阵，减少依赖
            for i in range(num_tasks):
                if adj_matrix[task, i] == 1:
                    in_degree[i] -= 1
                    if in_degree[i] == 0:
                        zero_in_degree.append(i)

        return execution_order

    def get_next_state(self, current_task_idx):
        """获取下一个状态"""
        next_task_idx = min(current_task_idx + 1, self.config.NUM_STEPS)

        # 加载数据
        task_features = self.load_task_features(next_task_idx)
        adj_matrix = self.load_adjacency_matrix(next_task_idx)
        machine_resources = self.load_machines_resource()
        comm_speed = self.load_machine_commu_speed()

        # 处理状态
        next_state = self.process_state(
            task_features,
            adj_matrix,
            machine_resources,
            comm_speed
        )

        return next_state, next_task_idx

    def get_next_state_sequence(self, current_task_idx):
        """获取下一个序列化状态"""
        next_task_idx = min(current_task_idx + 1, self.config.NUM_STEPS)

        # 加载数据
        task_features = self.load_task_features(next_task_idx)
        adj_matrix = self.load_adjacency_matrix(next_task_idx)
        machine_resources = self.load_machines_resource()
        comm_speed = self.load_machine_commu_speed()
        memory_status = self.load_memory_status()

        # 处理序列化状态
        next_state_seq = self.process_state_as_sequence(
            task_features,
            adj_matrix,
            machine_resources,
            comm_speed,
            memory_status
        )

        return next_state_seq, next_task_idx


    def _get_current_state(self, adj_matrix, task_features, num_tasks, machine_resources, comm_speed, memory_status, use_sequence=False):
        """获取当前状态"""
        if use_sequence:
            return self.process_state_as_sequence(task_features, adj_matrix, machine_resources, comm_speed, memory_status)
        else:
            return self.process_state(task_features, adj_matrix, machine_resources, comm_speed, memory_status)

    def save_dataset(self, idx, task_features, adj_matrix):
        """
        保存数据集到文件

        参数:
            idx: 数据集索引
            task_features: 任务特征
            adj_matrix: 邻接矩阵
        """
        # 保存任务特征 - 需要进行转置，使特征作为列而不是行
        feature_names = ['Cpu_Cycles', 'Data_Transfer_MB', 'Is_LLM', 'Token_Count', 'Memory_Req']
        task_ids = [str(i) for i in range(task_features.shape[1])]

        # 转置任务特征，使特征作为列
        task_features_df = pd.DataFrame(
            task_features.T,  # 转置，使每行是一个任务，每列是一个特征
            columns=feature_names,  # 将特征名称设为列名
            index=task_ids  # 将任务ID设为行索引
        )

        # 保存为Excel
        task_features_df.to_excel(os.path.join(self.config.output_dir, f'task_{idx}_features.xlsx'))

        # 保存邻接矩阵
        np.save(os.path.join(self.config.output_dir, f'adjacency_{idx}_matrix.npy'), adj_matrix)

    def _init_step_state(self, task_features, adj_matrix, machine_resources, comm_speed, memory_status):
        """初始化步骤状态"""
        # 复制数据
        # 确保使用原始的内存状态（满容量），而不是当前可能已经被修改的状态
        fresh_memory_status = self.load_memory_status().copy() if memory_status is None else memory_status.copy()

        self._step_state = {
            'task_features': task_features.copy(),
            'adj_matrix': adj_matrix.copy(),
            'machine_resources': machine_resources.copy() if machine_resources is not None else None,
            'comm_speed': comm_speed.copy() if comm_speed is not None else None,
            'memory_status': fresh_memory_status,

            'current_step': 0,
            'current_time': 0,
            'completed_tasks': set(),  # 使用set而非list
            'failed_tasks': set(),  # 使用set而非list
            'task_start_time': np.zeros(task_features.shape[1]),
            'task_finish_time': np.zeros(task_features.shape[1]),
            'task_machines': np.full(task_features.shape[1], -1),
            'machine_assignment': np.full(task_features.shape[1], -1),  # 添加machine_assignment键
            'machine_finish_time': np.zeros(self.config.NUM_EDGE_SERVERS + 1),
            'dag_failed': False,  # 标记整个DAG是否失败
            'deadlock_detected': False,  # 标记是否检测到死锁
            'pending_tasks': set(),  # 添加pending_tasks键，用于跟踪待处理的任务
            'running_tasks': set(),  # 添加running_tasks键，用于跟踪正在运行的任务
            'memory_released_tasks': set(),  # 添加memory_released_tasks键，用于跟踪已释放内存的任务
            'total_completion_time': 0,  # 添加total_completion_time键，用于跟踪总完成时间
            'completion_ratio': 0.0,  # 添加completion_ratio键，用于跟踪完成率
            'completion_ratios': [],  # 添加completion_ratios键，用于跟踪完成率历史
            'total_reward': 0.0,  # 添加total_reward键，用于跟踪总奖励
            'memory_utilization': np.zeros(self.config.NUM_EDGE_SERVERS + 1),
            'task_memory_usage': np.zeros((task_features.shape[1], self.config.NUM_EDGE_SERVERS + 1)),  # 添加task_memory_usage键
            'FT_ud': np.zeros(task_features.shape[1]),  # 用户设备完成时间
            'FT_up': np.zeros(task_features.shape[1]),  # 上传完成时间
            'FT_ec': np.zeros(task_features.shape[1]),  # 边缘计算完成时间
            'FT_do': np.zeros(task_features.shape[1]),  # 下载完成时间
            'task_completion_times': {},
            'ready_tasks': [],  # 就绪任务队列
            'loop_counter': 0,  # 循环计数器，用于检测死锁

            # 新增指标
            'deadlock_count': 0,  # 临时死锁发生次数
            'task_wait_times': np.zeros(task_features.shape[1]),  # 任务等待时间（从就绪到开始执行）
            'machine_idle_times': np.zeros(self.config.NUM_EDGE_SERVERS + 1),  # 机器空闲时间
            'task_reassignments': np.zeros(task_features.shape[1]),  # 任务重新分配次数
            'memory_utilization_history': [],  # 内存利用率历史
            'load_balance_history': [],  # 负载均衡度历史
            'event_rewards': []  # 事件奖励历史
        }

        # 更新就绪任务
        self._update_ready_tasks(self._step_state['adj_matrix'], self._step_state['completed_tasks'], task_features.shape[1])

        # 打印初始内存状态
        print("\n===== 初始机器内存状态 =====")
        # 确保只遍历实际存在的机器
        num_machines = min(len(self._step_state['memory_status']), len(self._step_state['memory_utilization']))
        for i in range(num_machines):
            print(f"机器 {i}: 总内存 {self._step_state['memory_status'][i]:.2f}GB, 已使用 {self._step_state['memory_utilization'][i]:.2f}GB")
        print("===========================\n")

    def _update_ready_tasks(self, adj_matrix=None, completed_tasks=None, num_tasks=None):
        """
        更新就绪任务列表 - 找出所有前置依赖都已完成的任务

        参数:
            adj_matrix: 任务依赖关系矩阵，如果为None则使用状态中的值
            completed_tasks: 已完成任务集合，如果为None则使用状态中的值
            num_tasks: 任务数量，如果为None则从adj_matrix推断

        返回:
            ready_tasks: 新的就绪任务列表
        """
        # 如果没有提供参数，使用状态中的值
        if adj_matrix is None:
            adj_matrix = self._step_state['adj_matrix']
        if completed_tasks is None:
            completed_tasks = self._step_state['completed_tasks']
        if num_tasks is None:
            num_tasks = adj_matrix.shape[0]

        # 获取当前状态信息
        ready_tasks = []
        failed_tasks = self._step_state.get('failed_tasks', set())

        # 检查每个任务
        for task_idx in range(num_tasks):
            # 跳过已完成或已失败的任务
            if task_idx in completed_tasks or task_idx in failed_tasks:
                continue

            # 跳过已在就绪队列或待执行队列中的任务
            if task_idx in self._step_state.get('ready_tasks', []) or task_idx in self._step_state.get('pending_tasks', set()):
                continue

            # 检查是否所有前置任务都已完成
            all_deps_completed = True
            for i in range(num_tasks):
                if adj_matrix[i, task_idx] > 0 and i not in completed_tasks:  # i是task_idx的前置任务
                    all_deps_completed = False
                    break

            if all_deps_completed:
                ready_tasks.append(task_idx)
                print(f"[UPDATE_READY] 任务 {task_idx} 加入就绪队列")  # 始终打印，不受debug标志影响

        # 如果是内部调用（没有提供参数），更新状态
        if adj_matrix is self._step_state['adj_matrix']:
            self._step_state['ready_tasks'].extend(ready_tasks)

        return ready_tasks

    def _update_task_status(self, task_idx, machine_idx, current_time, completion_time):
        """
        更新任务状态

        参数:
            task_idx: 任务索引
            machine_idx: 机器索引
            current_time: 当前时间
            completion_time: 任务完成时间
        """
        # 打印机器内存变化情况
        memory_req = self._step_state['task_features'][4, task_idx]
        previous_memory = self._step_state['memory_utilization'][machine_idx]
        new_memory = previous_memory + memory_req

        print(f"\n===== 分配任务内存变化 =====")
        print(f"任务 {task_idx} 分配给机器 {machine_idx}")
        print(f"任务内存需求: {memory_req:.2f}GB")
        print(f"机器 {machine_idx} 内存使用: {previous_memory:.2f}GB -> {new_memory:.2f}GB")
        print(f"机器 {machine_idx} 内存容量: {self._step_state['memory_status'][machine_idx]:.2f}GB")
        print("===========================\n")

        # 更新内存使用情况
        self._step_state['memory_utilization'][machine_idx] += memory_req

        # 更新任务状态
        self._step_state['task_start_time'][task_idx] = current_time
        self._step_state['task_finish_time'][task_idx] = completion_time
        self._step_state['task_machines'][task_idx] = machine_idx

        # 更新机器完成时间
        self._step_state['machine_finish_time'][machine_idx] = completion_time

        # 添加任务到运行中任务集合
        self._step_state['running_tasks'].add(task_idx)

    def compute_reward(self, task_idx, task_completion_time, memory_status, machine_idx, completed_tasks, num_tasks, is_llm):
        # 将毫秒转换为秒，便于更直观的处理
        completion_time_sec = task_completion_time /1000

        # 计算有效任务数（排除失败任务）
        failed_tasks = self._step_state['failed_tasks'] if self._step_state and 'failed_tasks' in self._step_state else set()
        valid_tasks = num_tasks - len(failed_tasks)

        # 获取任务内存需求
        memory_req = 0
        if is_llm == 1 and self._step_state and 'task_memory_usage' in self._step_state:
            memory_req = self._step_state['task_memory_usage'][task_idx, machine_idx]
            if memory_req == 0:  # 如果尚未记录内存使用
                # 从任务特征中获取内存需求
                for task_feature in self._step_state.get('task_features', []):
                    if len(task_feature) > 4 and task_feature[2] == 1:  # 是LLM任务
                        memory_req = task_feature[4]
                        break

        # 1. 时间奖励 - 使用更合适的缩放方式
        # 假设完成时间在0-100秒之间，使用非线性变换提高区分度
        time_reward = -completion_time_sec / 10.0  # 调整分母可以控制奖励幅度

        # 2. 内存奖励 - 优化LLM任务的内存使用
        memory_reward = 0
        if is_llm == 1:
            memory_limit = self.config.LOCAL_MEMORY_LIMIT if machine_idx == 0 else self.config.MEMORY_LIMIT
            memory_ratio = memory_status[machine_idx] / memory_limit

            # 为大模型分配到合适的设备给予额外奖励
            if machine_idx > 0 and memory_ratio > 0.2:  # 在边缘设备且还有足够内存
                memory_reward = 5.0
            elif machine_idx == 0 and memory_req > self.config.LOCAL_MEMORY_LIMIT * 0.8:
                # 本地设备处理接近其内存上限的任务，给予负奖励
                memory_reward = -5.0

        # 3. 进度奖励 - 更加强调完成率，考虑失败任务
        completion_ratio = len(completed_tasks) / max(1, valid_tasks) if valid_tasks > 0 else 0.0
        progress_reward = completion_ratio * 5.0

        # 4. 失败任务惩罚 - 对于有失败任务的情况给予一定惩罚
        failure_penalty = 0
        if len(failed_tasks) > 0:
            failure_ratio = len(failed_tasks) / num_tasks
            failure_penalty = -failure_ratio * 20.0  # 失败任务比例越高，惩罚越大

        # 5. 完成奖励 - 全部完成时的额外奖励
        # 只有当所有有效任务都完成时才给予奖励
        completion_bonus = 0
        if len(completed_tasks) == valid_tasks and valid_tasks > 0:
            completion_bonus = 50.0

        # 组合奖励
        reward = time_reward + memory_reward + progress_reward + failure_penalty

        # 当所有有效任务完成时，添加完成奖励
        if len(completed_tasks) == valid_tasks and valid_tasks > 0:
            reward += completion_bonus

        # 记录完成率到步骤状态
        self._step_state['completion_ratio'] = completion_ratio

        # 输出调试信息
        if self.debug:
            print(f"[REWARD] 任务{task_idx} - 时间奖励: {time_reward:.2f}, 内存奖励: {memory_reward:.2f}, "
                  f"进度奖励: {progress_reward:.2f}, 失败惩罚: {failure_penalty:.2f}, "
                  f"完成奖励: {completion_bonus:.2f}, 总奖励: {reward:.2f}")
            print(f"[STATS] 已完成任务: {len(completed_tasks)}/{valid_tasks}, 失败任务: {len(failed_tasks)}/{num_tasks}")

        # 合理裁剪
        return np.clip(reward, -10.0, 10.0)

    def _release_completed_tasks_memory(self, force_release=False, specific_task=None):
        """
        释放已完成任务的内存

        参数:
            force_release: 是否强制释放内存，即使任务完成时间还没到
            specific_task: 指定要释放内存的任务索引，如果为None则释放所有符合条件的任务内存

        返回:
            bool: 是否有内存被释放
        """
        current_time = self._step_state['current_time']
        machine_finish_time = self._step_state['task_finish_time']
        task_machines = self._step_state['task_machines']
        memory_utilization = self._step_state['memory_utilization']
        memory_status = self._step_state['memory_status']

        memory_released = False

        # 如果指定了特定任务，则只释放该任务的内存
        if specific_task is not None:
            task_indices = [specific_task]
        else:
            task_indices = range(len(machine_finish_time))

        for task_idx in task_indices:
            finish_time = machine_finish_time[task_idx] if task_idx < len(machine_finish_time) else 0
            # 如果任务已完成但尚未释放内存，或者强制释放，或者是指定的任务
            # 如果是强制释放或指定任务，则不检查running_tasks
            if ((finish_time <= current_time and finish_time > 0) or force_release or specific_task is not None) and \
               task_idx not in self._step_state['memory_released_tasks']:

                # 获取任务所在机器
                if isinstance(task_machines, dict):
                    machine_idx = task_machines.get(task_idx, -1)
                elif hasattr(task_machines, '__getitem__') and hasattr(task_machines, '__len__'):  # 数组类型检查
                    machine_idx = task_machines[task_idx] if task_idx < len(task_machines) else -1
                else:
                    print(f"[ERROR] task_machines类型错误: {type(task_machines)}")
                    machine_idx = -1

                # 如果机器索引无效，跳过此任务
                if machine_idx < 0 or machine_idx >= len(memory_status):
                    if self.debug:
                        print(f"[WARNING] 任务 {task_idx} 没有有效的机器分配，跳过内存释放")
                    continue

                # 获取任务内存需求
                memory_req = self._step_state['task_features'][4, task_idx]

                # 从任务内存使用矩阵中获取实际使用的内存
                actual_memory_used = self._step_state['task_memory_usage'][task_idx, machine_idx]

                # 如果任务没有实际使用内存，则使用预计的内存需求
                # 强制使用预计的内存需求，确保内存被正确释放
                if actual_memory_used <= 0:
                    actual_memory_used = memory_req
                    if self.debug:
                        print(f"[MEMORY] 任务 {task_idx} 使用预计内存需求 {memory_req:.2f}GB 进行释放")

                # 如果是强制释放，确保使用预计的内存需求
                if force_release:
                    actual_memory_used = memory_req
                    if self.debug:
                        print(f"[MEMORY] 强制释放任务 {task_idx} 的内存，使用预计内存需求 {memory_req:.2f}GB")

                # 释放内存前的使用量
                previous_memory = memory_utilization[machine_idx]

                # 检查内存使用量是否足够
                if previous_memory < actual_memory_used:
                    print(f"[WARNING] 机器 {machine_idx} 的内存使用量({previous_memory:.2f}GB)小于要释放的内存({actual_memory_used:.2f}GB)")
                    # 调整释放的内存量，避免负值
                    actual_memory_used = previous_memory

                new_memory = previous_memory - actual_memory_used

                # 更新可用内存
                memory_status[machine_idx] += actual_memory_used
                memory_utilization[machine_idx] = new_memory

                # 重置任务内存使用
                self._step_state['task_memory_usage'][task_idx, machine_idx] = 0

                # 打印内存释放信息
                print(f"\n===== 释放任务内存变化 =====")
                print(f"任务 {task_idx} 在机器 {machine_idx} 上完成")
                print(f"释放内存: {actual_memory_used:.2f}GB")
                print(f"机器 {machine_idx} 内存使用: {previous_memory:.2f}GB -> {new_memory:.2f}GB")
                print(f"机器 {machine_idx} 可用内存: {memory_status[machine_idx]:.2f}GB")
                print(f"任务内存使用矩阵值: {self._step_state['task_memory_usage'][task_idx, machine_idx]:.2f}GB")
                print("===========================\n")

                # 内存使用情况已在前面更新
                # memory_utilization[machine_idx] = new_memory

                # 标记任务已释放内存
                self._step_state['memory_released_tasks'].add(task_idx)

                # 如果任务在运行中，从运行任务列表中移除
                if task_idx in self._step_state['running_tasks']:
                    self._step_state['running_tasks'].remove(task_idx)

                memory_released = True

                # 如果是强制释放，打印额外信息
                if force_release and finish_time > current_time:
                    print(f"[DEBUG] 强制释放任务 {task_idx} 的内存，该任务原定完成时间: {finish_time}，当前时间: {current_time}")

        # 如果有内存被释放，更新就绪任务列表
        if memory_released:
            self._update_ready_tasks()

        return memory_released

    def dag_step(self, adj_matrix, task_features, actions, num_tasks, machine_resources=None, comm_speed=None, memory_status=None, use_sequence=False, debug=True):
        """
        简化版的DAG调度方法，让RL代理承担更多责任

        参数:
            adj_matrix: 任务依赖关系矩阵
            task_features: 任务特征
            actions: 所有任务的机器分配数组，每个任务一个连续动作值（范围为-1到1）
            num_tasks: 任务数量
            machine_resources: 可选，机器资源数组，如果为None则从缓存或文件加载
            comm_speed: 可选，通信速度矩阵，如果为None则从缓存或文件加载
            memory_status: 可选，机器内存状态数组，如果为None则从缓存或文件加载
            use_sequence: 是否返回序列化状态
            debug: 是否打印调试信息

        返回:
            next_state: 下一个状态
            reward: 奖励值
            done: 是否完成
            info: 包含完成时间、资源使用等信息的字典
        """
        # 1. 初始化阶段
        # 检查是否是第一次调用step
        if self._step_state is None:
            # 初始化步骤状态
            self._init_step_state(task_features, adj_matrix, machine_resources, comm_speed, memory_status)

        # 使用传入的调试标志或实例标志
        debug = debug or self.debug
        if debug:
            print(f"[DEBUG] DAG批量调度开始，共{num_tasks}个任务")

        # 获取当前状态
        memory_status = self._step_state['memory_status']

        # 从当前状态中提取变量
        completed_tasks = self._step_state.get('completed_tasks', set())
        machine_finish_time = self._step_state.get('machine_finish_time', np.zeros(self.config.NUM_EDGE_SERVERS + 1))
        memory_utilization = self._step_state.get('memory_utilization', np.zeros(self.config.NUM_EDGE_SERVERS + 1))

        # 2. Agent的任务分配
        # 直接使用Agent提供的actions，将每个任务映射到一个机器
        machine_assignments = {}  # 存储任务到机器的映射
        num_machines = machine_resources.shape[0]

        for task_idx in range(num_tasks):
            # 将动作值从[-1,1]映射到[0,num_machines-1]
            if task_idx < len(actions):
                action_value = actions[task_idx]
                machine_idx = int(round(((action_value + 1) / 2) * (num_machines - 1)))
                machine_idx = max(0, min(machine_idx, num_machines - 1))  # 确保索引在有效范围内

                # 如果模型输出的机器索引超出范围，将其映射到有效范围内
                if machine_idx >= num_machines:
                    if debug:
                        print(f"[WARNING] 任务{task_idx}的机器索引{machine_idx}超出范围，将被映射到机器{machine_idx % num_machines}")
                    machine_idx = machine_idx % num_machines

                machine_assignments[task_idx] = machine_idx

                # 更新任务机器字典
                self._step_state['task_machines'][task_idx] = machine_idx

        # 初始化状态变量
        # 确保内存使用量初始化为0
        self._step_state['memory_utilization'] = np.zeros(num_machines)

        # 初始化任务内存使用矩阵
        self._step_state['task_memory_usage'] = np.zeros((num_tasks, num_machines))

        # 打印初始机器内存状态
        if debug:
            print("\n===== 初始机器内存状态 ===== ")
            for i in range(num_machines):
                # 使用实际的内存状态，而不是配置中的固定值
                total_memory = memory_status[i]
                print(f"机器 {i}: 总内存 {total_memory:.2f}GB, 已使用 {memory_utilization[i]:.2f}GB")
            print("===========================")

        # 打印机器分配情况
        if debug:
            print("\n===== 机器分配情况 ===== ")
            for task_idx in range(num_tasks):
                if task_idx in machine_assignments:
                    machine_idx = machine_assignments[task_idx]
                    action_value = actions[task_idx] if task_idx < len(actions) else 0.0
                    print(f"[ASSIGN] 任务{task_idx} 分配到机器{machine_idx} (动作值: {action_value:.3f})")
            print("===========================")

        # 3. 初始队列构建
        # 初始化就绪队列 - 找出初始就绪任务（没有依赖的任务）
        ready_queue = []
        for task_idx in range(num_tasks):
            has_dependencies = False
            for i in range(num_tasks):
                if adj_matrix[i, task_idx] > 0:  # task_idx依赖于i
                    has_dependencies = True
                    break
            if not has_dependencies:
                ready_queue.append(task_idx)
                if debug:
                    print(f"[INIT_READY] 任务{task_idx}加入初始就绪队列")

        # 初始化待执行队列
        pending_tasks = set()

        # 更新状态
        self._step_state['ready_tasks'] = ready_queue
        self._step_state['pending_tasks'] = pending_tasks

        # 4. 主循环执行阶段
        # 设置一个合理的死锁检测阈值
        MAX_DEADLOCK_ATTEMPTS = num_tasks * 3  # 增加到任务数的3倍，减少误判
        self._step_state['loop_counter'] = 0

        # 记录事件奖励
        self._step_state['event_rewards'] = []

        while len(completed_tasks) + len(self._step_state['failed_tasks']) < num_tasks:
            progress_made_this_iteration = False
            current_event_rewards = 0

            # A. 尝试执行就绪队列中的任务
            for task_idx in list(ready_queue):  # 使用list创建副本以避免在迭代时修改
                # 跳过已完成或已失败的任务
                if task_idx in completed_tasks or task_idx in self._step_state['failed_tasks']:
                    ready_queue.remove(task_idx)
                    continue

                # 获取Agent为此任务分配的机器
                assigned_machine_idx = machine_assignments.get(task_idx, -1)
                if assigned_machine_idx == -1:
                    if debug:
                        print(f"[ERROR] 任务{task_idx}没有分配机器")
                    continue

                # 获取任务内存需求
                task_memory_req = task_features[4, task_idx]

                # 检查机器总内存是否足够
                machine_total_memory = memory_status[assigned_machine_idx]

                if task_memory_req > machine_total_memory:
                    # 该任务在此机器上永远不可能执行
                    self._step_state['failed_tasks'].add(task_idx)
                    ready_queue.remove(task_idx)
                    current_event_rewards += PENALTY_TASK_FAIL_BAD_ASSIGNMENT
                    progress_made_this_iteration = True

                    if debug:
                        print(f"[FAIL] 任务{task_idx}因分配到内存不足的机器{assigned_machine_idx}而失败")
                        print(f"       需要{task_memory_req:.2f}GB，但机器总内存只有{machine_total_memory:.2f}GB")

                    continue

                # 检查当前可用内存是否足够
                if memory_status[assigned_machine_idx] >= task_memory_req:
                    # 可以执行
                    progress_made_this_iteration = True

                    # 计算前置任务完成时间
                    pred_finish_time = 0
                    for i in range(num_tasks):
                        if adj_matrix[i, task_idx] > 0:  # i是task_idx的前置任务
                            if i in completed_tasks:
                                pred_finish_time = max(pred_finish_time, self._step_state['task_finish_time'][i])
                            else:
                                if debug:
                                    print(f"[WARNING] 任务{task_idx}的前置任务{i}尚未完成，但仍尝试执行")

                    # 计算任务开始时间
                    start_time = max(pred_finish_time, machine_finish_time[assigned_machine_idx])

                    # 获取任务信息
                    cpu_cycles = task_features[0, task_idx]
                    data_size = task_features[1, task_idx]
                    is_llm = task_features[2, task_idx]
                    token_count = task_features[3, task_idx]

                    # 计算执行时间
                    if assigned_machine_idx == 0:  # 本地执行
                        # 本地CPU频率
                        cpu_freq = self.config.LOCAL_CPU_FREQ  # 单位: GHz

                        # 计算纯执行时间 (秒)
                        execution_time_sec = cpu_cycles / (cpu_freq * 1e9)

                        # 对于LLM模型，考虑token生成时间，使用本地设备的异构参数
                        if is_llm == 1:
                            # 使用本地设备的token_per_second参数
                            token_time_sec = token_count * self.machine_token_per_second[0]
                            # 使用本地设备的base_execution_time参数
                            base_time_sec = self.machine_base_execution_time[0]
                            execution_time_sec = (base_time_sec + token_time_sec) * self.config.LOCAL_PENALTY

                        # 转换为毫秒用于内部计算
                        execution_time = execution_time_sec * 1000

                        # 计算任务完成时间
                        finish_time = start_time + execution_time

                    else:  # 边缘执行
                        # 边缘CPU频率
                        cpu_freq = machine_resources[assigned_machine_idx]  # 单位: GHz

                        # 计算上传时间 (秒)
                        upload_speed = comm_speed[0, assigned_machine_idx]  # 从用户设备(0)到目标机器的通信速度
                        upload_time_sec = data_size / upload_speed if upload_speed > 0 else 0
                        upload_time = upload_time_sec * 1000  # 转换为毫秒

                        # 计算纯执行时间 (秒)
                        execution_time_sec = cpu_cycles / (cpu_freq * 1e9)

                        # 对于LLM模型，考虑token生成时间，使用边缘设备的异构参数
                        if is_llm == 1:
                            # 使用边缘设备的token_per_second参数
                            token_time_sec = token_count * self.machine_token_per_second[assigned_machine_idx]
                            # 使用边缘设备的base_execution_time参数
                            base_time_sec = self.machine_base_execution_time[assigned_machine_idx]
                            execution_time_sec = base_time_sec + token_time_sec

                        # 转换为毫秒用于内部计算
                        execution_time = execution_time_sec * 1000

                        # 计算下载时间 (秒)
                        download_speed = comm_speed[assigned_machine_idx, 0]  # 从目标机器到用户设备(0)的通信速度
                        download_time_sec = data_size / download_speed if download_speed > 0 else 0
                        download_time = download_time_sec * 1000  # 转换为毫秒

                        # 计算任务完成时间
                        finish_time = start_time + upload_time + execution_time + download_time

                    # 分配内存
                    memory_status[assigned_machine_idx] -= task_memory_req
                    memory_utilization[assigned_machine_idx] += task_memory_req
                    self._step_state['task_memory_usage'][task_idx, assigned_machine_idx] = task_memory_req

                    # 更新机器完成时间
                    machine_finish_time[assigned_machine_idx] = finish_time

                    # 更新任务完成时间
                    self._step_state['task_finish_time'][task_idx] = finish_time

                    # 标记任务为已完成
                    completed_tasks.add(task_idx)
                    ready_queue.remove(task_idx)

                    # 更新总完成时间
                    self._step_state['total_completion_time'] = max(self._step_state['total_completion_time'], finish_time)

                    # 添加任务完成奖励
                    current_event_rewards += REWARD_TASK_COMPLETED

                    # 添加时间惩罚
                    time_penalty = self.calculate_time_penalty(start_time, finish_time, task_idx)
                    current_event_rewards -= time_penalty

                    if debug:
                        print(f"[COMPLETE] 任务{task_idx}在机器{assigned_machine_idx}上完成，完成时间: {finish_time/1000:.3f}秒")
                        print(f"          奖励: +{REWARD_TASK_COMPLETED:.2f}，时间惩罚: -{time_penalty:.2f}")

                    # 立即释放内存
                    memory_status[assigned_machine_idx] += task_memory_req
                    memory_utilization[assigned_machine_idx] -= task_memory_req

                    if debug:
                        print(f"[MEMORY] 任务{task_idx}完成后释放内存{task_memory_req:.2f}GB，机器{assigned_machine_idx}可用内存: {memory_status[assigned_machine_idx]:.2f}GB")

                        # 更新就绪队列 - 检查是否有新的任务可以执行
                    print(f"[DEBUG] 任务{task_idx}完成后，检查新的就绪任务")
                    print(f"[DEBUG] 当前已完成任务: {completed_tasks}")

                    new_ready_tasks = self._update_ready_tasks(adj_matrix, completed_tasks, num_tasks)
                    print(f"[DEBUG] 找到新的就绪任务: {new_ready_tasks}")

                    for new_task in new_ready_tasks:
                        if new_task not in ready_queue and new_task not in pending_tasks:
                            ready_queue.append(new_task)
                            print(f"[NEW_READY] 任务{new_task}加入就绪队列")

                    print(f"[DEBUG] 更新后的就绪队列: {ready_queue}")
                    print(f"[DEBUG] 待执行队列: {pending_tasks}")

                else:
                    # 内存不足，将任务移到待执行队列
                    progress_made_this_iteration = True
                    ready_queue.remove(task_idx)
                    pending_tasks.add(task_idx)
                    current_event_rewards += PENALTY_TASK_TO_PENDING

                    if debug:
                        print(f"[READY->PENDING] 任务{task_idx}因内存不足({task_memory_req:.2f}GB > {memory_status[assigned_machine_idx]:.2f}GB)移至待执行队列")
                        print(f"                惩罚: {PENALTY_TASK_TO_PENDING:.2f}")

            # B. 尝试调度等待队列中的任务
            for task_idx in list(pending_tasks):  # 使用list创建副本以避免在迭代时修改
                # 获取Agent为此任务分配的机器
                assigned_machine_idx = machine_assignments.get(task_idx, -1)
                if assigned_machine_idx == -1:
                    if debug:
                        print(f"[ERROR] 待执行任务{task_idx}没有分配机器")
                    continue

                # 获取任务内存需求
                task_memory_req = task_features[4, task_idx]

                # 检查当前可用内存是否足够
                if memory_status[assigned_machine_idx] >= task_memory_req:
                    # 可以执行，将任务移回就绪队列
                    progress_made_this_iteration = True
                    pending_tasks.remove(task_idx)
                    ready_queue.append(task_idx)
                    current_event_rewards += REWARD_TASK_LEAVES_PENDING

                    if debug:
                        print(f"[PENDING->READY] 任务{task_idx}因内存足够({task_memory_req:.2f}GB <= {memory_status[assigned_machine_idx]:.2f}GB)移回就绪队列")
                        print(f"                奖励: +{REWARD_TASK_LEAVES_PENDING:.2f}")

            # C. 死锁检测
            if not progress_made_this_iteration:
                self._step_state['loop_counter'] += 1

                if debug:
                    print(f"[DEBUG] 本轮未执行任务且未释放内存，循环计数: {self._step_state['loop_counter']}/{MAX_DEADLOCK_ATTEMPTS}")
                    print(f"[DEBUG] 就绪队列: {ready_queue}, 待执行队列: {pending_tasks}")

                if self._step_state['loop_counter'] >= MAX_DEADLOCK_ATTEMPTS:
                    # 检测到死锁
                    if debug:
                        print(f"[DEADLOCK] 检测到死锁: {len(pending_tasks)}个任务等待内存, {len(ready_queue)}个任务在就绪队列")
                        print(f"[DEADLOCK] 待执行任务: {pending_tasks}")
                        print(f"[DEADLOCK] 就绪任务: {ready_queue}")

                    # 将所有在就绪队列和待执行队列中的任务标记为失败
                    for task_idx in list(ready_queue) + list(pending_tasks):
                        self._step_state['failed_tasks'].add(task_idx)
                        if debug:
                            print(f"[FAIL] 任务{task_idx}因死锁被标记为失败")

                    # 清空队列
                    ready_queue.clear()
                    pending_tasks.clear()

                    # 标记死锁
                    self._step_state['deadlock_detected'] = True
                    self._step_state['dag_failed'] = True

                    # 添加死锁惩罚
                    current_event_rewards += PENALTY_DEADLOCK

                    # 跳出主循环
                    break
            else:
                # 有进展，重置循环计数器
                self._step_state['loop_counter'] = 0

            # 累积事件奖励
            self._step_state['event_rewards'].append(current_event_rewards)
            self._step_state['total_reward'] += current_event_rewards

            # 如果没有待处理任务和就绪任务，检查是否所有任务都已完成或失败
            if not pending_tasks and not ready_queue:
                remaining_tasks = num_tasks - len(completed_tasks) - len(self._step_state['failed_tasks'])
                if remaining_tasks <= 0:
                    # 没有剩余任务，正常结束
                    break

        # 5. 收尾阶段
        # 更新状态
        self._step_state['completed_tasks'] = completed_tasks
        self._step_state['ready_tasks'] = ready_queue
        self._step_state['pending_tasks'] = pending_tasks

        # 计算完成率
        completion_ratio = len(completed_tasks) / num_tasks
        self._step_state['completion_ratio'] = completion_ratio

        # 计算最终奖励
        final_reward = 0

        # 根据完成情况计算最终奖励
        if len(completed_tasks) == num_tasks:
            # 所有任务都成功完成
            # 完成奖励
            completion_bonus = REWARD_COMPLETION_BONUS

            # makespan惩罚
            makespan_penalty = -self._step_state['total_completion_time'] * PENALTY_TIME_FACTOR

            # 最终奖励
            final_bonus = completion_bonus + makespan_penalty
            final_reward += final_bonus

            if debug:
                print(f"[REWARD] 所有任务完成，完成奖励: {completion_bonus:.2f}，makespan惩罚: {makespan_penalty:.2f}，最终奖励: {final_bonus:.2f}")
        elif self._step_state.get('deadlock_detected', False):
            # 死锁情况下，确保总奖励不会高于成功完成的情况
            # 已经在检测到死锁时添加了 PENALTY_DEADLOCK，这里不需要额外惩罚
            if debug:
                print(f"[REWARD] 检测到死锁，已添加死锁惩罚: {PENALTY_DEADLOCK:.2f}")
        else:
            # 部分完成的情况
            # 根据完成率给予部分奖励
            completion_ratio = len(completed_tasks) / num_tasks
            partial_bonus = REWARD_COMPLETION_BONUS * completion_ratio * 0.5  # 只给予一半的比例奖励
            final_reward += partial_bonus

            if debug:
                print(f"[REWARD] 部分完成 ({completion_ratio*100:.1f}%)，部分奖励: {partial_bonus:.2f}")

        # 累积最终奖励
        self._step_state['total_reward'] += final_reward

        # 修复未完成DAG的完成时间计算
        total_time = self._step_state['total_completion_time']
        if len(completed_tasks) < num_tasks:
            # 只有当完成率低于100%时才设置较大的完成时间
            total_time = 1000000  # 1000秒，单位是毫秒
            if debug:
                print(f"[TIME] DAG未完全完成，设置总完成时间为1000秒")

        # 获取下一个状态
        next_state = self._get_current_state(adj_matrix, task_features, num_tasks, machine_resources, comm_speed, memory_status, use_sequence)

        # 一次性调度总是完成的，无论是正常完成还是失败
        done = True

        # 返回结果
        return next_state, self._step_state['total_reward'], done, {
            "completed_tasks": completed_tasks,
            "total_time": total_time,
            "total_reward": self._step_state['total_reward'],
            "dag_failed": self._step_state['dag_failed'],
            "deadlock_detected": self._step_state['deadlock_detected'],
            "event_rewards": self._step_state['event_rewards']
        }

    def _force_release_memory(self):
        """
        处理内存不足的情况，记录相关信息但不进行强制释放

        返回:
            bool: 是否成功处理
        """
        # 记录当前内存状态
        current_memory = self._step_state['memory_status'].copy()

        # 记录内存不足的机器
        low_memory_machines = [i for i, mem in enumerate(current_memory) if mem < 0.1]  # 小于0.1GB视为内存不足

        if low_memory_machines:
            print(f"警告：机器 {low_memory_machines} 内存不足")
            return False

        return True

    def _can_proceed_with_memory(self, *args, **kwargs):
        """
        此方法已被废弃，不再使用。为保持接口兼容性而保留，始终返回False。
        使用*args和**kwargs来避免未使用变量的警告。
        """
        # 使用参数以避免未使用变量的警告
        _ = len(args)  # 使用args
        _ = len(kwargs)  # 使用kwargs
        print("警告: _can_proceed_with_memory 方法已废弃，不再使用")
        return False

    def step_task(self, action, adj_matrix=None, task_features=None, num_tasks=None, machine_resources=None, comm_speed=None, memory_status=None, use_sequence=False, debug=True):
        """
        逐任务调度方法，每次只调度一个任务

        参数:
            action: 机器分配动作，范围为[-1,1]
            adj_matrix: 任务依赖关系矩阵，如果为None则使用状态中的值
            task_features: 任务特征，如果为None则使用状态中的值
            num_tasks: 任务数量，如果为None则从adj_matrix推断
            machine_resources: 可选，机器资源数组，如果为None则从缓存或文件加载
            comm_speed: 可选，通信速度矩阵，如果为None则从缓存或文件加载
            memory_status: 可选，机器内存状态数组，如果为None则从缓存或文件加载
            use_sequence: 是否返回序列化状态
            debug: 是否打印调试信息

        返回:
            next_state: 下一个状态
            reward: 奖励值
            done: 是否完成
            info: 包含完成时间、资源使用等信息的字典
        """
        # 1. 初始化阶段
        # 检查是否是第一次调用step
        if self._step_state is None:
            # 确保参数不为None
            if adj_matrix is None or task_features is None:
                raise ValueError("首次调用step_task时，adj_matrix和task_features不能为None")

            # 如果num_tasks为None，从adj_matrix推断
            if num_tasks is None:
                num_tasks = adj_matrix.shape[0]

            # 初始化步骤状态
            self._init_step_state(task_features, adj_matrix, machine_resources, comm_speed, memory_status)

            # 初始化就绪队列
            self._update_ready_tasks(adj_matrix, set(), num_tasks)

            # 如果就绪队列为空，返回错误
            if not self._step_state['ready_tasks']:
                raise ValueError("初始化后就绪队列为空，无法调度任务")

        # 使用传入的调试标志或实例标志
        debug = debug or self.debug

        # 获取当前状态
        adj_matrix = self._step_state['adj_matrix'] if adj_matrix is None else adj_matrix
        task_features = self._step_state['task_features'] if task_features is None else task_features
        num_tasks = adj_matrix.shape[0] if num_tasks is None else num_tasks
        memory_status = self._step_state['memory_status'] if memory_status is None else memory_status
        machine_resources = self._step_state['machine_resources'] if machine_resources is None else machine_resources
        comm_speed = self._step_state['comm_speed'] if comm_speed is None else comm_speed

        # 从当前状态中提取变量
        completed_tasks = self._step_state.get('completed_tasks', set())
        failed_tasks = self._step_state.get('failed_tasks', set())
        ready_queue = self._step_state.get('ready_tasks', [])
        pending_tasks = self._step_state.get('pending_tasks', set())
        machine_finish_time = self._step_state.get('machine_finish_time', np.zeros(self.config.NUM_EDGE_SERVERS + 1))
        memory_utilization = self._step_state.get('memory_utilization', np.zeros(self.config.NUM_EDGE_SERVERS + 1))

        # 2. 检查是否已完成
        if len(completed_tasks) + len(failed_tasks) >= num_tasks:
            if debug:
                print(f"[DEBUG] 所有任务已完成或失败，无需进一步调度")
                print(f"[DEBUG] 已完成任务: {len(completed_tasks)}/{num_tasks}, 失败任务: {len(failed_tasks)}/{num_tasks}")

            # 获取下一个状态
            next_state = self._get_current_state(adj_matrix, task_features, num_tasks, machine_resources, comm_speed, memory_status, use_sequence)

            # 返回结果
            return next_state, 0, True, {
                "completed_tasks": completed_tasks,
                "total_time": self._step_state['total_completion_time'],
                "total_reward": self._step_state['total_reward'],
                "dag_failed": self._step_state['dag_failed'],
                "deadlock_detected": self._step_state['deadlock_detected'],
                "event_rewards": self._step_state['event_rewards']
            }

        # 3. 检查就绪队列是否为空
        if not ready_queue and not pending_tasks:
            if debug:
                print(f"[DEBUG] 就绪队列和待执行队列为空，但仍有任务未完成，可能存在死锁")

            # 标记死锁
            self._step_state['deadlock_detected'] = True
            self._step_state['dag_failed'] = True

            # 将所有未完成的任务标记为失败
            for task_idx in range(num_tasks):
                if task_idx not in completed_tasks and task_idx not in failed_tasks:
                    failed_tasks.add(task_idx)
                    if debug:
                        print(f"[FAIL] 任务{task_idx}因死锁被标记为失败")

            # 添加死锁惩罚
            self._step_state['total_reward'] += PENALTY_DEADLOCK

            # 获取下一个状态
            next_state = self._get_current_state(adj_matrix, task_features, num_tasks, machine_resources, comm_speed, memory_status, use_sequence)

            # 返回结果
            return next_state, PENALTY_DEADLOCK, True, {
                "completed_tasks": completed_tasks,
                "total_time": self._step_state['total_completion_time'],
                "total_reward": self._step_state['total_reward'],
                "dag_failed": True,
                "deadlock_detected": True,
                "event_rewards": self._step_state['event_rewards']
            }

        # 4. 从就绪队列中选择一个任务进行调度
        task_idx = None
        if ready_queue:
            task_idx = ready_queue[0]  # 选择就绪队列中的第一个任务
        elif pending_tasks:
            # 如果就绪队列为空但待执行队列不为空，尝试从待执行队列中选择一个任务
            task_idx = list(pending_tasks)[0]

            # 检查是否有足够的内存
            machine_idx = self._convert_action_to_machine_idx(action, machine_resources.shape[0])
            task_memory_req = task_features[4, task_idx]

            if memory_status[machine_idx] >= task_memory_req:
                # 可以执行，将任务移回就绪队列
                pending_tasks.remove(task_idx)
                ready_queue.append(task_idx)

                # 添加奖励
                self._step_state['total_reward'] += REWARD_TASK_LEAVES_PENDING

                if debug:
                    print(f"[PENDING->READY] 任务{task_idx}因内存足够({task_memory_req:.2f}GB <= {memory_status[machine_idx]:.2f}GB)移回就绪队列")
                    print(f"                奖励: +{REWARD_TASK_LEAVES_PENDING:.2f}")
            else:
                # 内存不足，无法执行
                if debug:
                    print(f"[DEBUG] 待执行任务{task_idx}内存不足({task_memory_req:.2f}GB > {memory_status[machine_idx]:.2f}GB)，无法执行")

                # 获取下一个状态
                next_state = self._get_current_state(adj_matrix, task_features, num_tasks, machine_resources, comm_speed, memory_status, use_sequence)

                # 返回结果
                return next_state, 0, False, {
                    "completed_tasks": completed_tasks,
                    "total_time": self._step_state['total_completion_time'],
                    "total_reward": self._step_state['total_reward'],
                    "dag_failed": self._step_state['dag_failed'],
                    "deadlock_detected": self._step_state['deadlock_detected'],
                    "event_rewards": self._step_state['event_rewards']
                }

        # 5. 执行任务
        # 将动作转换为机器索引
        machine_idx = self._convert_action_to_machine_idx(action, machine_resources.shape[0])

        # 获取任务内存需求
        task_memory_req = task_features[4, task_idx]

        # 检查机器总内存是否足够
        machine_total_memory = memory_status[machine_idx]

        if task_memory_req > machine_total_memory:
            # 该任务在此机器上永远不可能执行
            failed_tasks.add(task_idx)
            ready_queue.remove(task_idx)

            # 添加惩罚
            reward = PENALTY_TASK_FAIL_BAD_ASSIGNMENT
            self._step_state['total_reward'] += reward

            if debug:
                print(f"[FAIL] 任务{task_idx}因分配到内存不足的机器{machine_idx}而失败")
                print(f"       需要{task_memory_req:.2f}GB，但机器总内存只有{machine_total_memory:.2f}GB")

            # 获取下一个状态
            next_state = self._get_current_state(adj_matrix, task_features, num_tasks, machine_resources, comm_speed, memory_status, use_sequence)

            # 返回结果
            return next_state, reward, False, {
                "completed_tasks": completed_tasks,
                "total_time": self._step_state['total_completion_time'],
                "total_reward": self._step_state['total_reward'],
                "dag_failed": self._step_state['dag_failed'],
                "deadlock_detected": self._step_state['deadlock_detected'],
                "event_rewards": self._step_state['event_rewards']
            }

        # 检查当前可用内存是否足够
        if memory_status[machine_idx] >= task_memory_req:
            # 可以执行
            # 在任务执行开始时捕获内存状态，而不是在完成时
            # 记录当前内存状态，用于后续计算奖励
            current_memory_status = memory_status.copy()

            # 计算前置任务完成时间
            pred_finish_time = 0
            for i in range(num_tasks):
                if adj_matrix[i, task_idx] > 0:  # i是task_idx的前置任务
                    if i in completed_tasks:
                        pred_finish_time = max(pred_finish_time, self._step_state['task_finish_time'][i])
                    else:
                        if debug:
                            print(f"[WARNING] 任务{task_idx}的前置任务{i}尚未完成，但仍尝试执行")

            # 计算任务开始时间
            start_time = max(pred_finish_time, machine_finish_time[machine_idx])

            # 获取任务信息
            cpu_cycles = task_features[0, task_idx]
            data_size = task_features[1, task_idx]
            is_llm = task_features[2, task_idx]
            token_count = task_features[3, task_idx]

            # 计算执行时间
            if machine_idx == 0:  # 本地执行
                # 本地CPU频率
                cpu_freq = self.config.LOCAL_CPU_FREQ  # 单位: GHz

                # 计算纯执行时间 (秒)
                execution_time_sec = cpu_cycles / (cpu_freq * 1e9)

                # 对于LLM模型，考虑token生成时间，使用本地设备的异构参数
                if is_llm == 1:
                    # 使用本地设备的token_per_second参数
                    token_time_sec = token_count * self.token_per_second[0]
                    # 使用本地设备的base_execution_time参数
                    base_time_sec = self.base_execution_time[0]
                    execution_time_sec = (base_time_sec + token_time_sec) * self.config.LOCAL_PENALTY

                # 转换为毫秒用于内部计算
                execution_time = execution_time_sec * 1000

                # 计算任务完成时间
                finish_time = start_time + execution_time

                # 记录上传、执行和下载时间
                self._step_state['FT_ud'][task_idx] = start_time
                self._step_state['FT_up'][task_idx] = start_time
                self._step_state['FT_ec'][task_idx] = finish_time
                self._step_state['FT_do'][task_idx] = finish_time

            else:  # 边缘执行
                # 边缘CPU频率
                cpu_freq = machine_resources[machine_idx]  # 单位: GHz

                # 计算上传时间 (秒)
                upload_speed = comm_speed[0, machine_idx]  # 从用户设备(0)到目标机器的通信速度
                upload_time_sec = data_size / upload_speed if upload_speed > 0 else 0
                upload_time = upload_time_sec * 1000  # 转换为毫秒

                # 计算纯执行时间 (秒)
                execution_time_sec = cpu_cycles / (cpu_freq * 1e9)

                # 对于LLM模型，考虑token生成时间，使用边缘设备的异构参数
                if is_llm == 1:
                    # 使用边缘设备的token_per_second参数
                    token_time_sec = token_count * self.token_per_second[machine_idx]
                    # 使用边缘设备的base_execution_time参数
                    base_time_sec = self.base_execution_time[machine_idx]
                    execution_time_sec = base_time_sec + token_time_sec

                # 转换为毫秒用于内部计算
                execution_time = execution_time_sec * 1000

                # 计算下载时间 (秒)
                download_speed = comm_speed[machine_idx, 0]  # 从目标机器到用户设备(0)的通信速度
                download_time_sec = data_size / download_speed if download_speed > 0 else 0
                download_time = download_time_sec * 1000  # 转换为毫秒

                # 计算任务完成时间
                finish_time = start_time + upload_time + execution_time + download_time

                # 记录上传、执行和下载时间
                self._step_state['FT_ud'][task_idx] = start_time
                self._step_state['FT_up'][task_idx] = start_time + upload_time
                self._step_state['FT_ec'][task_idx] = start_time + upload_time + execution_time
                self._step_state['FT_do'][task_idx] = finish_time

            # 分配内存
            memory_status[machine_idx] -= task_memory_req
            memory_utilization[machine_idx] += task_memory_req
            self._step_state['task_memory_usage'][task_idx, machine_idx] = task_memory_req

            # 更新机器完成时间
            machine_finish_time[machine_idx] = finish_time

            # 更新任务完成时间
            self._step_state['task_finish_time'][task_idx] = finish_time

            # 标记任务为已完成
            completed_tasks.add(task_idx)
            ready_queue.remove(task_idx)

            # 更新总完成时间
            self._step_state['total_completion_time'] = max(self._step_state['total_completion_time'], finish_time)

            # 添加任务完成奖励
            reward = REWARD_TASK_COMPLETED

            # 添加时间惩罚
            time_penalty = self.calculate_time_penalty(start_time, finish_time, task_idx)
            reward -= time_penalty

            # 更新总奖励
            self._step_state['total_reward'] += reward

            if debug:
                print(f"[COMPLETE] 任务{task_idx}在机器{machine_idx}上完成，完成时间: {finish_time/1000:.3f}秒")
                print(f"          奖励: +{REWARD_TASK_COMPLETED:.2f}，时间惩罚: -{time_penalty:.2f}")

            # 立即释放内存
            memory_status[machine_idx] += task_memory_req
            memory_utilization[machine_idx] -= task_memory_req

            if debug:
                print(f"[MEMORY] 任务{task_idx}完成后释放内存{task_memory_req:.2f}GB，机器{machine_idx}可用内存: {memory_status[machine_idx]:.2f}GB")

            # 更新就绪队列 - 检查是否有新的任务可以执行
            if debug:
                print(f"[DEBUG] 任务{task_idx}完成后，检查新的就绪任务")
                print(f"[DEBUG] 当前已完成任务: {completed_tasks}")

            new_ready_tasks = self._update_ready_tasks(adj_matrix, completed_tasks, num_tasks)

            if debug:
                print(f"[DEBUG] 找到新的就绪任务: {new_ready_tasks}")

            for new_task in new_ready_tasks:
                if new_task not in ready_queue and new_task not in pending_tasks:
                    ready_queue.append(new_task)
                    if debug:
                        print(f"[NEW_READY] 任务{new_task}加入就绪队列")

            if debug:
                print(f"[DEBUG] 更新后的就绪队列: {ready_queue}")
                print(f"[DEBUG] 待执行队列: {pending_tasks}")

            # 检查是否所有任务都已完成
            done = len(completed_tasks) + len(failed_tasks) >= num_tasks

            # 如果所有任务都已完成，添加完成奖励
            if done:
                if len(completed_tasks) == num_tasks:
                    # 完成奖励 - 所有任务都成功完成
                    completion_bonus = REWARD_COMPLETION_BONUS

                    # makespan惩罚
                    makespan_penalty = -self._step_state['total_completion_time'] * PENALTY_TIME_FACTOR

                    # 最终奖励
                    final_bonus = completion_bonus + makespan_penalty
                    reward += final_bonus

                    # 更新总奖励
                    self._step_state['total_reward'] += final_bonus

                    if debug:
                        print(f"[REWARD] 所有任务完成，完成奖励: {completion_bonus:.2f}，makespan惩罚: {makespan_penalty:.2f}，最终奖励: {final_bonus:.2f}")
                elif self._step_state.get('deadlock_detected', False):
                    # 死锁情况下，确保总奖励不会高于成功完成的情况
                    # 已经在检测到死锁时添加了 PENALTY_DEADLOCK，这里不需要额外惩罚
                    if debug:
                        print(f"[REWARD] 检测到死锁，已添加死锁惩罚: {PENALTY_DEADLOCK:.2f}")
                else:
                    # 部分完成的情况
                    # 根据完成率给予部分奖励
                    completion_ratio = len(completed_tasks) / num_tasks
                    partial_bonus = REWARD_COMPLETION_BONUS * completion_ratio * 0.5  # 只给予一半的比例奖励
                    reward += partial_bonus

                    # 更新总奖励
                    self._step_state['total_reward'] += partial_bonus

                    if debug:
                        print(f"[REWARD] 部分完成 ({completion_ratio*100:.1f}%)，部分奖励: {partial_bonus:.2f}")

            # 获取下一个状态
            next_state = self._get_current_state(adj_matrix, task_features, num_tasks, machine_resources, comm_speed, memory_status, use_sequence)

            # 返回结果
            return next_state, reward, done, {
                "completed_tasks": completed_tasks,
                "total_time": self._step_state['total_completion_time'],
                "total_reward": self._step_state['total_reward'],
                "dag_failed": self._step_state['dag_failed'],
                "deadlock_detected": self._step_state['deadlock_detected'],
                "event_rewards": self._step_state['event_rewards']
            }
        else:
            # 内存不足，将任务移到待执行队列
            ready_queue.remove(task_idx)
            pending_tasks.add(task_idx)

            # 添加惩罚
            reward = PENALTY_TASK_TO_PENDING
            self._step_state['total_reward'] += reward

            if debug:
                print(f"[READY->PENDING] 任务{task_idx}因内存不足({task_memory_req:.2f}GB > {memory_status[machine_idx]:.2f}GB)移至待执行队列")
                print(f"                惩罚: {PENALTY_TASK_TO_PENDING:.2f}")

            # 获取下一个状态
            next_state = self._get_current_state(adj_matrix, task_features, num_tasks, machine_resources, comm_speed, memory_status, use_sequence)

            # 返回结果
            return next_state, reward, False, {
                "completed_tasks": completed_tasks,
                "total_time": self._step_state['total_completion_time'],
                "total_reward": self._step_state['total_reward'],
                "dag_failed": self._step_state['dag_failed'],
                "deadlock_detected": self._step_state['deadlock_detected'],
                "event_rewards": self._step_state['event_rewards']
            }

    def _convert_action_to_machine_idx(self, action, num_machines):
        """
        将动作值从[-1,1]映射到[0,num_machines-1]

        参数:
            action: 动作值，范围为[-1,1]
            num_machines: 机器数量

        返回:
            machine_idx: 机器索引，范围为[0,num_machines-1]
        """
        # 将动作值从[-1,1]映射到[0,num_machines-1]
        machine_idx = int(round(((action + 1) / 2) * (num_machines - 1)))
        machine_idx = max(0, min(machine_idx, num_machines - 1))  # 确保索引在有效范围内
        return machine_idx