#!/usr/bin/env python3
"""
测试机器选择学习效果
专门测试调整奖励权重后，智能体是否能学会选择更快的边缘设备而不是用户设备
"""

import torch
import numpy as np
import argparse
import time
import matplotlib.pyplot as plt
from enhanced_data_loader import <PERSON>hancedDataLoader
from enhanced_config import EnhancedLLMConfig
from improved_xlstm_networks import ImprovedXLSTMSACAgent
from improved_reward_function import ImprovedRewardFunction

def test_machine_selection_learning():
    """测试机器选择学习效果"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--device_num", default=5, type=int, help="边缘设备数量")
    parser.add_argument("--data_path", default="../dataset/training/DAG_30_edges_5_mem_57-64GB_density_0.2_0.6",
                       type=str, help="数据路径")
    parser.add_argument("--episodes", default=100, type=int, help="训练回合数")
    parser.add_argument("--test_dag", default=0, type=int, help="测试用的DAG索引")
    args = parser.parse_args()

    print("🎯 测试机器选择学习效果 - 方案1: 调整奖励权重")
    print(f"参数: {args}")

    # 创建配置
    config = EnhancedLLMConfig()
    config.update_edge_servers(args.device_num)

    if args.data_path != config.TRAINING_DATA_PATH:
        config.TRAINING_DATA_PATH = args.data_path
        print(f"[CONFIG] 已设置自定义数据路径: {args.data_path}")

    # 创建数据加载器
    data_loader = EnhancedDataLoader(config)
    data_loader.set_debug(False)  # 关闭详细调试

    # 创建改进的xLSTM智能体
    agent = ImprovedXLSTMSACAgent(
        state_dim=config.ENHANCED_STATE_DIM,
        action_dim=2,  # [task_selection, machine_assignment]
        hidden_dim=256,
        seq_len=config.SEQ_LEN,
        num_machines=args.device_num + 1,  # 包括用户设备
        xlstm_layers=2,
        lr=3e-4,
        device="cuda"
    )

    print(f"📊 配置信息:")
    print(f"  边缘设备数: {args.device_num}")
    print(f"  测试DAG: {args.test_dag}")
    print(f"  训练回合数: {args.episodes}")
    print(f"  新奖励权重: 边缘设备+10.0, 用户设备-20.0")

    # 记录训练过程
    episode_makespans = []
    episode_rewards = []
    machine_usage_stats = []  # 记录机器使用统计
    user_device_usage = []    # 专门记录用户设备使用次数
    edge_device_usage = []    # 专门记录边缘设备使用次数

    print(f"\n🎯 开始训练...")
    start_time = time.time()

    for episode in range(args.episodes):
        # 使用固定的DAG进行测试，观察学习效果
        dag_idx = args.test_dag

        # 加载DAG数据
        task_features = data_loader.load_task_features(dag_idx)
        adj_matrix = data_loader.load_adjacency_matrix(dag_idx)
        machine_resources = data_loader.load_machines_resource()
        comm_speed = data_loader.load_machine_commu_speed()
        memory_status = data_loader.load_memory_status()

        num_tasks = task_features.shape[1]

        # 重置环境
        data_loader._step_state = None

        # 🔧 强制重置内存状态为原始值
        original_memory = data_loader.load_memory_status()
        for i in range(len(memory_status)):
            memory_status[i] = original_memory[i]

        # 初始化环境
        data_loader._init_step_state(task_features, adj_matrix, machine_resources, comm_speed, memory_status)

        # 获取初始状态
        state = data_loader.process_state_as_enhanced_sequence(
            task_features, adj_matrix, machine_resources, comm_speed, memory_status
        )

        episode_reward = 0
        step_count = 0
        done = False
        machine_assignments = []  # 记录机器分配
        step_rewards = []         # 记录每步奖励

        # 逐步调度
        while not done and step_count < num_tasks * 2:
            # 获取就绪任务掩码
            ready_tasks = data_loader.get_ready_tasks()
            if not ready_tasks:
                break

            ready_mask = np.zeros(config.SEQ_LEN)
            for i, _ in enumerate(ready_tasks[:config.SEQ_LEN]):
                if i < config.SEQ_LEN:
                    ready_mask[i] = 1.0

            # 转换为tensor
            state_tensor = torch.FloatTensor(state).unsqueeze(0).to("cuda")
            ready_mask_tensor = torch.FloatTensor(ready_mask).unsqueeze(0).to("cuda")

            # 选择动作 - 在前30个episode增加探索
            deterministic = episode > 30
            compound_action = agent.select_action(state_tensor, ready_mask_tensor, deterministic=deterministic)

            # 执行动作
            next_state, reward, done, info = data_loader.step_enhanced_task(
                compound_action, debug=False
            )

            # 记录机器分配
            if 'assigned_machine' in info:
                machine_assignments.append(info['assigned_machine'])

            # 记录步骤奖励
            step_rewards.append(reward)

            # 存储经验
            agent.store_transition(state, compound_action, reward, next_state, done)

            # 更新状态和奖励
            state = next_state
            episode_reward += reward
            step_count += 1

            # 训练智能体
            if len(agent.replay_buffer) > 64:
                agent.update()

        # 记录统计信息
        completed_tasks = info.get("completed_tasks", set())
        total_time = info.get("total_time", 1000000) / 1000.0  # 转换为秒
        completion_rate = len(completed_tasks) / num_tasks

        # 只记录成功完成的episode的makespan
        if completion_rate == 1.0:
            episode_makespans.append(total_time)
        else:
            episode_makespans.append(1000.0)  # 失败的episode用1000秒表示

        episode_rewards.append(episode_reward)

        # 统计机器使用情况
        machine_usage = [0] * (args.device_num + 1)
        for machine_idx in machine_assignments:
            if 0 <= machine_idx < len(machine_usage):
                machine_usage[machine_idx] += 1
        machine_usage_stats.append(machine_usage)

        # 专门统计用户设备vs边缘设备使用
        user_usage = machine_usage[0] if len(machine_usage) > 0 else 0
        edge_usage = sum(machine_usage[1:]) if len(machine_usage) > 1 else 0
        user_device_usage.append(user_usage)
        edge_device_usage.append(edge_usage)

        # 打印进度
        if (episode + 1) % 10 == 0 or episode < 5:
            recent_makespans = [m for m in episode_makespans[-10:] if m < 1000]
            avg_makespan = np.mean(recent_makespans) if recent_makespans else 1000.0
            avg_reward = np.mean(episode_rewards[-10:])

            # 最近10个episode的机器使用统计
            recent_user_usage = np.mean(user_device_usage[-10:])
            recent_edge_usage = np.mean(edge_device_usage[-10:])

            # 计算用户设备使用比例
            total_assignments = recent_user_usage + recent_edge_usage
            user_ratio = recent_user_usage / total_assignments if total_assignments > 0 else 0

            elapsed_time = time.time() - start_time
            print(f"Episode {episode+1:3d} | "
                  f"Makespan: {total_time:6.1f}s (平均: {avg_makespan:6.1f}s) | "
                  f"奖励: {episode_reward:6.1f} (平均: {avg_reward:6.1f}) | "
                  f"完成率: {completion_rate:.1%} | "
                  f"用户设备使用率: {user_ratio:.1%} | "
                  f"用时: {elapsed_time:.1f}s")

    # 训练完成分析
    print(f"\n📈 训练完成分析:")

    # 成功完成的episode统计
    successful_episodes = [m for m in episode_makespans if m < 1000]
    if successful_episodes:
        print(f"  成功完成回合: {len(successful_episodes)}/{args.episodes}")
        print(f"  最佳Makespan: {min(successful_episodes):.2f}秒")
        print(f"  平均Makespan: {np.mean(successful_episodes):.2f}秒")
        print(f"  Makespan标准差: {np.std(successful_episodes):.2f}秒")

        # 分析学习趋势
        first_half = successful_episodes[:len(successful_episodes)//2] if len(successful_episodes) > 10 else successful_episodes[:5]
        second_half = successful_episodes[len(successful_episodes)//2:] if len(successful_episodes) > 10 else successful_episodes[5:]

        if first_half and second_half:
            improvement = np.mean(first_half) - np.mean(second_half)
            print(f"  Makespan改进: {improvement:.2f}秒 ({'改进' if improvement > 0 else '退化'})")
    else:
        print(f"  ⚠️ 没有成功完成的回合!")

    # 机器选择学习分析
    print(f"\n🖥️ 机器选择学习分析:")

    # 分析前后期的机器使用变化
    first_quarter = user_device_usage[:args.episodes//4]
    last_quarter = user_device_usage[-args.episodes//4:]

    if first_quarter and last_quarter:
        early_user_ratio = np.mean(first_quarter) / (np.mean(first_quarter) + np.mean(edge_device_usage[:args.episodes//4])) if np.mean(first_quarter) + np.mean(edge_device_usage[:args.episodes//4]) > 0 else 0
        late_user_ratio = np.mean(last_quarter) / (np.mean(last_quarter) + np.mean(edge_device_usage[-args.episodes//4:])) if np.mean(last_quarter) + np.mean(edge_device_usage[-args.episodes//4:]) > 0 else 0

        print(f"  前期用户设备使用率: {early_user_ratio:.1%}")
        print(f"  后期用户设备使用率: {late_user_ratio:.1%}")
        print(f"  学习效果: {'✅ 学会避免用户设备' if late_user_ratio < early_user_ratio else '❌ 仍偏好用户设备'}")

    # 整体机器使用分析
    avg_machine_usage = np.mean(machine_usage_stats, axis=0)
    for i, usage in enumerate(avg_machine_usage):
        machine_name = "用户设备" if i == 0 else f"边缘设备{i}"
        print(f"  {machine_name}: 平均使用 {usage:.1f} 次/episode")

    # 绘制学习曲线
    if len(successful_episodes) > 5:
        plt.figure(figsize=(15, 10))

        # Makespan学习曲线
        plt.subplot(2, 3, 1)
        plt.plot(episode_makespans)
        plt.title('Makespan Learning Curve')
        plt.xlabel('Episode')
        plt.ylabel('Makespan (seconds)')
        plt.ylim(0, min(1000, max(episode_makespans) * 1.1))

        # 奖励学习曲线
        plt.subplot(2, 3, 2)
        plt.plot(episode_rewards)
        plt.title('Reward Learning Curve')
        plt.xlabel('Episode')
        plt.ylabel('Episode Reward')

        # 用户设备使用率变化
        plt.subplot(2, 3, 3)
        total_usage = np.array(user_device_usage) + np.array(edge_device_usage)
        user_ratios = np.array(user_device_usage) / np.maximum(total_usage, 1)
        plt.plot(user_ratios)
        plt.title('User Device Usage Ratio')
        plt.xlabel('Episode')
        plt.ylabel('User Device Usage Ratio')
        plt.ylim(0, 1)

        # 机器使用分布
        plt.subplot(2, 3, 4)
        machine_usage_array = np.array(machine_usage_stats)
        for i in range(machine_usage_array.shape[1]):
            machine_name = "用户设备" if i == 0 else f"边缘设备{i}"
            plt.plot(machine_usage_array[:, i], label=machine_name)
        plt.title('Machine Usage Over Time')
        plt.xlabel('Episode')
        plt.ylabel('Tasks Assigned')
        plt.legend()

        # Makespan分布直方图
        plt.subplot(2, 3, 5)
        plt.hist(successful_episodes, bins=20, alpha=0.7)
        plt.title('Makespan Distribution')
        plt.xlabel('Makespan (seconds)')
        plt.ylabel('Frequency')

        # 用户设备vs边缘设备使用对比
        plt.subplot(2, 3, 6)
        plt.plot(user_device_usage, label='用户设备', alpha=0.7)
        plt.plot(edge_device_usage, label='边缘设备', alpha=0.7)
        plt.title('User vs Edge Device Usage')
        plt.xlabel('Episode')
        plt.ylabel('Tasks Assigned')
        plt.legend()

        plt.tight_layout()
        plt.savefig('machine_selection_learning_results.png', dpi=300, bbox_inches='tight')
        print(f"\n📊 学习曲线已保存到: machine_selection_learning_results.png")

    return {
        'makespans': episode_makespans,
        'rewards': episode_rewards,
        'machine_usage': machine_usage_stats,
        'user_device_usage': user_device_usage,
        'edge_device_usage': edge_device_usage,
        'best_makespan': min(successful_episodes) if successful_episodes else None,
        'avg_makespan': np.mean(successful_episodes) if successful_episodes else None
    }

if __name__ == "__main__":
    results = test_machine_selection_learning()
