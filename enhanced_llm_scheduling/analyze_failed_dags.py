#!/usr/bin/env python3
"""
分析训练中失败的DAG
"""

import pandas as pd
import numpy as np
import os
import sys

def analyze_failed_dags(model_path):
    """分析指定模型训练中的失败DAG"""
    
    print("🔍 本次训练中调度失败的DAG详细分析")
    print("=" * 80)
    
    # 文件路径
    history_file = f"{model_path}/final_training_history.xlsx"
    failed_file = f"{model_path}/final_failed_dags_analysis.xlsx"
    summary_file = f"{model_path}/final_dag_analysis_summary.xlsx"
    
    # 1. 从训练历史中分析失败episode
    if os.path.exists(history_file):
        print("📊 从训练历史分析失败Episode:")
        df = pd.read_excel(history_file)
        
        # 找出失败的episode
        failed_episodes = df[df['Completion_Ratio'] < 1.0]
        
        print(f"   总Episode数: {len(df)}")
        print(f"   失败Episode数: {len(failed_episodes)}")
        print(f"   成功率: {(len(df) - len(failed_episodes)) / len(df) * 100:.1f}%")
        
        if len(failed_episodes) > 0:
            print("\n❌ 失败Episode详情:")
            for idx, row in failed_episodes.iterrows():
                episode = int(row['Episode'])
                completion = row['Completion_Ratio']
                reward = row['Reward']
                makespan = row['Makespan']
                
                print(f"   Episode {episode:3d}: 完成率 {completion:5.1%}, 奖励 {reward:7.2f}, Makespan {makespan:6.1f}s")
                
                # 分析失败类型
                if completion == 0.0:
                    failure_type = "完全失败 (0%完成)"
                elif completion < 0.5:
                    failure_type = "严重失败 (<50%完成)"
                elif completion < 1.0:
                    failure_type = "部分失败 (>50%完成)"
                
                print(f"              失败类型: {failure_type}")
    
    # 2. 读取详细失败分析文件
    if os.path.exists(failed_file):
        print(f"\n📋 详细失败DAG分析文件:")
        try:
            failed_df = pd.read_excel(failed_file)
            print(f"   文件存在，包含 {len(failed_df)} 个失败DAG记录")
            
            if len(failed_df) > 0:
                print("\n🔍 失败DAG详细信息:")
                for idx, row in failed_df.iterrows():
                    print(f"\n   ❌ 失败DAG #{idx+1}:")
                    print(f"      Episode: {row['episode']}")
                    print(f"      DAG ID: {row['dag_id']}")
                    print(f"      完成率: {row['completion_rate']:.1%} ({row['completed_tasks']}/{row['total_tasks']})")
                    print(f"      失败原因: {row['reason']}")
                    print(f"      最大并行度: {row['max_ready_tasks']}")
                    print(f"      总内存需求: {row['total_memory_demand']:.2f}GB")
                    print(f"      LLM任务比例: {row['llm_ratio']:.1%}")
                    print(f"      平均任务内存: {row['avg_memory_per_task']:.2f}GB")
                    
                    # 失败原因分析
                    reasons = []
                    if row['memory_overflow']:
                        reasons.append("内存溢出")
                    if row['deadlock_detected']:
                        reasons.append("死锁")
                    if row['high_parallelism']:
                        reasons.append("高并行度")
                    
                    if reasons:
                        print(f"      具体问题: {', '.join(reasons)}")
                    else:
                        print(f"      具体问题: 未知原因")
        except Exception as e:
            print(f"   读取失败: {e}")
    else:
        print(f"\n⚠️  详细失败分析文件不存在: {failed_file}")
    
    # 3. 读取汇总信息
    if os.path.exists(summary_file):
        print(f"\n📈 失败统计汇总:")
        try:
            summary_df = pd.read_excel(summary_file)
            summary = summary_df.iloc[0]
            
            print(f"   总episode数: {summary['total_episodes']}")
            print(f"   成功episode数: {summary['successful_episodes']}")
            print(f"   失败episode数: {summary['failed_episodes']}")
            print(f"   成功率: {summary['success_rate']:.1f}%")
            print(f"   内存溢出次数: {summary['memory_overflow_count']}")
            print(f"   死锁次数: {summary['deadlock_count']}")
            print(f"   高并行度失败次数: {summary['high_parallelism_count']}")
            print(f"   平均完成率: {summary['avg_completion_rate']:.1%}")
            print(f"   唯一失败DAG数: {summary['failed_dag_count']}")
            
            # 解析失败DAG ID列表
            try:
                failed_dag_ids = eval(summary['unique_failed_dag_ids'])
                print(f"   失败DAG ID列表: {failed_dag_ids}")
            except:
                print(f"   失败DAG ID列表: {summary['unique_failed_dag_ids']}")
                
        except Exception as e:
            print(f"   读取失败: {e}")
    else:
        print(f"\n⚠️  汇总文件不存在: {summary_file}")
    
    # 4. 根据训练日志推断失败原因
    print(f"\n🔍 失败原因分析:")
    if os.path.exists(history_file):
        df = pd.read_excel(history_file)
        failed_episodes = df[df['Completion_Ratio'] < 1.0]
        
        # 分析失败模式
        complete_failures = len(failed_episodes[failed_episodes['Completion_Ratio'] == 0.0])
        partial_failures = len(failed_episodes[failed_episodes['Completion_Ratio'] > 0.0])
        
        print(f"   完全失败 (0%完成): {complete_failures} 个episode")
        print(f"   部分失败 (>0%完成): {partial_failures} 个episode")
        
        if complete_failures > 0:
            print(f"   完全失败可能原因: 代码错误、环境问题、或极难DAG")
        
        if partial_failures > 0:
            print(f"   部分失败可能原因: 内存不足、死锁、或调度策略问题")
            
            # 分析部分失败的完成率分布
            partial_completion_rates = failed_episodes[failed_episodes['Completion_Ratio'] > 0.0]['Completion_Ratio']
            if len(partial_completion_rates) > 0:
                print(f"   部分失败完成率范围: {partial_completion_rates.min():.1%} - {partial_completion_rates.max():.1%}")
                print(f"   部分失败平均完成率: {partial_completion_rates.mean():.1%}")

def main():
    """主函数"""
    # 默认使用最新的训练模型
    default_model_path = "models/improved_xlstm_sac_5devices_20250528_163905/final"
    
    if len(sys.argv) > 1:
        model_path = sys.argv[1]
    else:
        model_path = default_model_path
    
    if not os.path.exists(model_path):
        print(f"❌ 模型路径不存在: {model_path}")
        return
    
    print(f"📂 分析模型: {model_path}")
    analyze_failed_dags(model_path)

if __name__ == "__main__":
    main()
