"""
简化的SAC智能体
使用离散动作空间，参考别人的系统设计
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
import random
from collections import deque

class SimplifiedActorNetwork(nn.Module):
    """简化的Actor网络 - 输出离散动作的概率分布"""

    def __init__(self, state_dim, action_dim, hidden_dim=256):
        super(SimplifiedActorNetwork, self).__init__()

        self.network = nn.Sequential(
            nn.Linear(state_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, action_dim)
        )

        self._init_weights()

    def _init_weights(self):
        """初始化网络权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight, gain=0.1)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0.0)

    def forward(self, state):
        """前向传播"""
        logits = self.network(state)
        return logits

    def sample(self, state):
        """采样动作"""
        logits = self.forward(state)
        dist = torch.distributions.Categorical(logits=logits)
        action = dist.sample()
        log_prob = dist.log_prob(action)
        return action, log_prob

class SimplifiedCriticNetwork(nn.Module):
    """简化的Critic网络"""

    def __init__(self, state_dim, action_dim, hidden_dim=256):
        super(SimplifiedCriticNetwork, self).__init__()

        self.network = nn.Sequential(
            nn.Linear(state_dim + action_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1)
        )

        self._init_weights()

    def _init_weights(self):
        """初始化网络权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight, gain=0.1)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0.0)

    def forward(self, state, action):
        """前向传播"""
        # 将离散动作转换为one-hot编码
        if action.dim() == 1:
            # 计算动作维度
            action_dim = self.network[0].in_features - state.shape[-1]
            action_one_hot = F.one_hot(action.long(), num_classes=action_dim)
            action_one_hot = action_one_hot.float()
        else:
            action_one_hot = action

        x = torch.cat([state, action_one_hot], dim=-1)
        return self.network(x)

class SimplifiedReplayBuffer:
    """简化的经验回放缓冲区"""

    def __init__(self, capacity):
        self.buffer = deque(maxlen=capacity)

    def push(self, state, action, reward, next_state, done):
        """添加经验"""
        self.buffer.append((state, action, reward, next_state, done))

    def sample(self, batch_size):
        """采样批次经验"""
        batch = random.sample(self.buffer, batch_size)

        states = torch.stack([torch.FloatTensor(exp[0]) for exp in batch])
        actions = torch.tensor([exp[1] for exp in batch], dtype=torch.long)
        rewards = torch.tensor([exp[2] for exp in batch], dtype=torch.float32)
        next_states = torch.stack([torch.FloatTensor(exp[3]) for exp in batch])
        dones = torch.tensor([exp[4] for exp in batch], dtype=torch.bool)

        return states, actions, rewards, next_states, dones

    def __len__(self):
        return len(self.buffer)

class SimplifiedSACAgent:
    """简化的SAC智能体 - 使用离散动作空间"""

    def __init__(self, config, device='cuda'):
        self.config = config
        self.device = device

        # 网络参数
        self.state_dim = config.STATE_DIM
        self.action_dim = config.ACTION_DIM
        self.hidden_dim = 256

        # 初始化网络
        self.actor = SimplifiedActorNetwork(
            self.state_dim, self.action_dim, self.hidden_dim
        ).to(device)

        self.critic1 = SimplifiedCriticNetwork(
            self.state_dim, self.action_dim, self.hidden_dim
        ).to(device)

        self.critic2 = SimplifiedCriticNetwork(
            self.state_dim, self.action_dim, self.hidden_dim
        ).to(device)

        # 目标网络
        self.target_critic1 = SimplifiedCriticNetwork(
            self.state_dim, self.action_dim, self.hidden_dim
        ).to(device)

        self.target_critic2 = SimplifiedCriticNetwork(
            self.state_dim, self.action_dim, self.hidden_dim
        ).to(device)

        # 复制参数到目标网络
        self.target_critic1.load_state_dict(self.critic1.state_dict())
        self.target_critic2.load_state_dict(self.critic2.state_dict())

        # 优化器
        self.actor_optimizer = optim.Adam(self.actor.parameters(), lr=config.LEARNING_RATE)
        self.critic1_optimizer = optim.Adam(self.critic1.parameters(), lr=config.LEARNING_RATE)
        self.critic2_optimizer = optim.Adam(self.critic2.parameters(), lr=config.LEARNING_RATE)

        # SAC参数
        self.gamma = config.GAMMA
        self.tau = config.TAU
        self.alpha = 0.2  # 熵正则化系数

        # 经验回放
        self.replay_buffer = SimplifiedReplayBuffer(config.REPLAY_BUFFER_SIZE)
        self.batch_size = config.BATCH_SIZE
        self.update_count = 0

        print(f"[SIMPLIFIED_SAC] 初始化完成")
        print(f"  状态维度: {self.state_dim}")
        print(f"  动作维度: {self.action_dim} (离散)")
        print(f"  隐藏维度: {self.hidden_dim}")

    def select_action(self, state, deterministic=False):
        """选择动作"""
        if not isinstance(state, torch.Tensor):
            state = torch.FloatTensor(state).unsqueeze(0).to(self.device)

        with torch.no_grad():
            if deterministic:
                # 确定性动作（用于评估）
                logits = self.actor(state)
                action = torch.argmax(logits, dim=-1)
            else:
                # 随机动作（用于探索）
                action, _ = self.actor.sample(state)

        return action.cpu().numpy()[0]

    def store_transition(self, state, action, reward, next_state, done):
        """存储转移经验"""
        self.replay_buffer.push(state, action, reward, next_state, done)

    def update(self):
        """更新网络参数"""
        if len(self.replay_buffer) < self.batch_size:
            return {}

        # 采样批次数据
        states, actions, rewards, next_states, dones = self.replay_buffer.sample(self.batch_size)

        states = states.to(self.device)
        actions = actions.to(self.device)
        rewards = rewards.to(self.device)
        next_states = next_states.to(self.device)
        dones = dones.to(self.device)

        # 更新Critic网络
        critic_loss = self._update_critics(states, actions, rewards, next_states, dones)

        # 更新Actor网络
        actor_loss = self._update_actor(states)

        # 软更新目标网络
        self._soft_update_targets()

        self.update_count += 1

        return {
            'critic_loss': critic_loss,
            'actor_loss': actor_loss,
            'alpha': self.alpha
        }

    def _update_critics(self, states, actions, rewards, next_states, dones):
        """更新Critic网络"""
        with torch.no_grad():
            # 计算目标Q值
            next_logits = self.actor(next_states)
            next_probs = F.softmax(next_logits, dim=-1)
            next_log_probs = F.log_softmax(next_logits, dim=-1)

            # 计算期望Q值
            target_q1_all = self.target_critic1(next_states, next_probs)
            target_q2_all = self.target_critic2(next_states, next_probs)
            target_q_all = torch.min(target_q1_all, target_q2_all)

            # 加入熵正则化
            target_q = (next_probs * (target_q_all - self.alpha * next_log_probs)).sum(dim=-1, keepdim=True)
            target_q = rewards.unsqueeze(-1) + self.gamma * (1 - dones.float().unsqueeze(-1)) * target_q

        # 当前Q值
        actions_one_hot = F.one_hot(actions, num_classes=self.action_dim).float()
        current_q1 = self.critic1(states, actions_one_hot)
        current_q2 = self.critic2(states, actions_one_hot)

        # Critic损失
        critic1_loss = F.mse_loss(current_q1, target_q)
        critic2_loss = F.mse_loss(current_q2, target_q)

        # 更新Critic1
        self.critic1_optimizer.zero_grad()
        critic1_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.critic1.parameters(), max_norm=1.0)
        self.critic1_optimizer.step()

        # 更新Critic2
        self.critic2_optimizer.zero_grad()
        critic2_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.critic2.parameters(), max_norm=1.0)
        self.critic2_optimizer.step()

        return (critic1_loss + critic2_loss).item() / 2

    def _update_actor(self, states):
        """更新Actor网络"""
        # 采样动作
        logits = self.actor(states)
        probs = F.softmax(logits, dim=-1)
        log_probs = F.log_softmax(logits, dim=-1)

        # 计算Q值
        q1 = self.critic1(states, probs)
        q2 = self.critic2(states, probs)
        q = torch.min(q1, q2)

        # Actor损失（策略梯度 + 熵正则化）
        actor_loss = (probs * (self.alpha * log_probs - q)).sum(dim=-1).mean()

        # 更新Actor
        self.actor_optimizer.zero_grad()
        actor_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.actor.parameters(), max_norm=1.0)
        self.actor_optimizer.step()

        return actor_loss.item()

    def _soft_update_targets(self):
        """软更新目标网络"""
        for target_param, param in zip(self.target_critic1.parameters(), self.critic1.parameters()):
            target_param.data.copy_(self.tau * param.data + (1 - self.tau) * target_param.data)

        for target_param, param in zip(self.target_critic2.parameters(), self.critic2.parameters()):
            target_param.data.copy_(self.tau * param.data + (1 - self.tau) * target_param.data)

    def save_models(self, filepath):
        """保存模型"""
        torch.save({
            'actor': self.actor.state_dict(),
            'critic1': self.critic1.state_dict(),
            'critic2': self.critic2.state_dict(),
            'target_critic1': self.target_critic1.state_dict(),
            'target_critic2': self.target_critic2.state_dict(),
            'actor_optimizer': self.actor_optimizer.state_dict(),
            'critic1_optimizer': self.critic1_optimizer.state_dict(),
            'critic2_optimizer': self.critic2_optimizer.state_dict(),
        }, filepath)

    def load_models(self, filepath):
        """加载模型"""
        checkpoint = torch.load(filepath, map_location=self.device)

        self.actor.load_state_dict(checkpoint['actor'])
        self.critic1.load_state_dict(checkpoint['critic1'])
        self.critic2.load_state_dict(checkpoint['critic2'])
        self.target_critic1.load_state_dict(checkpoint['target_critic1'])
        self.target_critic2.load_state_dict(checkpoint['target_critic2'])

        self.actor_optimizer.load_state_dict(checkpoint['actor_optimizer'])
        self.critic1_optimizer.load_state_dict(checkpoint['critic1_optimizer'])
        self.critic2_optimizer.load_state_dict(checkpoint['critic2_optimizer'])
