"""
真正的xLSTM版本SAC网络架构
参照原版encoder-decoder实现
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import sys
import os

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

try:
    from xlstm import (
        xLSTMBlockStack,
        xLSTMBlockStackConfig,
        mLSTMBlockConfig,
        mLSTMLayerConfig,
        sLSTMBlockConfig,
        sLSTMLayerConfig,
        FeedForwardConfig,
    )
    XLSTM_AVAILABLE = True
    print("[xLSTM] 真正的xLSTM库可用")
except ImportError:
    print("[xLSTM] 警告: 真正的xLSTM库不可用，将使用简化版本")
    XLSTM_AVAILABLE = False

def create_xlstm_config(seq_len, hidden_dim, num_blocks, is_encoder=True):
    """
    创建 xLSTM 配置 - 参照原版实现

    参数:
        seq_len: 序列长度
        hidden_dim: 隐藏层维度
        num_blocks: 块数量
        is_encoder: 是否为编码器配置

    返回:
        xLSTM 配置对象
    """
    if not XLSTM_AVAILABLE:
        return None

    # 确保 hidden_dim 是 16 的倍数，这是 xLSTM 的要求
    hidden_dim = ((hidden_dim + 15) // 16) * 16

    # 创建配置 - 参照原版代码
    cfg = xLSTMBlockStackConfig(
        mlstm_block=mLSTMBlockConfig(
            mlstm=mLSTMLayerConfig(
                conv1d_kernel_size=2,
                qkv_proj_blocksize=2,
                num_heads=2
            )
        ),
        # slstm_block=sLSTMBlockConfig(
        #     slstm=sLSTMLayerConfig(
        #         backend="cuda" if torch.cuda.is_available() else "cpu",
        #         num_heads=4,
        #         conv1d_kernel_size=4,
        #         bias_init="powerlaw_blockdependent",
        #     ),
        #     feedforward=FeedForwardConfig(proj_factor=1.3, act_fn="gelu"),
        # ),
        context_length=seq_len,
        num_blocks=num_blocks,
        embedding_dim=hidden_dim,
        slstm_at=[]
        # slstm_at=[1] if is_encoder else [0],  # 编码器和解码器使用不同的块配置
    )

    return cfg

# 简化版xLSTM块（当真正的xLSTM不可用时使用）
class SimpleXLSTMBlock(nn.Module):
    """简化的xLSTM块，用于替代"""

    def __init__(self, input_dim, hidden_dim):
        super(SimpleXLSTMBlock, self).__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim

        # 使用LSTM作为简化实现
        self.lstm = nn.LSTM(input_dim, hidden_dim, batch_first=True)
        self.layer_norm = nn.LayerNorm(hidden_dim)

    def forward(self, x):
        # LSTM前向传播
        output, _ = self.lstm(x)
        # 层归一化
        output = self.layer_norm(output)
        return output

class XLSTMActorNetwork(nn.Module):
    """xLSTM版本的Actor网络，使用复合动作架构（与增强版/LSTM版本保持一致）"""

    def __init__(self, state_dim, action_dim, hidden_dim=256, seq_len=32,
                 num_machines=6, xlstm_layers=2):
        super(XLSTMActorNetwork, self).__init__()

        self.state_dim = state_dim
        self.action_dim = action_dim
        self.hidden_dim = hidden_dim
        self.seq_len = seq_len
        self.num_machines = num_machines
        self.xlstm_layers = xlstm_layers

        # 确保 hidden_dim 是 16 的倍数
        self.hidden_dim = ((hidden_dim + 15) // 16) * 16

        print(f"[xLSTM_ACTOR] 使用 xLSTM 复合动作架构")
        print(f"  序列长度: {seq_len}, 状态维度: {state_dim}")
        print(f"  隐藏维度: {self.hidden_dim}, 动作维度: {action_dim}, xLSTM层数: {xlstm_layers}")

        # 输入投影层
        self.input_projection = nn.Linear(state_dim, self.hidden_dim)

        if XLSTM_AVAILABLE:
            # 创建真正的xLSTM编码器配置
            xlstm_cfg = create_xlstm_config(
                seq_len=seq_len,
                hidden_dim=self.hidden_dim,
                num_blocks=xlstm_layers,
                is_encoder=True
            )

            # 创建真正的xLSTM编码器
            self.xlstm_encoder = xLSTMBlockStack(xlstm_cfg)
            print(f"[xLSTM_ACTOR] 使用真正的xLSTM实现")
        else:
            # 使用简化版本
            self.xlstm_encoder = nn.ModuleList([
                SimpleXLSTMBlock(self.hidden_dim, self.hidden_dim) for _ in range(xlstm_layers)
            ])
            print(f"[xLSTM_ACTOR] 使用简化版xLSTM实现")

        # 全局上下文处理
        self.global_context = nn.Sequential(
            nn.Linear(self.hidden_dim, self.hidden_dim),
            nn.ReLU(),
            nn.Linear(self.hidden_dim, self.hidden_dim)
        )

        # 任务选择头（注意力机制）
        self.task_attention = nn.MultiheadAttention(
            embed_dim=self.hidden_dim,
            num_heads=8,
            dropout=0.1,
            batch_first=True
        )

        self.task_selector = nn.Sequential(
            nn.Linear(self.hidden_dim, self.hidden_dim),
            nn.ReLU(),
            nn.Linear(self.hidden_dim, 1)
        )

        # 机器分配头
        self.machine_allocator = nn.Sequential(
            nn.Linear(self.hidden_dim * 2, self.hidden_dim),  # 任务特征 + 全局上下文
            nn.ReLU(),
            nn.Linear(self.hidden_dim, self.hidden_dim),
            nn.ReLU(),
            nn.Linear(self.hidden_dim, 1)  # 输出连续值
        )

        # 输出层的标准差参数
        self.log_std_min = -10
        self.log_std_max = 1

        # 初始化网络权重
        self._init_weights()

    def _init_weights(self):
        """初始化网络权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight, gain=0.1)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0.0)
            elif isinstance(module, nn.LSTM):
                for name, param in module.named_parameters():
                    if 'weight' in name:
                        nn.init.xavier_uniform_(param, gain=0.1)
                    elif 'bias' in name:
                        nn.init.constant_(param, 0.0)

    def forward(self, state, ready_mask=None):
        """
        前向传播 - 复合动作架构（与增强版/LSTM版本保持一致）

        参数:
            state: [batch_size, seq_len, state_dim]
            ready_mask: [batch_size, seq_len] 就绪任务掩码

        返回:
            task_action_mean, task_action_log_std: 任务选择动作的均值和对数标准差
            machine_action_mean, machine_action_log_std: 机器分配动作的均值和对数标准差
        """
        batch_size, seq_len, _ = state.shape

        # 1. xLSTM编码
        # 首先将输入投影到hidden_dim
        encoded = self.input_projection(state)  # [batch_size, seq_len, hidden_dim]

        if XLSTM_AVAILABLE:
            # 使用真正的xLSTM
            encoded = self.xlstm_encoder(encoded)  # [batch_size, seq_len, hidden_dim]
        else:
            # 使用简化版本
            for xlstm_layer in self.xlstm_encoder:
                encoded = xlstm_layer(encoded)  # [batch_size, seq_len, hidden_dim]

        # 2. 全局上下文计算
        global_context = torch.mean(encoded, dim=1)  # [batch_size, hidden_dim]
        global_context = self.global_context(global_context)  # [batch_size, hidden_dim]

        # 3. 任务选择（基于注意力机制）
        # 使用全局上下文作为query
        query = global_context.unsqueeze(1)  # [batch_size, 1, hidden_dim]

        # 应用注意力机制
        if ready_mask is not None and ready_mask.sum().item() == 0:
            # 如果没有就绪任务，使用全局上下文
            attended_features = global_context.unsqueeze(1)  # [batch_size, 1, hidden_dim]
        else:
            attended_features, attention_weights = self.task_attention(
                query, encoded, encoded,
                key_padding_mask=~ready_mask.bool() if ready_mask is not None else None
            )  # [batch_size, 1, hidden_dim]

        attended_features = attended_features.squeeze(1)  # [batch_size, hidden_dim]

        # 计算任务选择分数
        task_scores = self.task_selector(encoded)  # [batch_size, seq_len, 1]
        task_scores = task_scores.squeeze(-1)  # [batch_size, seq_len]

        # 应用就绪掩码
        if ready_mask is not None:
            task_scores = task_scores.masked_fill(~ready_mask.bool(), float('-inf'))

        # 任务选择动作（使用softmax转换为概率分布，然后转换为连续动作）
        # 处理没有就绪任务的情况
        if ready_mask is not None and ready_mask.sum(dim=-1).min() == 0:
            # 如果某些批次没有就绪任务，使用均匀分布
            task_probs = torch.ones_like(task_scores) / seq_len
        else:
            task_probs = F.softmax(task_scores, dim=-1)  # [batch_size, seq_len]

        # 将概率分布转换为连续动作值（期望值）
        task_indices = torch.arange(seq_len, device=state.device).float()
        task_action_mean = torch.sum(task_probs * task_indices, dim=-1)  # [batch_size]
        task_action_mean = (task_action_mean / max(seq_len - 1, 1)) * 2 - 1  # 归一化到[-1, 1]

        # 确保没有NaN值和数值稳定性
        task_action_mean = torch.clamp(task_action_mean, -0.99, 0.99)

        # 检查并处理NaN值
        if torch.isnan(task_action_mean).any():
            task_action_mean = torch.zeros_like(task_action_mean)

        # 任务选择的标准差（固定值，更保守）
        task_action_log_std = torch.full_like(task_action_mean, -2.0)

        # 4. 机器分配
        # 结合选中任务的特征和全局上下文
        combined_features = torch.cat([attended_features, global_context], dim=-1)
        machine_output = self.machine_allocator(combined_features)  # [batch_size, 1]

        # 使用更稳定的激活函数
        machine_action_mean = torch.tanh(machine_output.squeeze(-1) * 0.5)  # [batch_size] 缩放输入

        # 确保没有NaN值和数值稳定性
        machine_action_mean = torch.clamp(machine_action_mean, -0.99, 0.99)

        # 检查并处理NaN值
        if torch.isnan(machine_action_mean).any():
            machine_action_mean = torch.zeros_like(machine_action_mean)

        machine_action_log_std = torch.full_like(machine_action_mean, -2.0)

        return (task_action_mean, task_action_log_std,
                machine_action_mean, machine_action_log_std)

    def sample(self, state, ready_mask=None):
        """采样动作"""
        task_mean, task_log_std, machine_mean, machine_log_std = self.forward(state, ready_mask)

        # 数值稳定性检查
        task_mean = torch.clamp(task_mean, -10.0, 10.0)
        machine_mean = torch.clamp(machine_mean, -10.0, 10.0)
        task_log_std = torch.clamp(task_log_std, self.log_std_min, self.log_std_max)
        machine_log_std = torch.clamp(machine_log_std, self.log_std_min, self.log_std_max)

        task_std = task_log_std.exp()
        machine_std = machine_log_std.exp()

        # 采样
        task_normal = torch.distributions.Normal(task_mean, task_std)
        machine_normal = torch.distributions.Normal(machine_mean, machine_std)

        task_action = task_normal.rsample()
        machine_action = machine_normal.rsample()

        # 应用tanh激活并计算log概率
        task_action_tanh = torch.tanh(task_action)
        machine_action_tanh = torch.tanh(machine_action)

        task_log_prob = task_normal.log_prob(task_action) - torch.log(1 - task_action_tanh.pow(2) + 1e-6)
        machine_log_prob = machine_normal.log_prob(machine_action) - torch.log(1 - machine_action_tanh.pow(2) + 1e-6)

        # 组合动作和log概率
        actions = torch.stack([task_action_tanh, machine_action_tanh], dim=-1)
        log_probs = task_log_prob + machine_log_prob

        return actions, log_probs

class XLSTMCriticNetwork(nn.Module):
    """xLSTM版本的Critic网络，使用复合动作架构（与增强版/LSTM版本保持一致）"""

    def __init__(self, state_dim, action_dim, hidden_dim=256, seq_len=32, xlstm_layers=2):
        super(XLSTMCriticNetwork, self).__init__()

        self.state_dim = state_dim
        self.action_dim = action_dim
        self.hidden_dim = hidden_dim
        self.xlstm_layers = xlstm_layers

        # 确保 hidden_dim 是 16 的倍数
        self.hidden_dim = ((hidden_dim + 15) // 16) * 16

        # 输入投影层
        self.input_projection = nn.Linear(state_dim, self.hidden_dim)

        if XLSTM_AVAILABLE:
            # 创建真正的xLSTM编码器
            xlstm_cfg = create_xlstm_config(
                seq_len=seq_len,
                hidden_dim=self.hidden_dim,
                num_blocks=xlstm_layers,
                is_encoder=True
            )
            self.xlstm_encoder = xLSTMBlockStack(xlstm_cfg)
        else:
            # 使用简化版本
            self.xlstm_encoder = nn.ModuleList([
                SimpleXLSTMBlock(self.hidden_dim, self.hidden_dim) for _ in range(xlstm_layers)
            ])

        # 状态编码
        self.state_encoder = nn.Sequential(
            nn.Linear(self.hidden_dim, self.hidden_dim),
            nn.ReLU(),
            nn.Linear(self.hidden_dim, self.hidden_dim)
        )

        # 动作编码
        self.action_encoder = nn.Sequential(
            nn.Linear(action_dim, self.hidden_dim),
            nn.ReLU(),
            nn.Linear(self.hidden_dim, self.hidden_dim)
        )

        # Q值输出
        self.q_head = nn.Sequential(
            nn.Linear(self.hidden_dim * 2, self.hidden_dim),
            nn.ReLU(),
            nn.Linear(self.hidden_dim, self.hidden_dim),
            nn.ReLU(),
            nn.Linear(self.hidden_dim, 1)
        )

        # 初始化网络权重
        self._init_weights()

        # 只在第一次初始化时打印信息
        if not hasattr(XLSTMCriticNetwork, '_init_count'):
            XLSTMCriticNetwork._init_count = 0
        XLSTMCriticNetwork._init_count += 1

        if XLSTMCriticNetwork._init_count <= 2:
            print(f"[xLSTM_CRITIC] 初始化完成: state_dim={state_dim}, action_dim={action_dim}")

    def _init_weights(self):
        """初始化网络权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight, gain=0.1)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0.0)
            elif isinstance(module, nn.LSTM):
                for name, param in module.named_parameters():
                    if 'weight' in name:
                        nn.init.xavier_uniform_(param, gain=0.1)
                    elif 'bias' in name:
                        nn.init.constant_(param, 0.0)

    def forward(self, state, action):
        """
        前向传播

        参数:
            state: [batch_size, seq_len, state_dim]
            action: [batch_size, action_dim]

        返回:
            q_value: [batch_size, 1]
        """
        # 1. 状态编码
        encoded = self.input_projection(state)  # [batch_size, seq_len, hidden_dim]

        if XLSTM_AVAILABLE:
            # 使用真正的xLSTM
            encoded = self.xlstm_encoder(encoded)
        else:
            # 使用简化版本
            for xlstm_layer in self.xlstm_encoder:
                encoded = xlstm_layer(encoded)

        # 全局池化
        state_features = torch.mean(encoded, dim=1)  # [batch_size, hidden_dim]
        state_encoded = self.state_encoder(state_features)

        # 2. 动作编码
        action_encoded = self.action_encoder(action)

        # 3. 组合并输出Q值
        combined = torch.cat([state_encoded, action_encoded], dim=-1)
        q_value = self.q_head(combined)

        return q_value
