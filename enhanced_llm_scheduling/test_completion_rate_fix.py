#!/usr/bin/env python3
"""
测试完成率修复效果
验证token处理和内存管理修复是否能提高完成率到100%
"""

import os
import sys
import numpy as np
import time
from datetime import datetime

# 添加路径
sys.path.append('..')
sys.path.append('.')

from enhanced_data_loader import EnhancedDataLoader
from improved_xlstm_networks import ImprovedXLSTMSACAgent
from improved_reward_function import ImprovedRewardFunction
from enhanced_config import EnhancedLLMConfig

def test_completion_rate_fix(data_path, num_test_episodes=50, device_num=5):
    """
    测试完成率修复效果
    Args:
        data_path: 测试数据路径
        num_test_episodes: 测试回合数
        device_num: 边缘设备数量
    """
    print("🔧 测试完成率修复效果")
    print("=" * 80)

    # 创建配置实例
    config = EnhancedLLMConfig()
    config.NUM_EDGE_SERVERS = device_num
    config.update_edge_servers(device_num)
    config.set_custom_data_path(data_path)

    print(f"📊 测试配置:")
    print(f"  数据路径: {data_path}")
    print(f"  测试回合数: {num_test_episodes}")
    print(f"  边缘设备数: {device_num}")
    print(f"  状态维度: {config.ENHANCED_STATE_DIM}")

    # 创建数据加载器
    data_loader = EnhancedDataLoader(config, debug=True)

    # 创建智能体（使用随机策略进行测试）
    agent = ImprovedXLSTMSACAgent(
        state_dim=config.ENHANCED_STATE_DIM,
        action_dim=2,
        hidden_dim=256,
        seq_len=config.SEQ_LEN,
        num_machines=device_num + 1,
        xlstm_layers=2,
        lr=3e-4,
        device='cpu'  # 使用CPU进行快速测试
    )

    # 统计结果
    completion_rates = []
    makespans = []
    successful_episodes = 0
    failed_episodes = 0
    token_issues = 0
    memory_issues = 0

    print(f"\n🎯 开始测试...")

    for episode in range(num_test_episodes):
        try:
            # 随机选择DAG
            dag_idx = np.random.randint(0, 200)

            # 加载DAG数据
            task_features = data_loader.load_task_features(dag_idx)
            adj_matrix = data_loader.load_adjacency_matrix(dag_idx)
            machine_resources = data_loader.load_machines_resource()
            comm_speed = data_loader.load_machine_commu_speed()
            memory_status = data_loader.load_memory_status()
            num_tasks = task_features.shape[1]

            # 初始化环境
            data_loader._init_step_state(task_features, adj_matrix, machine_resources, comm_speed, memory_status)

            # 获取初始状态
            state = data_loader.process_state_as_enhanced_sequence(
                task_features, adj_matrix, machine_resources, comm_speed, memory_status)

            step_count = 0
            max_steps = num_tasks * 2
            episode_token_issues = 0
            episode_memory_issues = 0

            # 执行回合
            while step_count < max_steps:
                step_count += 1

                # 获取就绪任务
                ready_tasks = data_loader.get_ready_tasks()
                if not ready_tasks:
                    break

                ready_mask = np.zeros(config.SEQ_LEN)
                for i, _ in enumerate(ready_tasks[:config.SEQ_LEN]):
                    if i < config.SEQ_LEN:
                        ready_mask[i] = 1.0

                # 使用随机动作进行测试
                action = np.random.uniform(-1, 1, 2)

                # 执行动作
                try:
                    next_state, reward, done, info = data_loader.step_enhanced_task(action, debug=False)

                    # 检查token问题
                    executed_task = info.get('executed_task', -1)
                    if executed_task >= 0:
                        predicted_tokens = data_loader._task_predicted_output_tokens.get(executed_task, 0)
                        if predicted_tokens > 2000 or predicted_tokens <= 0:
                            episode_token_issues += 1

                    # 检查内存问题
                    failed_task = info.get('failed_task', -1)
                    if failed_task >= 0 and info.get('reason') == 'memory_insufficient':
                        episode_memory_issues += 1

                    if done:
                        break

                    state = next_state

                except Exception as e:
                    print(f"    ❌ Episode {episode} 执行错误: {e}")
                    failed_episodes += 1
                    break

            # 记录结果
            if 'info' in locals() and info is not None:
                completed_tasks_data = info.get('completed_tasks', set())
                completed_tasks = len(completed_tasks_data) if hasattr(completed_tasks_data, '__len__') else 0
                completion_rate = completed_tasks / num_tasks
                makespan = info.get('total_time', 0) / 1000.0

                completion_rates.append(completion_rate)
                makespans.append(makespan if completion_rate >= 1.0 else 0.0)

                if completion_rate >= 1.0:
                    successful_episodes += 1

                token_issues += episode_token_issues
                memory_issues += episode_memory_issues

                # 打印进度
                if episode % 10 == 0 or completion_rate >= 1.0:
                    print(f"Episode {episode:3d}: DAG {dag_idx}, "
                          f"完成率={completion_rate:.2f}, "
                          f"Makespan={makespan:.2f}s, "
                          f"Token问题={episode_token_issues}, "
                          f"内存问题={episode_memory_issues}")

        except Exception as e:
            print(f"Episode {episode} 初始化错误: {e}")
            failed_episodes += 1
            continue

    # 计算统计结果
    if completion_rates:
        avg_completion_rate = np.mean(completion_rates)
        perfect_completion_rate = successful_episodes / num_test_episodes
        valid_makespans = [m for m in makespans if m > 0]
        avg_makespan = np.mean(valid_makespans) if valid_makespans else 0.0

        print(f"\n📊 测试结果统计:")
        print(f"  总测试回合: {num_test_episodes}")
        print(f"  成功回合: {num_test_episodes - failed_episodes}")
        print(f"  失败回合: {failed_episodes}")
        print(f"  平均完成率: {avg_completion_rate:.3f}")
        print(f"  100%完成回合: {successful_episodes}")
        print(f"  100%完成率: {perfect_completion_rate:.3f}")
        print(f"  平均Makespan (100%完成): {avg_makespan:.3f}秒")
        print(f"  Token问题总数: {token_issues}")
        print(f"  内存问题总数: {memory_issues}")

        # 分析问题
        print(f"\n🔍 问题分析:")
        if perfect_completion_rate < 1.0:
            print(f"  ⚠️  仍有{(1-perfect_completion_rate)*100:.1f}%的DAG未能100%完成")
            if token_issues > 0:
                print(f"  🔸 Token问题: {token_issues}次 (可能影响LLM任务执行)")
            if memory_issues > 0:
                print(f"  🔸 内存问题: {memory_issues}次 (可能导致任务失败)")
            if failed_episodes > 0:
                print(f"  🔸 执行失败: {failed_episodes}次 (系统错误)")
        else:
            print(f"  ✅ 所有DAG都达到了100%完成率！")

        # 给出建议
        print(f"\n💡 修复建议:")
        if token_issues > 0:
            print(f"  - 检查token数读取逻辑，确保从文件正确读取")
        if memory_issues > 0:
            print(f"  - 检查内存释放逻辑，确保已完成任务及时释放内存")
        if perfect_completion_rate < 0.95:
            print(f"  - 可能需要进一步调试调度逻辑或奖励函数")
        elif perfect_completion_rate >= 0.95:
            print(f"  - 修复效果良好，可以进行正式训练")

        return {
            'avg_completion_rate': avg_completion_rate,
            'perfect_completion_rate': perfect_completion_rate,
            'avg_makespan': avg_makespan,
            'token_issues': token_issues,
            'memory_issues': memory_issues,
            'failed_episodes': failed_episodes
        }
    else:
        print(f"\n❌ 测试失败：没有成功的回合")
        return None

if __name__ == "__main__":
    # 测试数据路径 - 使用实际存在的数据集
    data_path = "../dataset/training/DAG_30_edges_5_mem_38-64GB_density_0.4_0.8"

    if not os.path.exists(data_path):
        print(f"❌ 数据路径不存在: {data_path}")
        print("尝试查找其他可用的数据集...")

        # 尝试其他可能的路径
        alternative_paths = [
            "../dataset/training/DAG_30_edges_5_mem_115-128GB_density_0.4_0.8",
            "../dataset/training/DAG_30_edges_5_mem_115-128GB_density_0.2_0.6",
            "../dataset/training/1",  # 使用编号数据集
            "../dataset/training/2"
        ]

        for alt_path in alternative_paths:
            if os.path.exists(alt_path):
                data_path = alt_path
                print(f"✅ 找到可用数据集: {data_path}")
                break
        else:
            print("❌ 未找到可用的数据集")
            sys.exit(1)

    # 运行测试
    results = test_completion_rate_fix(data_path, num_test_episodes=50, device_num=5)

    if results:
        print(f"\n🎉 测试完成！")
        if results['perfect_completion_rate'] >= 0.95:
            print(f"✅ 修复效果良好，可以进行训练")
        else:
            print(f"⚠️  仍需进一步修复")
