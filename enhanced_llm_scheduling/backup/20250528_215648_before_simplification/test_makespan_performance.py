#!/usr/bin/env python3
"""
测试已训练模型的Makespan性能
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
import time
import torch

# 添加路径
sys.path.append('..')
from enhanced_data_loader import EnhancedDataLoader
from lstm_sac_agent import LSTMSACAgent
from xlstm_sac_agent import xLSTMSACAgent
import enhanced_config as config

def test_model_makespan(model_path, model_type, device_num, num_episodes=50):
    """测试单个模型的makespan性能"""
    
    print(f"🔍 测试 {model_type}-{device_num}设备模型...")
    
    # 设置配置
    config.EDGE_NUM = device_num
    config.ENHANCED_STATE_DIM = 5 + 1 + device_num*4 + 6  # 动态调整状态维度
    
    # 初始化数据加载器
    data_path = f"../dataset/testing/DAG_30_edges_{device_num}_mem_25-38GB_density_0.4_0.6"
    if not os.path.exists(data_path):
        print(f"❌ 测试数据集不存在: {data_path}")
        return None
    
    data_loader = EnhancedDataLoader(data_path)
    
    # 加载模型
    try:
        if model_type == "LSTM":
            agent = LSTMSACAgent(
                state_dim=config.ENHANCED_STATE_DIM,
                action_dim=2,
                hidden_dim=config.HIDDEN_DIM,
                lr=config.LEARNING_RATE
            )
        else:  # xLSTM
            agent = xLSTMSACAgent(
                state_dim=config.ENHANCED_STATE_DIM,
                action_dim=2,
                hidden_dim=config.HIDDEN_DIM,
                lr=config.LEARNING_RATE
            )
        
        agent.load_models(model_path)
        print(f"✅ 模型加载成功")
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return None
    
    # 测试性能
    makespans = []
    completion_rates = []
    rewards = []
    
    for episode in range(num_episodes):
        try:
            # 加载DAG数据
            dag_idx = episode % 200
            task_features = data_loader.load_task_features(dag_idx)
            adj_matrix = data_loader.load_adjacency_matrix(dag_idx)
            machine_resources = data_loader.load_machines_resource()
            comm_speed = data_loader.load_machine_commu_speed()
            memory_status = data_loader.load_memory_status()
            num_tasks = task_features.shape[1]
            
            # 初始化环境
            data_loader._init_step_state(task_features, adj_matrix, machine_resources, comm_speed, memory_status)
            
            # 获取初始状态
            state = data_loader.process_state_as_enhanced_sequence(
                task_features, adj_matrix, machine_resources, comm_speed, memory_status)
            
            # 执行调度
            step_count = 0
            max_steps = num_tasks * 2
            episode_reward = 0
            
            while step_count < max_steps:
                step_count += 1
                
                # 获取就绪任务掩码
                ready_tasks = data_loader.get_ready_tasks()
                ready_mask = np.zeros(config.SEQ_LEN)
                for i, task_id in enumerate(ready_tasks[:config.SEQ_LEN]):
                    if i < config.SEQ_LEN:
                        ready_mask[i] = 1.0
                
                # 选择动作（确定性）
                action = agent.select_action(state, ready_mask, deterministic=True)
                
                # 执行动作
                next_state, reward, done, info = data_loader.step_enhanced_task(action)
                
                # 更新状态
                state = next_state
                episode_reward += reward
                
                if done:
                    break
            
            # 记录结果
            completed_tasks = len(info.get('completed_tasks', set()))
            completion_rate = completed_tasks / num_tasks
            
            # 获取makespan
            if data_loader._step_state is not None and 'total_completion_time' in data_loader._step_state:
                makespan = data_loader._step_state['total_completion_time'] / 1000.0  # 转换为秒
            else:
                makespan = info.get('total_time', 0) / 1000.0
            
            makespans.append(makespan)
            completion_rates.append(completion_rate)
            rewards.append(episode_reward)
            
            if episode % 10 == 0:
                print(f"  Episode {episode}: Makespan={makespan:.2f}s, Completion={completion_rate:.2%}")
                
        except Exception as e:
            print(f"❌ Episode {episode} 测试失败: {e}")
            continue
    
    if makespans:
        results = {
            'model_type': model_type,
            'device_num': device_num,
            'avg_makespan': np.mean(makespans),
            'min_makespan': np.min(makespans),
            'max_makespan': np.max(makespans),
            'std_makespan': np.std(makespans),
            'avg_completion': np.mean(completion_rates),
            'avg_reward': np.mean(rewards),
            'num_episodes': len(makespans)
        }
        
        print(f"📊 {model_type}-{device_num}设备结果:")
        print(f"  平均Makespan: {results['avg_makespan']:.2f}s")
        print(f"  最佳Makespan: {results['min_makespan']:.2f}s")
        print(f"  平均完成率: {results['avg_completion']:.2%}")
        
        return results
    else:
        print(f"❌ 没有有效的测试结果")
        return None

def main():
    """主函数"""
    
    print("🚀 开始测试模型Makespan性能...")
    
    # 查找所有final模型
    models_dir = Path("models")
    final_models = list(models_dir.glob("*_final_*"))
    
    results = []
    
    for model_dir in final_models:
        try:
            model_name = model_dir.name
            
            # 解析模型信息
            if "lstm_sac" in model_name:
                model_type = "LSTM"
            elif "xlstm_sac" in model_name:
                model_type = "xLSTM"
            else:
                continue
            
            # 提取设备数
            import re
            device_match = re.search(r'(\d+)devices', model_name)
            if device_match:
                device_num = int(device_match.group(1))
            else:
                continue
            
            # 查找模型文件
            if model_type == "LSTM":
                model_file = model_dir / "lstm_sac_model.pth"
            else:
                model_file = model_dir / "xlstm_sac_model.pth"
            
            if not model_file.exists():
                print(f"❌ 模型文件不存在: {model_file}")
                continue
            
            # 测试模型
            result = test_model_makespan(str(model_file), model_type, device_num)
            if result:
                results.append(result)
                
        except Exception as e:
            print(f"❌ 处理模型 {model_dir.name} 失败: {e}")
            continue
    
    # 保存结果
    if results:
        results_df = pd.DataFrame(results)
        results_df = results_df.sort_values(['device_num', 'model_type'])
        results_df.to_excel("makespan_performance_results.xlsx", index=False)
        
        # 绘制对比图
        plot_makespan_comparison(results_df)
        
        # 打印汇总
        print(f"\n📊 Makespan性能汇总:")
        print("=" * 80)
        
        for device_num in sorted(results_df['device_num'].unique()):
            device_models = results_df[results_df['device_num'] == device_num]
            print(f"\n🔧 {device_num}设备:")
            
            for _, row in device_models.iterrows():
                print(f"  {row['model_type']:>5}: "
                      f"平均Makespan={row['avg_makespan']:>6.1f}s, "
                      f"最佳Makespan={row['min_makespan']:>6.1f}s, "
                      f"完成率={row['avg_completion']:>6.1%}")
        
        print(f"\n✅ 结果已保存到 makespan_performance_results.xlsx")
        
    else:
        print("❌ 没有找到有效的测试结果")

def plot_makespan_comparison(results_df):
    """绘制makespan对比图"""
    
    plt.style.use('ggplot')
    plt.rcParams['font.family'] = 'DejaVu Sans'
    plt.rcParams['font.size'] = 12
    
    device_nums = sorted(results_df['device_num'].unique())
    model_types = sorted(results_df['model_type'].unique())
    
    # 1. 平均Makespan对比
    plt.figure(figsize=(12, 8))
    
    for model_type in model_types:
        model_data = results_df[results_df['model_type'] == model_type]
        makespans = []
        
        for device_num in device_nums:
            device_data = model_data[model_data['device_num'] == device_num]
            if len(device_data) > 0:
                makespans.append(device_data['avg_makespan'].iloc[0])
            else:
                makespans.append(None)
        
        # 过滤None值
        valid_devices = [d for d, m in zip(device_nums, makespans) if m is not None]
        valid_makespans = [m for m in makespans if m is not None]
        
        if valid_makespans:
            plt.plot(valid_devices, valid_makespans, 'o-', linewidth=2, 
                    label=f'{model_type} SAC', markersize=8)
    
    plt.xlabel('Number of Edge Devices')
    plt.ylabel('Average Makespan (seconds)')
    plt.title('Model Performance Comparison: Makespan vs Device Number')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig('makespan_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. 最佳Makespan对比
    plt.figure(figsize=(12, 8))
    
    for model_type in model_types:
        model_data = results_df[results_df['model_type'] == model_type]
        makespans = []
        
        for device_num in device_nums:
            device_data = model_data[model_data['device_num'] == device_num]
            if len(device_data) > 0:
                makespans.append(device_data['min_makespan'].iloc[0])
            else:
                makespans.append(None)
        
        # 过滤None值
        valid_devices = [d for d, m in zip(device_nums, makespans) if m is not None]
        valid_makespans = [m for m in makespans if m is not None]
        
        if valid_makespans:
            plt.plot(valid_devices, valid_makespans, 's-', linewidth=2, 
                    label=f'{model_type} SAC (Best)', markersize=8)
    
    plt.xlabel('Number of Edge Devices')
    plt.ylabel('Best Makespan (seconds)')
    plt.title('Model Performance Comparison: Best Makespan vs Device Number')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig('best_makespan_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("📈 Makespan对比图已生成:")
    print("  📊 makespan_comparison.png")
    print("  📊 best_makespan_comparison.png")

if __name__ == "__main__":
    main()
