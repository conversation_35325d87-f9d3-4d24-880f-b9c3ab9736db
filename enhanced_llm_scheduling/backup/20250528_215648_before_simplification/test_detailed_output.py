#!/usr/bin/env python3
"""
测试详细调度信息输出
验证enhanced_data_loader中的详细打印功能
"""

import os
import sys
import numpy as np

# 添加路径
sys.path.append('..')
sys.path.append('.')

from enhanced_data_loader import <PERSON>hancedDataLoader
from enhanced_config import EnhancedLLMConfig

def test_detailed_output():
    """测试详细输出功能"""
    print("🧪 测试详细调度信息输出")
    print("=" * 80)
    
    # 设置参数
    device_num = 2
    enhanced_config = EnhancedLLMConfig()
    enhanced_config.NUM_EDGE_SERVERS = device_num
    enhanced_config.update_edge_servers(device_num)
    
    print(f"📊 配置信息:")
    print(f"  边缘设备数: {device_num}")
    print(f"  状态维度: {enhanced_config.ENHANCED_STATE_DIM}")
    
    # 创建数据加载器（启用debug模式）
    data_loader = EnhancedDataLoader(enhanced_config, debug=True)
    
    # 创建模拟数据
    print(f"\n🔧 创建模拟DAG数据...")
    num_tasks = 6
    
    # 创建任务特征
    task_features = np.zeros((5, num_tasks))
    task_features[0, :] = np.random.uniform(1e9, 3e9, num_tasks)  # CPU周期
    task_features[1, :] = np.random.uniform(10, 50, num_tasks)   # 数据大小MB
    task_features[2, :] = np.random.choice([0, 1], num_tasks, p=[0.7, 0.3])  # 30% LLM任务
    task_features[3, :] = np.random.uniform(100, 1000, num_tasks)  # Token数
    task_features[4, :] = np.random.uniform(2, 6, num_tasks)      # 内存需求GB
    
    # 创建简单的链式依赖
    adj_matrix = np.zeros((num_tasks, num_tasks))
    for i in range(num_tasks - 1):
        adj_matrix[i, i + 1] = 1
    
    # 创建机器资源
    machine_resources = np.array([3e9, 4e9, 5e9])  # 用户设备 + 2个边缘设备
    
    # 创建通信速度矩阵
    comm_speed = np.array([
        [0, 50, 60],
        [50, 0, 80],
        [60, 80, 0]
    ])
    
    # 创建初始内存状态
    memory_status = np.array([16.0, 28.0, 30.0])  # GB
    
    print(f"📋 DAG信息:")
    print(f"  任务数量: {num_tasks}")
    llm_count = sum(1 for i in range(num_tasks) if task_features[2, i] == 1)
    print(f"  LLM任务数: {llm_count}/{num_tasks}")
    
    # 打印任务详情
    print(f"\n📝 任务详情:")
    for i in range(num_tasks):
        is_llm = task_features[2, i] == 1
        cpu_cycles = task_features[0, i] / 1e9
        data_size = task_features[1, i]
        tokens = task_features[3, i]
        memory = task_features[4, i]
        
        print(f"  任务{i}: {'LLM' if is_llm else '普通'}, "
              f"CPU={cpu_cycles:.1f}G周期, 数据={data_size:.1f}MB, "
              f"Token={tokens:.0f}, 内存={memory:.1f}GB")
    
    print(f"\n🎯 开始详细调度测试...")
    
    try:
        # 初始化环境
        data_loader._init_step_state(task_features, adj_matrix, machine_resources, comm_speed, memory_status)
        
        # 执行几个调度步骤
        step_count = 0
        max_steps = 8
        
        while step_count < max_steps:
            step_count += 1
            
            print(f"\n{'='*60}")
            print(f"🔄 执行步骤 {step_count}")
            print(f"{'='*60}")
            
            # 获取就绪任务
            ready_tasks = data_loader.get_ready_tasks()
            if not ready_tasks:
                print("没有就绪任务，测试结束")
                break
            
            print(f"当前就绪任务: {ready_tasks}")
            
            # 模拟智能体选择动作
            # 选择第一个就绪任务，随机选择机器
            task_choice_action = 0.0  # 选择第一个就绪任务
            machine_choice_action = np.random.uniform(-1, 1)  # 随机选择机器
            
            compound_action = [task_choice_action, machine_choice_action]
            
            print(f"模拟动作: 任务选择={task_choice_action:.3f}, 机器选择={machine_choice_action:.3f}")
            
            # 执行动作（启用debug模式）
            try:
                next_state, reward, done, info = data_loader.step_enhanced_task(
                    compound_action, debug=True
                )
                
                print(f"\n📈 步骤结果:")
                print(f"  奖励: {reward:.2f}")
                print(f"  是否完成: {done}")
                
                if done:
                    completed_tasks = len(info.get('completed_tasks', set()))
                    total_time = info.get('total_time', 0) / 1000.0
                    print(f"\n🏁 DAG执行完成!")
                    print(f"  完成任务: {completed_tasks}/{num_tasks}")
                    print(f"  总时间: {total_time:.3f}秒")
                    break
                
            except Exception as e:
                print(f"❌ 执行错误: {e}")
                break
        
        print(f"\n✅ 详细输出测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = test_detailed_output()
    
    if success:
        print(f"\n🎉 详细调度信息输出功能正常工作!")
        print(f"\n📝 现在训练时会显示以下详细信息:")
        print(f"  ✅ 调度决策详情 (任务选择、机器分配)")
        print(f"  ✅ 内存状态监控 (分配前后、利用率)")
        print(f"  ✅ 任务执行详情 (时间分解、位置)")
        print(f"  ✅ 内存释放过程 (释放量、剩余)")
        print(f"  ✅ 奖励计算分解 (各组件贡献)")
        print(f"  ✅ 调度结果总结 (进度、状态)")
        
        print(f"\n🚀 使用方法:")
        print(f"  在任何训练脚本中调用 step_enhanced_task(action, debug=True)")
        print(f"  或者在 EnhancedDataLoader 初始化时设置 debug=True")
        
        return True
    else:
        print(f"\n❌ 测试失败，请检查问题。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
