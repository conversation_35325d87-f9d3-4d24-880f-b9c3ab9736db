#!/usr/bin/env python3
"""
演示详细训练信息打印功能
展示调度决策过程和内存占用的详细信息
"""

import os
import sys
import time
import numpy as np
import torch

# 添加路径
sys.path.append('..')
sys.path.append('.')

from enhanced_data_loader import <PERSON>hanced<PERSON><PERSON>Loader
from improved_xlstm_networks import ImprovedXLSTMSACAgent
from improved_reward_function import ImprovedRewardFunction
from enhanced_config import EnhancedLLMConfig
from train_improved_xlstm import calculate_memory_utilization, calculate_load_balance_score

def demo_detailed_training():
    """演示详细训练过程"""
    print("🚀 改进的xLSTM调度系统 - 详细训练演示")
    print("=" * 80)
    
    # 设置参数
    device_num = 2
    enhanced_config = EnhancedLLMConfig()
    enhanced_config.NUM_EDGE_SERVERS = device_num
    enhanced_config.update_edge_servers(device_num)
    
    print(f"📊 系统配置:")
    print(f"  边缘设备数: {device_num}")
    print(f"  状态维度: {enhanced_config.ENHANCED_STATE_DIM}")
    print(f"  序列长度: {enhanced_config.SEQ_LEN}")
    
    # 创建模拟数据
    print(f"\n🔧 创建模拟DAG数据...")
    num_tasks = 8
    
    # 创建任务特征 [CPU, 数据大小, 是否LLM, Token数, 内存需求]
    task_features = np.zeros((5, num_tasks))
    task_features[0, :] = np.random.uniform(1e9, 5e9, num_tasks)  # CPU周期
    task_features[1, :] = np.random.uniform(10, 100, num_tasks)   # 数据大小MB
    task_features[2, :] = np.random.choice([0, 1], num_tasks, p=[0.6, 0.4])  # 40% LLM任务
    task_features[3, :] = np.random.uniform(100, 2000, num_tasks)  # Token数
    task_features[4, :] = np.random.uniform(2, 8, num_tasks)      # 内存需求GB
    
    # 创建邻接矩阵（简单的链式依赖）
    adj_matrix = np.zeros((num_tasks, num_tasks))
    for i in range(num_tasks - 2):
        adj_matrix[i, i + 1] = 1  # 任务i依赖于任务i+1
    
    # 添加一些分支
    if num_tasks >= 6:
        adj_matrix[1, 4] = 1  # 任务1也依赖于任务4
        adj_matrix[2, 5] = 1  # 任务2也依赖于任务5
    
    # 创建机器资源
    machine_resources = np.array([3e9, 4e9, 5e9])  # 用户设备 + 2个边缘设备
    
    # 创建通信速度矩阵
    comm_speed = np.array([
        [0, 50, 60],    # 从用户设备到各设备的上传速度
        [50, 0, 80],    # 从边缘1到各设备的速度
        [60, 80, 0]     # 从边缘2到各设备的速度
    ])
    
    # 创建初始内存状态
    memory_status = np.array([16.0, 28.0, 30.0])  # GB
    
    print(f"📋 DAG信息:")
    print(f"  任务数量: {num_tasks}")
    llm_count = sum(1 for i in range(num_tasks) if task_features[2, i] == 1)
    print(f"  LLM任务数: {llm_count}/{num_tasks}")
    print(f"  机器配置: 用户设备(3GHz), 边缘1(4GHz), 边缘2(5GHz)")
    print(f"  初始内存: {memory_status} GB")
    
    # 创建数据加载器
    data_loader = EnhancedDataLoader(enhanced_config, debug=True)
    
    # 创建智能体
    agent = ImprovedXLSTMSACAgent(
        state_dim=enhanced_config.ENHANCED_STATE_DIM,
        action_dim=2,
        hidden_dim=128,
        seq_len=enhanced_config.SEQ_LEN,
        num_machines=device_num + 1,
        xlstm_layers=1,
        lr=3e-4,
        device='cpu'
    )
    
    # 创建奖励函数
    reward_function = ImprovedRewardFunction(
        max_makespan=200.0,
        completion_weight=15.0,
        makespan_weight=10.0,
        efficiency_weight=3.0,
        penalty_weight=25.0
    )
    
    print(f"\n🎯 开始详细调度演示...")
    
    # 初始化环境
    data_loader._init_step_state(task_features, adj_matrix, machine_resources, comm_speed, memory_status)
    
    # 获取初始状态
    state = data_loader.process_state_as_enhanced_sequence(
        task_features, adj_matrix, machine_resources, comm_speed, memory_status)
    
    print(f"\n🔍 初始状态:")
    print(f"  状态形状: {state.shape}")
    print(f"  初始就绪任务: {data_loader.get_ready_tasks()}")
    
    # 打印任务详情
    print(f"\n📝 任务详情:")
    for i in range(num_tasks):
        is_llm = task_features[2, i] == 1
        cpu_cycles = task_features[0, i] / 1e9
        data_size = task_features[1, i]
        tokens = task_features[3, i]
        memory = task_features[4, i]
        
        print(f"  任务{i}: {'LLM' if is_llm else '普通'}, "
              f"CPU={cpu_cycles:.1f}G周期, 数据={data_size:.1f}MB, "
              f"Token={tokens:.0f}, 内存={memory:.1f}GB")
    
    # 打印依赖关系
    print(f"\n🔗 任务依赖关系:")
    for i in range(num_tasks):
        predecessors = [j for j in range(num_tasks) if adj_matrix[j, i] == 1]
        if predecessors:
            print(f"  任务{i} 依赖于: {predecessors}")
        else:
            print(f"  任务{i}: 无依赖（初始就绪）")
    
    # 执行调度过程
    step_count = 0
    total_reward = 0
    start_time = time.time()
    
    while step_count < num_tasks * 2:  # 最多执行2倍任务数的步骤
        step_count += 1
        
        print(f"\n{'='*80}")
        print(f"🔄 执行步骤 {step_count}")
        print(f"{'='*80}")
        
        # 获取就绪任务
        ready_tasks = data_loader.get_ready_tasks()
        if not ready_tasks:
            print("    没有就绪任务，调度结束")
            break
        
        completed_tasks = data_loader._step_state.get('completed_tasks', set())
        failed_tasks = data_loader._step_state.get('failed_tasks', set())
        
        print(f"📊 当前状态:")
        print(f"  就绪任务: {ready_tasks}")
        print(f"  已完成: {len(completed_tasks)}/{num_tasks}")
        print(f"  失败任务: {len(failed_tasks)}")
        
        # 打印详细的任务状态
        print(f"\n📋 任务状态详情:")
        for task_idx in range(num_tasks):
            status = data_loader._task_status.get(task_idx, 0)
            status_names = ["未就绪", "就绪", "运行中", "已完成"]
            predicted_memory = data_loader._task_predicted_memory.get(task_idx, 0)
            predicted_tokens = data_loader._task_predicted_output_tokens.get(task_idx, 0)
            is_llm = task_features[2, task_idx] == 1
            
            status_icon = "⏳" if status == 0 else "✅" if status == 1 else "🔄" if status == 2 else "✔️"
            
            print(f"    {status_icon} 任务{task_idx}: {status_names[status]}, "
                  f"{'LLM' if is_llm else '普通'}, "
                  f"预测内存{predicted_memory:.2f}GB, "
                  f"预测输出{predicted_tokens:.0f}tokens")
        
        # 打印当前内存状态
        current_memory = data_loader._step_state.get('memory_status', [])
        machine_times = data_loader._step_state.get('machine_finish_time', [])
        
        print(f"\n💾 内存和负载状态:")
        for i, (mem, finish_time) in enumerate(zip(current_memory, machine_times)):
            machine_type = "用户设备" if i == 0 else f"边缘设备{i}"
            utilization = (32.0 - mem) / 32.0 * 100  # 假设总内存32GB
            print(f"    {machine_type}: {mem:.2f}GB可用 ({utilization:.1f}%已用), "
                  f"完成时间{finish_time/1000:.2f}s")
        
        # 计算系统指标
        memory_util = calculate_memory_utilization(current_memory)
        load_balance = calculate_load_balance_score(machine_times)
        
        print(f"\n📈 系统性能指标:")
        print(f"    总体内存利用率: {memory_util:.1%}")
        print(f"    负载均衡分数: {load_balance:.3f}")
        
        # 创建就绪掩码
        ready_mask = np.zeros(enhanced_config.SEQ_LEN)
        for i, _ in enumerate(ready_tasks[:enhanced_config.SEQ_LEN]):
            if i < enhanced_config.SEQ_LEN:
                ready_mask[i] = 1.0
        
        # 转换为tensor
        state_tensor = torch.FloatTensor(state).unsqueeze(0)
        ready_mask_tensor = torch.FloatTensor(ready_mask).unsqueeze(0)
        
        # 智能体决策
        action = agent.select_action(state_tensor, ready_mask_tensor, deterministic=False)
        
        # 映射动作到具体选择
        task_choice_idx = int(((action[0] + 1) / 2) * (len(ready_tasks) - 1))
        task_choice_idx = max(0, min(task_choice_idx, len(ready_tasks) - 1))
        selected_task_idx = ready_tasks[task_choice_idx]
        
        machine_idx = int(((action[1] + 1) / 2) * (len(machine_resources) - 1))
        machine_idx = max(0, min(machine_idx, len(machine_resources) - 1))
        
        machine_names = ["用户设备", "边缘设备1", "边缘设备2"]
        
        print(f"\n🎯 智能体决策:")
        print(f"    原始动作: 任务选择={action[0]:.3f}, 机器选择={action[1]:.3f}")
        print(f"    选择任务: 任务{selected_task_idx} (就绪队列第{task_choice_idx}个)")
        print(f"    分配机器: {machine_names[machine_idx]} (索引{machine_idx})")
        
        # 预测执行结果
        predicted_memory = data_loader._task_predicted_memory.get(selected_task_idx, 0)
        available_memory = current_memory[machine_idx]
        
        if predicted_memory > available_memory:
            print(f"    ⚠️  内存不足警告: 需要{predicted_memory:.2f}GB, 可用{available_memory:.2f}GB")
        else:
            print(f"    ✅ 内存充足: 需要{predicted_memory:.2f}GB, 可用{available_memory:.2f}GB")
        
        # 执行动作
        try:
            next_state, _, done, info = data_loader.step_enhanced_task(action, debug=False)
            
            # 打印执行结果
            executed_task = info.get('executed_task', -1)
            if executed_task >= 0:
                execution_time = info.get('execution_time', 0) / 1000.0
                print(f"\n✅ 执行成功:")
                print(f"    执行任务: 任务{executed_task}")
                print(f"    执行时间: {execution_time:.3f}秒")
                print(f"    累计时间: {data_loader._step_state.get('total_completion_time', 0)/1000:.3f}秒")
                
                newly_ready = info.get('newly_ready_tasks', [])
                if newly_ready:
                    print(f"    🆕 新增就绪任务: {newly_ready}")
            
            failed_task = info.get('failed_task', -1)
            if failed_task >= 0:
                reason = info.get('reason', 'unknown')
                print(f"\n❌ 执行失败:")
                print(f"    失败任务: 任务{failed_task}")
                print(f"    失败原因: {reason}")
            
            # 计算奖励
            step_info = {
                'valid_action': True,
                'task_completed': executed_task >= 0,
                'memory_utilization': memory_util,
                'load_balance_score': load_balance
            }
            
            reward, reward_components = reward_function.calculate_reward(step_info)
            total_reward += reward
            
            print(f"\n💰 奖励分析:")
            print(f"    步骤奖励: {reward:.2f}")
            print(f"    累计奖励: {total_reward:.2f}")
            if reward_components:
                print(f"    奖励组件:")
                for comp_name, comp_value in reward_components.items():
                    if comp_value != 0:
                        print(f"      {comp_name}: {comp_value:.2f}")
            
            if done:
                print(f"\n🏁 DAG执行完成!")
                completed_count = len(info.get('completed_tasks', set()))
                makespan = data_loader._step_state.get('total_completion_time', 0) / 1000.0
                
                # 计算最终奖励
                episode_info = {
                    'total_tasks': num_tasks,
                    'completed_tasks': completed_count,
                    'makespan': makespan,
                    'failed_tasks': num_tasks - completed_count,
                    'deadlock_occurred': info.get('deadlock_detected', False)
                }
                
                final_reward, final_components = reward_function.calculate_reward({}, episode_info)
                total_reward += final_reward
                
                print(f"\n📊 最终统计:")
                print(f"    完成任务: {completed_count}/{num_tasks} ({completed_count/num_tasks:.1%})")
                print(f"    总Makespan: {makespan:.2f}秒")
                print(f"    最终奖励: {final_reward:.2f}")
                print(f"    总奖励: {total_reward:.2f}")
                print(f"    执行效率: {completed_count/makespan:.2f} 任务/秒")
                
                if final_components:
                    print(f"    最终奖励组件:")
                    for comp_name, comp_value in final_components.items():
                        if comp_value != 0:
                            print(f"      {comp_name}: {comp_value:.2f}")
                break
            
            state = next_state
            
        except Exception as e:
            print(f"\n❌ 执行错误: {e}")
            break
        
        # 添加小延迟以便观察
        time.sleep(0.5)
    
    total_time = time.time() - start_time
    print(f"\n⏱️  演示总用时: {total_time:.2f}秒")
    
    print(f"\n🎉 详细调度演示完成!")
    print(f"\n📝 演示展示的功能:")
    print(f"  ✅ 实时任务状态跟踪")
    print(f"  ✅ 详细内存占用监控")
    print(f"  ✅ 智能体决策过程可视化")
    print(f"  ✅ 系统性能指标计算")
    print(f"  ✅ 奖励函数组件分析")
    print(f"  ✅ 调度结果统计")

def main():
    """主函数"""
    try:
        demo_detailed_training()
        print(f"\n🚀 现在可以使用详细训练模式:")
        print(f"python train_improved_xlstm.py --episodes 100 --device_num 2")
        print(f"  (每100个episode显示详细信息)")
        return True
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
