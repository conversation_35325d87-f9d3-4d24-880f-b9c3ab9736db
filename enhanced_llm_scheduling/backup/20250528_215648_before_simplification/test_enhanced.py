"""
增强LLM调度系统测试脚本
"""
import torch
import numpy as np
import argparse
import os
import sys
import time
import pandas as pd
from datetime import datetime

# 添加路径
sys.path.append('..')
sys.path.append('.')

from enhanced_config import Enhanced<PERSON>MConfig
from enhanced_data_loader import <PERSON>hancedDataLoader
from enhanced_sac_agent import <PERSON>hancedSACA<PERSON>

def create_ready_mask(data_loader, seq_len):
    """创建就绪任务掩码"""
    ready_tasks = data_loader.get_ready_tasks()
    ready_mask = np.zeros(seq_len)
    
    for task_idx in ready_tasks:
        if task_idx < seq_len:
            ready_mask[task_idx] = 1.0
    
    return ready_mask

def test_baseline_algorithms(data_loader, task_features, adj_matrix, machine_resources, 
                           comm_speed, memory_status, num_tasks):
    """测试基线算法"""
    baseline_results = {}
    
    # 1. 随机调度
    try:
        data_loader._step_state = None
        data_loader._init_step_state(task_features, adj_matrix, machine_resources, 
                                   comm_speed, memory_status.copy())
        
        step_count = 0
        max_steps = num_tasks * 2
        
        while step_count < max_steps:
            ready_tasks = data_loader.get_ready_tasks()
            if not ready_tasks:
                break
            
            # 随机选择任务和机器
            task_idx = np.random.choice(ready_tasks)
            machine_idx = np.random.randint(0, len(machine_resources))
            action = [0.0, (machine_idx / (len(machine_resources) - 1)) * 2 - 1]
            
            _, reward, done, info = data_loader.step_enhanced_task(action, debug=False)
            
            step_count += 1
            if done:
                break
        
        completed_tasks = len(info.get('completed_tasks', set()))
        total_time = info.get('total_time', 0)
        
        baseline_results['Random'] = {
            'completion_rate': completed_tasks / num_tasks,
            'total_time': total_time,
            'steps': step_count
        }
    except Exception as e:
        baseline_results['Random'] = {'completion_rate': 0, 'total_time': float('inf'), 'steps': 0}
    
    # 2. 贪心调度
    try:
        data_loader._step_state = None
        data_loader._init_step_state(task_features, adj_matrix, machine_resources, 
                                   comm_speed, memory_status.copy())
        
        step_count = 0
        max_steps = num_tasks * 2
        
        while step_count < max_steps:
            ready_tasks = data_loader.get_ready_tasks()
            if not ready_tasks:
                break
            
            # 选择第一个就绪任务，分配到最快的机器
            task_idx = ready_tasks[0]
            machine_idx = np.argmax(machine_resources)
            action = [0.0, (machine_idx / (len(machine_resources) - 1)) * 2 - 1]
            
            _, reward, done, info = data_loader.step_enhanced_task(action, debug=False)
            
            step_count += 1
            if done:
                break
        
        completed_tasks = len(info.get('completed_tasks', set()))
        total_time = info.get('total_time', 0)
        
        baseline_results['Greedy'] = {
            'completion_rate': completed_tasks / num_tasks,
            'total_time': total_time,
            'steps': step_count
        }
    except Exception as e:
        baseline_results['Greedy'] = {'completion_rate': 0, 'total_time': float('inf'), 'steps': 0}
    
    return baseline_results

def test_enhanced_sac_model(model_path, edge_num, task_num=200, test_episodes=100):
    """测试增强SAC模型"""
    
    print(f"\n{'='*60}")
    print(f"测试 {edge_num} 个边缘设备的模型: {model_path}")
    print(f"{'='*60}")
    
    # 创建配置
    config = EnhancedLLMConfig()
    config.update_edge_servers(edge_num, task_num)
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() and config.CUDA else 'cpu')
    
    # 初始化数据加载器和智能体
    data_loader = EnhancedDataLoader(config, debug=False)
    agent = EnhancedSACAgent(config, device)
    
    # 加载模型
    if os.path.exists(model_path):
        agent.load_models(model_path)
        print(f"成功加载模型: {model_path}")
    else:
        print(f"警告: 模型文件不存在: {model_path}")
        return None
    
    # 测试统计
    sac_rewards = []
    sac_completion_rates = []
    sac_times = []
    sac_steps = []
    
    baseline_results_all = []
    
    print(f"开始测试 {test_episodes} 个回合...")
    
    for episode in range(test_episodes):
        # 随机选择一个DAG
        dag_idx = np.random.randint(1, 201)
        
        try:
            # 加载DAG数据
            task_features = data_loader.load_task_features(dag_idx)
            adj_matrix = data_loader.load_adjacency_matrix(dag_idx)
            machine_resources = data_loader.load_machines_resource()
            comm_speed = data_loader.load_machine_commu_speed()
            memory_status = data_loader.load_memory_status()
            
            num_tasks = task_features.shape[1]
            
            # 测试SAC模型
            data_loader._step_state = None
            state = data_loader.process_state_as_enhanced_sequence(
                task_features, adj_matrix, machine_resources, comm_speed, memory_status.copy()
            )
            
            data_loader._init_step_state(task_features, adj_matrix, machine_resources, 
                                       comm_speed, memory_status.copy())
            
            ready_mask = create_ready_mask(data_loader, config.SEQ_LEN)
            
            episode_reward = 0
            step_count = 0
            max_steps = num_tasks * 2
            
            while step_count < max_steps:
                # 选择动作（确定性）
                action = agent.select_action(state, ready_mask, deterministic=True)
                
                # 执行动作
                next_state, reward, done, info = data_loader.step_enhanced_task(action, debug=False)
                
                # 更新状态
                state = next_state
                ready_mask = create_ready_mask(data_loader, config.SEQ_LEN)
                episode_reward += reward
                step_count += 1
                
                if done:
                    break
            
            # 记录SAC结果
            completed_tasks = len(info.get('completed_tasks', set()))
            completion_rate = completed_tasks / num_tasks
            total_time = info.get('total_time', 0)
            
            sac_rewards.append(episode_reward)
            sac_completion_rates.append(completion_rate)
            sac_times.append(total_time)
            sac_steps.append(step_count)
            
            # 测试基线算法（每10个回合测试一次）
            if episode % 10 == 0:
                baseline_results = test_baseline_algorithms(
                    data_loader, task_features, adj_matrix, machine_resources,
                    comm_speed, memory_status, num_tasks
                )
                baseline_results_all.append(baseline_results)
            
            # 打印进度
            if episode % 20 == 0:
                avg_completion = np.mean(sac_completion_rates[-20:])
                avg_time = np.mean(sac_times[-20:])
                print(f"Episode {episode:3d}: SAC完成率={completion_rate:.2f}, "
                      f"时间={total_time/1000:.2f}s, 平均完成率={avg_completion:.2f}")
        
        except Exception as e:
            print(f"Episode {episode} 测试失败: {e}")
            continue
    
    # 计算统计结果
    results = {
        'SAC': {
            'avg_reward': np.mean(sac_rewards),
            'avg_completion_rate': np.mean(sac_completion_rates),
            'avg_time': np.mean(sac_times),
            'avg_steps': np.mean(sac_steps),
            'std_completion_rate': np.std(sac_completion_rates),
            'std_time': np.std(sac_times)
        }
    }
    
    # 处理基线结果
    if baseline_results_all:
        for algorithm in ['Random', 'Greedy']:
            completion_rates = [r[algorithm]['completion_rate'] for r in baseline_results_all if algorithm in r]
            times = [r[algorithm]['total_time'] for r in baseline_results_all if algorithm in r]
            
            if completion_rates:
                results[algorithm] = {
                    'avg_completion_rate': np.mean(completion_rates),
                    'avg_time': np.mean(times),
                    'std_completion_rate': np.std(completion_rates),
                    'std_time': np.std(times)
                }
    
    # 打印结果
    print(f"\n{edge_num}个边缘设备测试结果:")
    print("-" * 50)
    for algorithm, stats in results.items():
        print(f"{algorithm}:")
        print(f"  平均完成率: {stats['avg_completion_rate']:.3f} ± {stats.get('std_completion_rate', 0):.3f}")
        print(f"  平均时间: {stats['avg_time']/1000:.2f} ± {stats.get('std_time', 0)/1000:.2f} 秒")
        if 'avg_reward' in stats:
            print(f"  平均奖励: {stats['avg_reward']:.2f}")
    
    return results

def main():
    parser = argparse.ArgumentParser(description='测试增强LLM调度系统')
    parser.add_argument('--model_path', type=str, required=True, help='模型文件路径')
    parser.add_argument('--edge_num', type=int, default=5, help='边缘设备数量')
    parser.add_argument('--task_num', type=int, default=200, help='最大任务数量')
    parser.add_argument('--test_episodes', type=int, default=100, help='测试回合数')
    
    args = parser.parse_args()
    
    print(f"测试配置:")
    print(f"  模型路径: {args.model_path}")
    print(f"  边缘设备数量: {args.edge_num}")
    print(f"  最大任务数量: {args.task_num}")
    print(f"  测试回合数: {args.test_episodes}")
    
    # 开始测试
    results = test_enhanced_sac_model(
        args.model_path, args.edge_num, args.task_num, args.test_episodes
    )
    
    if results:
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"test_results_{args.edge_num}devices_{timestamp}.csv"
        
        # 创建DataFrame
        data = []
        for algorithm, stats in results.items():
            data.append({
                '算法': algorithm,
                '平均完成率': f"{stats['avg_completion_rate']:.3f}",
                '平均时间(秒)': f"{stats['avg_time']/1000:.2f}",
                '完成率标准差': f"{stats.get('std_completion_rate', 0):.3f}"
            })
        
        df = pd.DataFrame(data)
        df.to_csv(results_file, index=False)
        print(f"\n结果已保存到: {results_file}")
    else:
        print("测试失败")

if __name__ == "__main__":
    main()
