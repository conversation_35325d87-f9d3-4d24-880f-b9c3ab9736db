#!/usr/bin/env python3
"""
综合测试脚本：xLSTM、LSTM、基线算法对比
按照原版代码实现
"""

import os
import sys
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')

# 添加路径
sys.path.append('..')
sys.path.append('.')

from enhanced_config import EnhancedLLMConfig
from enhanced_data_loader import EnhancedDataLoader
from lstm_sac_agent import LSTMSACAgent
from xlstm_sac_agent import XLSTMSACAgent
from enhanced_sac_agent import EnhancedSACAgent

def load_baseline_algorithms():
    """加载基线算法"""
    try:
        # 尝试导入原版基线算法
        sys.path.append('../')
        from baseline_algorithms import (
            random_offloading,
            round_robin_offloading,
            single_edge_execution,
            greedy_offloading
        )
        return {
            'random': random_offloading,
            'round_robin': round_robin_offloading,
            'single_edge': single_edge_execution,
            'greedy': greedy_offloading
        }
    except ImportError:
        print("⚠️ 原版基线算法不可用，使用简化版本")
        return create_simple_baselines()

def create_simple_baselines():
    """创建简化的基线算法"""

    def simple_random(data_loader, dag_idx):
        """简单随机算法"""
        start_time = time.time()

        # 加载DAG数据
        task_features = data_loader.load_task_features(dag_idx)
        adj_matrix = data_loader.load_adjacency_matrix(dag_idx)
        machine_resources = data_loader.load_machines_resource()
        comm_speed = data_loader.load_machine_commu_speed()
        memory_status = data_loader.load_memory_status()
        num_tasks = task_features.shape[1]

        # 初始化环境
        data_loader._init_step_state(task_features, adj_matrix, machine_resources, comm_speed, memory_status)

        completed_tasks = 0
        max_steps = num_tasks * 3

        for step in range(max_steps):
            ready_tasks = data_loader.get_ready_tasks()
            if not ready_tasks:
                break

            # 随机选择任务和机器
            task_choice = np.random.uniform(-1, 1)
            machine_choice = np.random.uniform(-1, 1)
            action = np.array([task_choice, machine_choice])

            try:
                next_state, reward, done, info = data_loader.step_enhanced_task(action, debug=False)
                if done:
                    completed_tasks = len(info.get('completed_tasks', set()))
                    break
            except Exception:
                break

        algorithm_time = time.time() - start_time
        completion_rate = completed_tasks / num_tasks

        # 获取真正的makespan（DAG完成时间）
        if data_loader._step_state is not None and 'total_completion_time' in data_loader._step_state:
            makespan = data_loader._step_state['total_completion_time'] / 1000.0  # 转换为秒
        else:
            makespan = 0.0

        return {
            'completion_rate': completion_rate,
            'makespan': makespan,
            'completed_tasks': completed_tasks,
            'algorithm_time': algorithm_time
        }

    def simple_round_robin(data_loader, dag_idx):
        """简单轮询算法"""
        start_time = time.time()

        # 加载DAG数据
        task_features = data_loader.load_task_features(dag_idx)
        adj_matrix = data_loader.load_adjacency_matrix(dag_idx)
        machine_resources = data_loader.load_machines_resource()
        comm_speed = data_loader.load_machine_commu_speed()
        memory_status = data_loader.load_memory_status()
        num_tasks = task_features.shape[1]
        num_machines = len(machine_resources)

        # 初始化环境
        data_loader._init_step_state(task_features, adj_matrix, machine_resources, comm_speed, memory_status)

        completed_tasks = 0
        max_steps = num_tasks * 3
        machine_counter = 0

        for step in range(max_steps):
            ready_tasks = data_loader.get_ready_tasks()
            if not ready_tasks:
                break

            # 选择第一个就绪任务
            task_choice = -1.0 + (2.0 / max(len(ready_tasks) - 1, 1)) * 0  # 选择第一个任务

            # 轮询选择机器
            machine_idx = machine_counter % num_machines
            machine_choice = -1.0 + (2.0 / max(num_machines - 1, 1)) * machine_idx
            machine_counter += 1

            action = np.array([task_choice, machine_choice])

            try:
                next_state, reward, done, info = data_loader.step_enhanced_task(action, debug=False)
                if done:
                    completed_tasks = len(info.get('completed_tasks', set()))
                    break
            except Exception:
                break

        algorithm_time = time.time() - start_time
        completion_rate = completed_tasks / num_tasks

        # 获取真正的makespan（DAG完成时间）
        if data_loader._step_state is not None and 'total_completion_time' in data_loader._step_state:
            makespan = data_loader._step_state['total_completion_time'] / 1000.0  # 转换为秒
        else:
            makespan = 0.0

        return {
            'completion_rate': completion_rate,
            'makespan': makespan,
            'completed_tasks': completed_tasks,
            'algorithm_time': algorithm_time
        }

    def simple_single_edge(data_loader, dag_idx):
        """简单单边缘执行算法"""
        start_time = time.time()

        # 加载DAG数据
        task_features = data_loader.load_task_features(dag_idx)
        adj_matrix = data_loader.load_adjacency_matrix(dag_idx)
        machine_resources = data_loader.load_machines_resource()
        comm_speed = data_loader.load_machine_commu_speed()
        memory_status = data_loader.load_memory_status()
        num_tasks = task_features.shape[1]

        # 初始化环境
        data_loader._init_step_state(task_features, adj_matrix, machine_resources, comm_speed, memory_status)

        completed_tasks = 0
        max_steps = num_tasks * 3

        for step in range(max_steps):
            ready_tasks = data_loader.get_ready_tasks()
            if not ready_tasks:
                break

            # 选择第一个就绪任务
            task_choice = -1.0  # 选择第一个任务

            # 总是选择第一个边缘设备（machine_idx=1）
            machine_choice = -1.0 + (2.0 / max(len(machine_resources) - 1, 1)) * 1

            action = np.array([task_choice, machine_choice])

            try:
                next_state, reward, done, info = data_loader.step_enhanced_task(action, debug=False)
                if done:
                    completed_tasks = len(info.get('completed_tasks', set()))
                    break
            except Exception:
                break

        algorithm_time = time.time() - start_time
        completion_rate = completed_tasks / num_tasks

        # 获取真正的makespan（DAG完成时间）
        if data_loader._step_state is not None and 'total_completion_time' in data_loader._step_state:
            makespan = data_loader._step_state['total_completion_time'] / 1000.0  # 转换为秒
        else:
            makespan = 0.0

        return {
            'completion_rate': completion_rate,
            'makespan': makespan,
            'completed_tasks': completed_tasks,
            'algorithm_time': algorithm_time
        }

    return {
        'random': simple_random,
        'round_robin': simple_round_robin,
        'single_edge': simple_single_edge
    }

def test_agent_performance(agent, data_loader, dag_indices, agent_name):
    """测试智能体性能"""
    print(f"\n🧪 测试 {agent_name} 性能...")

    results = []

    for i, dag_idx in enumerate(dag_indices):
        try:
            start_time = time.time()

            # 加载DAG数据
            task_features = data_loader.load_task_features(dag_idx)
            adj_matrix = data_loader.load_adjacency_matrix(dag_idx)
            machine_resources = data_loader.load_machines_resource()
            comm_speed = data_loader.load_machine_commu_speed()
            memory_status = data_loader.load_memory_status()
            num_tasks = task_features.shape[1]

            # 初始化环境
            data_loader._init_step_state(task_features, adj_matrix, machine_resources, comm_speed, memory_status)

            # 获取初始状态
            state = data_loader.process_state_as_enhanced_sequence(
                task_features, adj_matrix, machine_resources, comm_speed, memory_status)

            completed_tasks = 0
            max_steps = num_tasks * 3

            for step in range(max_steps):
                ready_tasks = data_loader.get_ready_tasks()
                if not ready_tasks:
                    break

                # 获取就绪掩码
                import enhanced_config as config
                ready_mask = np.zeros(config.SEQ_LEN)
                for j, _ in enumerate(ready_tasks[:config.SEQ_LEN]):
                    if j < config.SEQ_LEN:
                        ready_mask[j] = 1.0

                # 选择动作
                action = agent.select_action(state, ready_mask, deterministic=True)

                # 执行动作
                next_state, reward, done, info = data_loader.step_enhanced_task(action, debug=False)

                if done:
                    completed_tasks = len(info.get('completed_tasks', set()))
                    break

                state = next_state

            algorithm_time = time.time() - start_time
            completion_rate = completed_tasks / num_tasks

            # 获取真正的makespan（DAG完成时间）
            if data_loader._step_state is not None and 'total_completion_time' in data_loader._step_state:
                makespan = data_loader._step_state['total_completion_time'] / 1000.0  # 转换为秒
            else:
                makespan = 0.0

            results.append({
                'dag_idx': dag_idx,
                'completion_rate': completion_rate,
                'makespan': makespan,
                'completed_tasks': completed_tasks,
                'algorithm_time': algorithm_time
            })

            if (i + 1) % 10 == 0:
                print(f"  已完成 {i + 1}/{len(dag_indices)} 个DAG")

        except Exception as e:
            print(f"  ❌ DAG {dag_idx} 测试失败: {e}")
            results.append({
                'dag_idx': dag_idx,
                'completion_rate': 0.0,
                'makespan': 0.0,
                'completed_tasks': 0,
                'algorithm_time': 0.0
            })

    return results

def test_baseline_performance(baseline_func, data_loader, dag_indices, baseline_name):
    """测试基线算法性能"""
    print(f"\n🧪 测试 {baseline_name} 基线算法性能...")

    results = []

    for i, dag_idx in enumerate(dag_indices):
        try:
            result = baseline_func(data_loader, dag_idx)
            result['dag_idx'] = dag_idx
            results.append(result)

            if (i + 1) % 10 == 0:
                print(f"  已完成 {i + 1}/{len(dag_indices)} 个DAG")

        except Exception as e:
            print(f"  ❌ DAG {dag_idx} 测试失败: {e}")
            results.append({
                'dag_idx': dag_idx,
                'completion_rate': 0.0,
                'makespan': 0.0,
                'completed_tasks': 0,
                'algorithm_time': 0.0
            })

    return results

def load_trained_models(edge_num, device='cuda'):
    """加载训练好的模型"""
    models = {}

    # 查找最新的模型文件
    models_dir = "models"
    if not os.path.exists(models_dir):
        print("❌ models目录不存在")
        return models

    # 查找LSTM模型
    lstm_dirs = [d for d in os.listdir(models_dir) if d.startswith(f"lstm_sac_{edge_num}devices")]
    if lstm_dirs:
        latest_lstm_dir = sorted(lstm_dirs)[-1]
        lstm_model_path = os.path.join(models_dir, latest_lstm_dir, "lstm_sac_model.pth")
        if os.path.exists(lstm_model_path):
            try:
                # 使用简化的配置
                import enhanced_config as config
                config.EDGE_NUM = edge_num
                config.ENHANCED_STATE_DIM = 5 + 1 + edge_num*4 + 6  # 动态调整状态维度

                lstm_agent = LSTMSACAgent(
                    state_dim=config.ENHANCED_STATE_DIM,
                    action_dim=2,
                    hidden_dim=config.HIDDEN_DIM,
                    lr=config.LEARNING_RATE,
                    device=device
                )
                lstm_agent.load_models(lstm_model_path)
                models['LSTM'] = lstm_agent
                print(f"✅ 加载LSTM模型: {lstm_model_path}")
            except Exception as e:
                print(f"❌ 加载LSTM模型失败: {e}")

    # 查找xLSTM模型
    xlstm_dirs = [d for d in os.listdir(models_dir) if d.startswith(f"xlstm_sac_{edge_num}devices")]
    if xlstm_dirs:
        latest_xlstm_dir = sorted(xlstm_dirs)[-1]
        xlstm_model_path = os.path.join(models_dir, latest_xlstm_dir, "xlstm_sac_model.pth")
        if os.path.exists(xlstm_model_path):
            try:
                # 使用简化的配置
                import enhanced_config as config
                config.EDGE_NUM = edge_num
                config.ENHANCED_STATE_DIM = 5 + 1 + edge_num*4 + 6  # 动态调整状态维度

                xlstm_agent = XLSTMSACAgent(
                    state_dim=config.ENHANCED_STATE_DIM,
                    action_dim=2,
                    hidden_dim=config.HIDDEN_DIM,
                    lr=config.LEARNING_RATE,
                    device=device
                )
                xlstm_agent.load_models(xlstm_model_path)
                models['xLSTM'] = xlstm_agent
                print(f"✅ 加载xLSTM模型: {xlstm_model_path}")
            except Exception as e:
                print(f"❌ 加载xLSTM模型失败: {e}")

    return models

def analyze_results(all_results):
    """分析测试结果"""
    print("\n" + "=" * 80)
    print("📊 测试结果分析")
    print("=" * 80)

    # 计算每个算法的统计指标
    summary = {}

    for algorithm, results in all_results.items():
        if not results:
            continue

        completion_rates = [r['completion_rate'] for r in results]
        makespans = [r['makespan'] for r in results]
        completed_tasks = [r['completed_tasks'] for r in results]

        # 计算成功完成的DAG（完成率100%）
        successful_dags = [r for r in results if r['completion_rate'] >= 1.0]
        success_rate = len(successful_dags) / len(results) * 100

        # 计算条件平均makespan（只考虑成功完成的DAG）
        conditional_avg_makespan = np.mean([r['makespan'] for r in successful_dags]) if successful_dags else 0

        summary[algorithm] = {
            'avg_completion_rate': np.mean(completion_rates),
            'avg_makespan': np.mean(makespans),
            'avg_completed_tasks': np.mean(completed_tasks),
            'success_rate': success_rate,
            'successful_dags': len(successful_dags),
            'conditional_avg_makespan': conditional_avg_makespan,
            'std_completion_rate': np.std(completion_rates),
            'std_makespan': np.std(makespans)
        }

    # 打印结果表格
    print(f"\n📋 算法性能对比:")
    print(f"{'算法':<12} {'平均完成率':<10} {'成功率':<8} {'平均Makespan':<12} {'条件平均Makespan':<15} {'成功DAG数':<10}")
    print("-" * 85)

    for algorithm, stats in summary.items():
        print(f"{algorithm:<12} "
              f"{stats['avg_completion_rate']:.2%} "
              f"{stats['success_rate']:>6.1f}% "
              f"{stats['avg_makespan']:>10.2f}s "
              f"{stats['conditional_avg_makespan']:>13.2f}s "
              f"{stats['successful_dags']:>8d}")

    return summary

def plot_comparison_charts(all_results, summary, save_dir):
    """绘制算法对比图表"""

    # 设置绘图样式
    plt.style.use('ggplot')
    plt.rcParams['font.family'] = 'DejaVu Sans'
    plt.rcParams['font.size'] = 12

    algorithms = list(summary.keys())

    # 1. 完成率对比
    plt.figure(figsize=(12, 8))
    completion_rates = [summary[alg]['avg_completion_rate'] for alg in algorithms]
    colors = plt.cm.Set3(np.linspace(0, 1, len(algorithms)))

    bars = plt.bar(algorithms, completion_rates, color=colors, alpha=0.8)
    plt.ylabel('Average Completion Rate')
    plt.title('Algorithm Performance Comparison: Completion Rate')
    plt.xticks(rotation=45)
    plt.ylim(0, 1.1)
    plt.gca().yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: f'{y:.0%}'))

    # 添加数值标签
    for bar, rate in zip(bars, completion_rates):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{rate:.1%}', ha='center', va='bottom')

    plt.tight_layout()
    plt.savefig(f"{save_dir}/completion_rate_comparison.png", dpi=300, bbox_inches='tight')
    plt.close()

    # 2. Makespan对比
    plt.figure(figsize=(12, 8))
    makespans = [summary[alg]['avg_makespan'] for alg in algorithms]

    bars = plt.bar(algorithms, makespans, color=colors, alpha=0.8)
    plt.ylabel('Average Makespan (seconds)')
    plt.title('Algorithm Performance Comparison: Makespan')
    plt.xticks(rotation=45)

    # 添加数值标签
    for bar, makespan in zip(bars, makespans):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(makespans)*0.01,
                f'{makespan:.1f}s', ha='center', va='bottom')

    plt.tight_layout()
    plt.savefig(f"{save_dir}/makespan_comparison.png", dpi=300, bbox_inches='tight')
    plt.close()

    # 3. 条件平均Makespan对比（只考虑成功完成的DAG）
    plt.figure(figsize=(12, 8))
    conditional_makespans = [summary[alg]['conditional_avg_makespan'] for alg in algorithms]

    bars = plt.bar(algorithms, conditional_makespans, color=colors, alpha=0.8)
    plt.ylabel('Conditional Average Makespan (seconds)')
    plt.title('Algorithm Performance Comparison: Conditional Average Makespan\n(Only Successfully Completed DAGs)')
    plt.xticks(rotation=45)

    # 添加数值标签
    for bar, makespan in zip(bars, conditional_makespans):
        if makespan > 0:
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(conditional_makespans)*0.01,
                    f'{makespan:.1f}s', ha='center', va='bottom')

    plt.tight_layout()
    plt.savefig(f"{save_dir}/conditional_makespan_comparison.png", dpi=300, bbox_inches='tight')
    plt.close()

    # 4. 成功率对比
    plt.figure(figsize=(12, 8))
    success_rates = [summary[alg]['success_rate'] for alg in algorithms]

    bars = plt.bar(algorithms, success_rates, color=colors, alpha=0.8)
    plt.ylabel('Success Rate (%)')
    plt.title('Algorithm Performance Comparison: Success Rate (100% Completion)')
    plt.xticks(rotation=45)
    plt.ylim(0, 105)

    # 添加数值标签
    for bar, rate in zip(bars, success_rates):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                f'{rate:.1f}%', ha='center', va='bottom')

    plt.tight_layout()
    plt.savefig(f"{save_dir}/success_rate_comparison.png", dpi=300, bbox_inches='tight')
    plt.close()

    # 5. 综合对比图（2x2子图）
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # 完成率
    ax1.bar(algorithms, completion_rates, color=colors, alpha=0.8)
    ax1.set_ylabel('Completion Rate')
    ax1.set_title('Average Completion Rate')
    ax1.tick_params(axis='x', rotation=45)
    ax1.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: f'{y:.0%}'))

    # Makespan
    ax2.bar(algorithms, makespans, color=colors, alpha=0.8)
    ax2.set_ylabel('Makespan (seconds)')
    ax2.set_title('Average Makespan')
    ax2.tick_params(axis='x', rotation=45)

    # 条件平均Makespan
    ax3.bar(algorithms, conditional_makespans, color=colors, alpha=0.8)
    ax3.set_ylabel('Conditional Makespan (seconds)')
    ax3.set_title('Conditional Average Makespan')
    ax3.tick_params(axis='x', rotation=45)

    # 成功率
    ax4.bar(algorithms, success_rates, color=colors, alpha=0.8)
    ax4.set_ylabel('Success Rate (%)')
    ax4.set_title('Success Rate (100% Completion)')
    ax4.tick_params(axis='x', rotation=45)

    plt.tight_layout()
    plt.savefig(f"{save_dir}/comprehensive_comparison.png", dpi=300, bbox_inches='tight')
    plt.close()

    print(f"📈 对比图表已生成:")
    print(f"  📊 completion_rate_comparison.png")
    print(f"  📊 makespan_comparison.png")
    print(f"  📊 conditional_makespan_comparison.png")
    print(f"  📊 success_rate_comparison.png")
    print(f"  📊 comprehensive_comparison.png")

def save_detailed_results(all_results, summary, save_dir):
    """保存详细结果到Excel"""
    os.makedirs(save_dir, exist_ok=True)

    # 保存汇总统计
    summary_df = pd.DataFrame(summary).T
    summary_df.to_excel(f"{save_dir}/algorithm_summary.xlsx")

    # 保存详细结果
    with pd.ExcelWriter(f"{save_dir}/detailed_results.xlsx") as writer:
        for algorithm, results in all_results.items():
            if results:
                df = pd.DataFrame(results)
                df.to_excel(writer, sheet_name=algorithm, index=False)

    # 绘制对比图表
    plot_comparison_charts(all_results, summary, save_dir)

    print(f"📁 详细结果已保存到: {save_dir}/")

def main():
    """主测试函数"""
    print("🧪 综合测试：xLSTM vs LSTM vs 基线算法")
    print("=" * 80)

    # 配置参数
    edge_num = 2  # 从2设备开始测试
    test_dag_count = 50  # 测试50个DAG
    device = 'cuda' if torch.cuda.is_available() else 'cpu'

    print(f"📊 测试配置:")
    print(f"  边缘设备数: {edge_num}")
    print(f"  测试DAG数量: {test_dag_count}")
    print(f"  计算设备: {device}")

    # 设置测试数据路径
    test_data_path = f"../dataset/testing/DAG_30_edges_{edge_num}_mem_25-38GB_density_0.4_0.6"
    if not os.path.exists(test_data_path):
        print(f"❌ 测试数据集不存在: {test_data_path}")
        print("请确保测试数据集存在")
        return

    # 创建数据加载器
    data_loader = EnhancedDataLoader(test_data_path)

    # 生成测试DAG索引
    dag_indices = list(range(test_dag_count))

    # 加载训练好的模型
    print(f"\n🔧 加载训练好的模型...")
    trained_models = load_trained_models(edge_num, device)

    if not trained_models:
        print("❌ 没有找到训练好的模型，请先训练模型")
        return

    # 加载基线算法
    print(f"\n🔧 加载基线算法...")
    baseline_algorithms = load_baseline_algorithms()

    # 开始测试
    all_results = {}

    # 测试训练好的模型
    for model_name, agent in trained_models.items():
        results = test_agent_performance(agent, data_loader, dag_indices, model_name)
        all_results[model_name] = results

    # 测试基线算法
    for baseline_name, baseline_func in baseline_algorithms.items():
        results = test_baseline_performance(baseline_func, data_loader, dag_indices, baseline_name)
        all_results[baseline_name] = results

    # 分析结果
    summary = analyze_results(all_results)

    # 保存结果
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    save_dir = f"test_results/comprehensive_test_{edge_num}devices_{timestamp}"

    save_detailed_results(all_results, summary, save_dir)

    print(f"\n🎉 综合测试完成！")
    print(f"📁 所有结果已保存到: {save_dir}")

if __name__ == "__main__":
    import torch
    main()