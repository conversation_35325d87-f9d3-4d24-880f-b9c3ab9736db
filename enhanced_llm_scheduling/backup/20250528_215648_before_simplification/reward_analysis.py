#!/usr/bin/env python3
"""
奖励函数分析工具
用于分析和调试奖励函数的设置和效果
"""

import numpy as np
import matplotlib.pyplot as plt
from improved_reward_function import ImprovedRewardFunction
import seaborn as sns

def analyze_reward_function():
    """分析奖励函数的特性"""
    
    # 创建不同配置的奖励函数
    configs = {
        'Original': ImprovedRewardFunction(
            max_makespan=500.0,
            completion_weight=15.0,
            makespan_weight=10.0,
            efficiency_weight=3.0,
            penalty_weight=25.0
        ),
        'Improved': ImprovedRewardFunction(
            max_makespan=150.0,
            completion_weight=100.0,
            makespan_weight=30.0,
            efficiency_weight=10.0,
            penalty_weight=50.0,
            step_reward_scale=0.01
        ),
        'Stable': ImprovedRewardFunction(
            max_makespan=120.0,
            completion_weight=80.0,
            makespan_weight=25.0,
            efficiency_weight=8.0,
            penalty_weight=40.0,
            step_reward_scale=0.005
        )
    }
    
    # 分析完成率对奖励的影响
    completion_rates = np.linspace(0, 1, 21)
    makespan_values = [50, 100, 150, 200]
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. 完成率 vs 奖励（固定makespan=100）
    ax = axes[0, 0]
    for config_name, reward_func in configs.items():
        rewards = []
        for completion_rate in completion_rates:
            episode_info = {
                'total_tasks': 30,
                'completed_tasks': int(30 * completion_rate),
                'makespan': 100.0,
                'failed_tasks': 30 - int(30 * completion_rate),
                'deadlock_occurred': False
            }
            reward, _ = reward_func.calculate_reward({}, episode_info)
            rewards.append(reward)
        
        ax.plot(completion_rates, rewards, marker='o', label=config_name)
    
    ax.set_xlabel('完成率')
    ax.set_ylabel('奖励值')
    ax.set_title('完成率对奖励的影响 (Makespan=100s)')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 2. Makespan vs 奖励（固定完成率=0.8）
    ax = axes[0, 1]
    makespan_range = np.linspace(50, 300, 26)
    for config_name, reward_func in configs.items():
        rewards = []
        for makespan in makespan_range:
            episode_info = {
                'total_tasks': 30,
                'completed_tasks': 24,  # 80%完成率
                'makespan': makespan,
                'failed_tasks': 6,
                'deadlock_occurred': False
            }
            reward, _ = reward_func.calculate_reward({}, episode_info)
            rewards.append(reward)
        
        ax.plot(makespan_range, rewards, marker='o', label=config_name)
    
    ax.set_xlabel('Makespan (秒)')
    ax.set_ylabel('奖励值')
    ax.set_title('Makespan对奖励的影响 (完成率=80%)')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 3. 奖励组件分析（使用Improved配置）
    ax = axes[1, 0]
    reward_func = configs['Improved']
    
    # 分析不同场景下的奖励组件
    scenarios = [
        {'name': '完美执行', 'completed': 30, 'makespan': 80, 'failed': 0, 'deadlock': False},
        {'name': '高完成率', 'completed': 27, 'makespan': 120, 'failed': 3, 'deadlock': False},
        {'name': '中等完成率', 'completed': 21, 'makespan': 150, 'failed': 9, 'deadlock': False},
        {'name': '低完成率', 'completed': 15, 'makespan': 200, 'failed': 15, 'deadlock': False},
        {'name': '死锁情况', 'completed': 10, 'makespan': 300, 'failed': 20, 'deadlock': True}
    ]
    
    component_names = []
    component_values = []
    
    for scenario in scenarios:
        episode_info = {
            'total_tasks': 30,
            'completed_tasks': scenario['completed'],
            'makespan': scenario['makespan'],
            'failed_tasks': scenario['failed'],
            'deadlock_occurred': scenario['deadlock']
        }
        
        _, components = reward_func.calculate_reward({}, episode_info)
        
        for comp_name, comp_value in components.items():
            component_names.append(f"{scenario['name']}\n{comp_name}")
            component_values.append(comp_value)
    
    # 重新组织数据用于热力图
    scenarios_names = [s['name'] for s in scenarios]
    unique_components = list(set([name.split('\n')[1] for name in component_names]))
    
    heatmap_data = np.zeros((len(scenarios_names), len(unique_components)))
    
    for i, scenario in enumerate(scenarios):
        episode_info = {
            'total_tasks': 30,
            'completed_tasks': scenario['completed'],
            'makespan': scenario['makespan'],
            'failed_tasks': scenario['failed'],
            'deadlock_occurred': scenario['deadlock']
        }
        
        _, components = reward_func.calculate_reward({}, episode_info)
        
        for j, comp_name in enumerate(unique_components):
            heatmap_data[i, j] = components.get(comp_name, 0)
    
    im = ax.imshow(heatmap_data, cmap='RdYlBu_r', aspect='auto')
    ax.set_xticks(range(len(unique_components)))
    ax.set_xticklabels(unique_components, rotation=45, ha='right')
    ax.set_yticks(range(len(scenarios_names)))
    ax.set_yticklabels(scenarios_names)
    ax.set_title('不同场景下的奖励组件')
    
    # 添加数值标注
    for i in range(len(scenarios_names)):
        for j in range(len(unique_components)):
            text = ax.text(j, i, f'{heatmap_data[i, j]:.1f}',
                          ha="center", va="center", color="black", fontsize=8)
    
    plt.colorbar(im, ax=ax)
    
    # 4. 奖励稳定性分析
    ax = axes[1, 1]
    
    # 模拟训练过程中的奖励变化
    episodes = 1000
    reward_history = {'Original': [], 'Improved': [], 'Stable': []}
    
    np.random.seed(42)  # 确保可重复性
    
    for episode in range(episodes):
        # 模拟随机的执行结果
        completion_rate = np.random.beta(2, 2)  # 偏向中等完成率
        makespan = np.random.gamma(2, 50)  # 偏向较小的makespan
        
        for config_name, reward_func in configs.items():
            episode_info = {
                'total_tasks': 30,
                'completed_tasks': int(30 * completion_rate),
                'makespan': makespan,
                'failed_tasks': 30 - int(30 * completion_rate),
                'deadlock_occurred': np.random.random() < 0.05  # 5%死锁概率
            }
            
            reward, _ = reward_func.calculate_reward({}, episode_info)
            reward_history[config_name].append(reward)
    
    # 计算滑动平均
    window = 50
    for config_name, rewards in reward_history.items():
        smoothed = np.convolve(rewards, np.ones(window)/window, mode='valid')
        ax.plot(range(window-1, episodes), smoothed, label=f'{config_name} (平滑)')
        
        # 显示原始数据的方差
        variance = np.var(rewards)
        print(f"{config_name} 奖励方差: {variance:.2f}")
    
    ax.set_xlabel('训练回合')
    ax.set_ylabel('奖励值 (滑动平均)')
    ax.set_title('奖励稳定性对比')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('reward_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 打印详细分析
    print("\n🎯 奖励函数分析报告")
    print("=" * 50)
    
    print("\n📊 配置对比:")
    for config_name, reward_func in configs.items():
        print(f"\n{config_name}:")
        print(f"  最大Makespan: {reward_func.max_makespan}")
        print(f"  完成率权重: {reward_func.completion_weight}")
        print(f"  Makespan权重: {reward_func.makespan_weight}")
        print(f"  效率权重: {reward_func.efficiency_weight}")
        print(f"  惩罚权重: {reward_func.penalty_weight}")
        if hasattr(reward_func, 'step_reward_scale'):
            print(f"  步骤奖励缩放: {reward_func.step_reward_scale}")
    
    print("\n💡 建议:")
    print("1. 'Stable'配置适合初期训练，奖励变化较平稳")
    print("2. 'Improved'配置适合后期优化，更强调高完成率")
    print("3. 监控训练过程中的奖励方差，过高说明需要调整")
    print("4. 完成率权重应该是最高的，因为这是最重要的指标")
    print("5. 步骤奖励缩放应该很小，避免噪声干扰")

def test_reward_scenarios():
    """测试特定场景下的奖励计算"""
    
    reward_func = ImprovedRewardFunction(
        max_makespan=120.0,
        completion_weight=80.0,
        makespan_weight=25.0,
        efficiency_weight=8.0,
        penalty_weight=40.0,
        step_reward_scale=0.005
    )
    
    test_cases = [
        {
            'name': '理想情况',
            'episode_info': {
                'total_tasks': 30,
                'completed_tasks': 30,
                'makespan': 80.0,
                'failed_tasks': 0,
                'deadlock_occurred': False
            }
        },
        {
            'name': '当前训练水平',
            'episode_info': {
                'total_tasks': 30,
                'completed_tasks': 15,
                'makespan': 150.0,
                'failed_tasks': 15,
                'deadlock_occurred': False
            }
        },
        {
            'name': '最差情况',
            'episode_info': {
                'total_tasks': 30,
                'completed_tasks': 5,
                'makespan': 300.0,
                'failed_tasks': 25,
                'deadlock_occurred': True
            }
        }
    ]
    
    print("\n🧪 奖励测试案例")
    print("=" * 50)
    
    for test_case in test_cases:
        print(f"\n📋 {test_case['name']}:")
        episode_info = test_case['episode_info']
        
        reward, components = reward_func.calculate_reward({}, episode_info)
        
        print(f"  总奖励: {reward:.2f}")
        print("  奖励组件:")
        for comp_name, comp_value in components.items():
            print(f"    {comp_name}: {comp_value:.2f}")
        
        completion_rate = episode_info['completed_tasks'] / episode_info['total_tasks']
        print(f"  完成率: {completion_rate:.1%}")
        print(f"  Makespan: {episode_info['makespan']:.1f}秒")

if __name__ == "__main__":
    print("🔍 开始奖励函数分析...")
    analyze_reward_function()
    test_reward_scenarios()
    print("\n✅ 分析完成！")
