#!/usr/bin/env python3
"""
测试序列打印功能
运行少量episode来验证序列信息是否正确打印
"""

import sys
import os
import argparse

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from train_improved_xlstm import train_improved_xlstm

def test_sequence_printing():
    """测试序列打印功能"""
    print("🔍 测试序列打印功能")
    print("=" * 60)
    
    # 使用较小的数据集和少量episode进行测试
    data_path = '../dataset/training/DAG_200_edges_2_mem_32GB_density_0.4_0.6'
    
    print(f"📊 测试配置:")
    print(f"  数据路径: {data_path}")
    print(f"  测试回合数: 20")
    print(f"  边缘设备数: 2")
    print(f"  期望看到的序列信息:")
    print(f"    - Episode 0, 5, 10, 15: 基础序列信息")
    print(f"    - Episode 0, 20: 详细序列分析")
    print(f"    - 网络调试信息: 每100次前向传播")
    print(f"    - 采样调试信息: 每100次采样")
    
    try:
        # 运行训练测试
        result = train_improved_xlstm(
            data_path=data_path,
            num_episodes=20,  # 只运行20个episode
            save_interval=1000,  # 不保存中间结果
            device_num=2,
            device='cuda'
        )
        
        agent, rewards, completion_rates, makespans, efficiency_scores = result
        
        print("\n" + "=" * 60)
        print("🎉 序列打印功能测试完成!")
        
        # 显示测试结果统计
        if rewards:
            print(f"\n📊 测试结果统计:")
            print(f"  总回合数: {len(rewards)}")
            print(f"  平均奖励: {sum(rewards)/len(rewards):.2f}")
            print(f"  平均完成率: {sum(completion_rates)/len(completion_rates):.2%}")
            print(f"  最高完成率: {max(completion_rates):.2%}")
            
            # 检查是否有序列信息输出
            print(f"\n🔍 序列信息检查:")
            print(f"  如果您在上面的输出中看到了以下信息，说明序列打印功能正常:")
            print(f"    ✅ '📊 [序列信息] Episode X, Step Y'")
            print(f"    ✅ '🔬 [详细序列分析] Episode X, Step Y'")
            print(f"    ✅ '[TASK_SELECTOR_DEBUG] Logits分布'")
            print(f"    ✅ '[SEQUENCE_DEBUG] 就绪任务'")
            print(f"    ✅ '[SAMPLING_DEBUG] 采样过程'")
            
            print(f"\n💡 如果没有看到序列信息，可能的原因:")
            print(f"    1. Episode数量太少，没有触发打印条件")
            print(f"    2. 网络前向传播次数不足100次")
            print(f"    3. 调试标志设置有问题")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='测试序列打印功能')
    parser.add_argument('--episodes', type=int, default=20, help='测试回合数')
    parser.add_argument('--device_num', type=int, default=2, help='边缘设备数量')
    parser.add_argument('--device', type=str, default='cuda', help='计算设备')
    
    args = parser.parse_args()
    
    print(f"🚀 启动序列打印功能测试")
    print(f"参数: episodes={args.episodes}, device_num={args.device_num}, device={args.device}")
    
    # 修改全局参数
    global test_episodes, test_device_num, test_device
    test_episodes = args.episodes
    test_device_num = args.device_num
    test_device = args.device
    
    test_sequence_printing()

if __name__ == "__main__":
    main()
