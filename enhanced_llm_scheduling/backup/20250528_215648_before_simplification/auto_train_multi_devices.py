#!/usr/bin/env python3
"""
自动训练多设备数的xLSTM和LSTM模型
训练设备数: 2, 3, 4, 6
训练集: DAG_30_edges_x_mem_25-38GB_density_0.4_0.6
Episodes: 3000
保存间隔: 1000 episodes
"""

import os
import sys
import time
import subprocess
import logging
from datetime import datetime

def setup_logging():
    """设置日志"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = f"auto_train_log_{timestamp}.txt"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return log_file

def run_training(script_name, edge_num, episodes=3000, save_interval=1000):
    """运行训练脚本"""
    cmd = [
        'python', script_name,
        '--edge_num', str(edge_num),
        '--task_num', '30',
        '--episodes', str(episodes),
        '--save_interval', str(save_interval),
        '--data_path', f'../dataset/training/DAG_30_edges_{edge_num}_mem_25-38GB_density_0.4_0.6'
    ]
    
    logging.info(f"🚀 开始训练: {script_name} - {edge_num}设备")
    logging.info(f"命令: {' '.join(cmd)}")
    
    start_time = time.time()
    
    try:
        # 运行训练
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=7200  # 2小时超时
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        if result.returncode == 0:
            logging.info(f"✅ 训练成功完成: {script_name} - {edge_num}设备")
            logging.info(f"⏱️ 训练时间: {duration:.2f}秒 ({duration/60:.1f}分钟)")
            
            # 记录输出的最后几行
            if result.stdout:
                lines = result.stdout.strip().split('\n')
                logging.info("📊 训练输出摘要:")
                for line in lines[-10:]:  # 最后10行
                    if line.strip():
                        logging.info(f"  {line}")
            
            return True
        else:
            logging.error(f"❌ 训练失败: {script_name} - {edge_num}设备")
            logging.error(f"返回码: {result.returncode}")
            if result.stderr:
                logging.error(f"错误信息: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logging.error(f"⏰ 训练超时: {script_name} - {edge_num}设备")
        return False
    except Exception as e:
        logging.error(f"💥 训练异常: {script_name} - {edge_num}设备 - {e}")
        return False

def check_data_exists(edge_num):
    """检查训练数据是否存在"""
    data_path = f"../dataset/training/DAG_30_edges_{edge_num}_mem_25-38GB_density_0.4_0.6"
    if os.path.exists(data_path):
        logging.info(f"✅ 数据集存在: {data_path}")
        return True
    else:
        logging.warning(f"⚠️ 数据集不存在: {data_path}")
        return False

def main():
    """主函数"""
    print("🤖 自动训练多设备数模型")
    print("=" * 80)
    
    # 设置日志
    log_file = setup_logging()
    logging.info("🚀 开始自动训练任务")
    logging.info(f"📝 日志文件: {log_file}")
    
    # 训练配置
    device_nums = [2, 3, 4, 6]  # 设备数列表
    scripts = [
        ('train_lstm.py', 'LSTM'),
        ('train_xlstm.py', 'xLSTM')
    ]
    episodes = 3000
    save_interval = 1000
    
    # 检查脚本是否存在
    for script_name, model_type in scripts:
        if not os.path.exists(script_name):
            logging.error(f"❌ 训练脚本不存在: {script_name}")
            return
    
    # 统计信息
    total_tasks = len(device_nums) * len(scripts)
    completed_tasks = 0
    failed_tasks = 0
    
    logging.info(f"📊 训练计划:")
    logging.info(f"  设备数: {device_nums}")
    logging.info(f"  模型类型: {[model_type for _, model_type in scripts]}")
    logging.info(f"  总任务数: {total_tasks}")
    logging.info(f"  每个任务Episodes: {episodes}")
    logging.info(f"  保存间隔: {save_interval}")
    
    start_time = time.time()
    
    # 开始训练
    for edge_num in device_nums:
        logging.info(f"\n🔧 处理 {edge_num} 设备配置")
        
        # 检查数据集
        if not check_data_exists(edge_num):
            logging.warning(f"⚠️ 跳过 {edge_num} 设备训练（数据集不存在）")
            failed_tasks += len(scripts)
            continue
        
        for script_name, model_type in scripts:
            logging.info(f"\n📚 训练 {model_type} 模型 - {edge_num} 设备")
            
            success = run_training(script_name, edge_num, episodes, save_interval)
            
            if success:
                completed_tasks += 1
                logging.info(f"🎉 {model_type} - {edge_num}设备 训练完成")
            else:
                failed_tasks += 1
                logging.error(f"💔 {model_type} - {edge_num}设备 训练失败")
            
            # 进度报告
            progress = (completed_tasks + failed_tasks) / total_tasks * 100
            logging.info(f"📈 总进度: {completed_tasks + failed_tasks}/{total_tasks} ({progress:.1f}%)")
            
            # 短暂休息，避免系统过载
            time.sleep(5)
    
    # 最终报告
    end_time = time.time()
    total_duration = end_time - start_time
    
    logging.info(f"\n" + "=" * 80)
    logging.info(f"🏁 自动训练任务完成！")
    logging.info(f"📊 最终统计:")
    logging.info(f"  总任务数: {total_tasks}")
    logging.info(f"  成功完成: {completed_tasks}")
    logging.info(f"  失败任务: {failed_tasks}")
    logging.info(f"  成功率: {completed_tasks/total_tasks*100:.1f}%")
    logging.info(f"⏱️ 总耗时: {total_duration:.2f}秒 ({total_duration/3600:.1f}小时)")
    
    # 检查生成的模型
    logging.info(f"\n📁 检查生成的模型:")
    models_dir = "models"
    if os.path.exists(models_dir):
        model_files = os.listdir(models_dir)
        new_models = [f for f in model_files if any(f"_{edge_num}devices_" in f for edge_num in device_nums)]
        
        if new_models:
            logging.info(f"✅ 发现 {len(new_models)} 个新模型:")
            for model in sorted(new_models):
                logging.info(f"  📦 {model}")
        else:
            logging.warning(f"⚠️ 未发现新生成的模型")
    
    if completed_tasks == total_tasks:
        logging.info(f"🎉 所有训练任务都成功完成！")
        print(f"\n🎉 恭喜！所有 {total_tasks} 个训练任务都成功完成！")
        print(f"📁 请检查 models/ 目录查看训练好的模型")
        print(f"📝 详细日志请查看: {log_file}")
    else:
        logging.warning(f"⚠️ 有 {failed_tasks} 个任务失败，请检查日志")
        print(f"\n⚠️ 训练完成，但有 {failed_tasks} 个任务失败")
        print(f"📝 请检查日志文件: {log_file}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 用户中断训练")
        logging.info("🛑 用户中断训练")
    except Exception as e:
        print(f"\n💥 训练过程中发生异常: {e}")
        logging.error(f"💥 训练过程中发生异常: {e}")
        import traceback
        logging.error(traceback.format_exc())
