#!/usr/bin/env python3
"""
测试完成率专注的奖励函数
"""

from improved_reward_function import ImprovedRewardFunction

def test_completion_reward():
    """测试完成率专注的奖励函数"""
    
    print("🎯 测试完成率专注奖励函数")
    print("=" * 50)
    
    # 创建完成率专注奖励函数（参数不重要，只用完成率）
    reward_func = ImprovedRewardFunction(
        max_makespan=150.0,  # 不使用
        completion_weight=0.0,  # 不使用
        makespan_weight=0.0,  # 不使用
        efficiency_weight=0.0,  # 不使用
        penalty_weight=0.0,  # 不使用
        step_reward_scale=0.0  # 步骤奖励关闭
    )
    
    # 测试案例 - 不同完成率，不同makespan
    test_cases = [
        {
            'name': '完美完成 - 低makespan',
            'episode_info': {
                'total_tasks': 30,
                'completed_tasks': 30,
                'makespan': 80.0,
                'failed_tasks': 0,
                'deadlock_occurred': False
            }
        },
        {
            'name': '完美完成 - 高makespan',
            'episode_info': {
                'total_tasks': 30,
                'completed_tasks': 30,
                'makespan': 300.0,  # makespan很高但完成率100%
                'failed_tasks': 0,
                'deadlock_occurred': False
            }
        },
        {
            'name': '高完成率 - 90%',
            'episode_info': {
                'total_tasks': 30,
                'completed_tasks': 27,
                'makespan': 120.0,
                'failed_tasks': 3,
                'deadlock_occurred': False
            }
        },
        {
            'name': '中等完成率 - 70%',
            'episode_info': {
                'total_tasks': 30,
                'completed_tasks': 21,
                'makespan': 150.0,
                'failed_tasks': 9,
                'deadlock_occurred': False
            }
        },
        {
            'name': '中等完成率 - 50%',
            'episode_info': {
                'total_tasks': 30,
                'completed_tasks': 15,
                'makespan': 100.0,
                'failed_tasks': 15,
                'deadlock_occurred': False
            }
        },
        {
            'name': '低完成率 - 30%',
            'episode_info': {
                'total_tasks': 30,
                'completed_tasks': 9,
                'makespan': 90.0,  # makespan很低但完成率低
                'failed_tasks': 21,
                'deadlock_occurred': False
            }
        },
        {
            'name': '完全失败 - 0%',
            'episode_info': {
                'total_tasks': 30,
                'completed_tasks': 0,
                'makespan': 50.0,  # makespan很低但没完成任何任务
                'failed_tasks': 30,
                'deadlock_occurred': True
            }
        }
    ]
    
    print("\n📊 测试结果:")
    print("-" * 80)
    print(f"{'案例':<25} {'完成率':<8} {'Makespan':<10} {'奖励':<10} {'说明'}")
    print("-" * 80)
    
    for test_case in test_cases:
        episode_info = test_case['episode_info']
        
        # 计算奖励（步骤信息为空，只计算回合奖励）
        reward, components = reward_func.calculate_reward({}, episode_info)
        
        completion_rate = episode_info['completed_tasks'] / episode_info['total_tasks']
        makespan = episode_info['makespan']
        
        # 说明
        if completion_rate == 1.0:
            explanation = f"=100+50(完美奖励)"
        else:
            explanation = f"={completion_rate:.1%}×100"
        
        print(f"{test_case['name']:<25} {completion_rate:<8.1%} {makespan:<10.1f} {reward:<10.1f} {explanation}")
    
    print("-" * 80)
    
    # 分析奖励特性
    print("\n📈 完成率专注奖励函数特性:")
    print("1. 奖励 = 完成率 × 100")
    print("2. 完美完成(100%)额外奖励 +50")
    print("3. 完全不考虑makespan、内存、负载等因素")
    print("4. 步骤奖励完全关闭")
    print("5. 专注于提高任务完成率")
    
    print("\n🎯 奖励范围:")
    print("- 完美完成: 150分 (100% + 50奖励)")
    print("- 90%完成: 90分")
    print("- 70%完成: 70分")
    print("- 50%完成: 50分")
    print("- 30%完成: 30分")
    print("- 完全失败: 0分")
    
    print("\n💡 优势:")
    print("1. 逻辑极其简单，只关注完成率")
    print("2. 鼓励智能体优先学会完成任务")
    print("3. 不会因为makespan差异影响学习")
    print("4. 奖励范围明确(0-150)")
    print("5. 适合训练初期建立基础策略")
    
    print("\n⚠️  注意事项:")
    print("1. 不考虑makespan，可能导致执行时间很长")
    print("2. 适合作为第一阶段训练目标")
    print("3. 后续可能需要加入makespan考虑")
    print("4. 需要监控makespan是否在合理范围内")

def compare_completion_scenarios():
    """比较不同完成率场景的奖励"""
    
    print("\n🔄 完成率奖励对比:")
    print("=" * 50)
    
    reward_func = ImprovedRewardFunction()
    
    # 不同完成率，固定总任务数30
    completion_scenarios = [
        (30, 30),  # 100%
        (30, 27),  # 90%
        (30, 24),  # 80%
        (30, 21),  # 70%
        (30, 18),  # 60%
        (30, 15),  # 50%
        (30, 12),  # 40%
        (30, 9),   # 30%
        (30, 6),   # 20%
        (30, 3),   # 10%
        (30, 0),   # 0%
    ]
    
    print(f"{'完成任务':<8} {'完成率':<8} {'奖励':<8} {'奖励增量'}")
    print("-" * 40)
    
    prev_reward = None
    for total, completed in completion_scenarios:
        episode_info = {
            'total_tasks': total,
            'completed_tasks': completed,
            'makespan': 150.0,  # 固定makespan，不影响奖励
            'failed_tasks': total - completed,
            'deadlock_occurred': False
        }
        
        reward, _ = reward_func.calculate_reward({}, episode_info)
        completion_rate = completed / total
        
        if prev_reward is None:
            increment = "基准"
        else:
            increment = f"+{reward - prev_reward:.0f}"
        
        print(f"{completed:<8} {completion_rate:<8.1%} {reward:<8.0f} {increment}")
        prev_reward = reward
    
    print("\n📊 分析:")
    print("- 每完成1个任务，奖励增加约3.33分")
    print("- 完美完成有50分额外奖励")
    print("- 奖励与完成率呈线性关系")
    print("- makespan完全不影响奖励")

def test_different_task_numbers():
    """测试不同任务数量下的奖励"""
    
    print("\n🔢 不同任务数量的奖励对比:")
    print("=" * 45)
    
    reward_func = ImprovedRewardFunction()
    
    # 不同任务数量，都是100%完成
    task_numbers = [10, 20, 30, 40, 50]
    
    print(f"{'任务数':<8} {'完成率':<8} {'奖励':<8} {'说明'}")
    print("-" * 35)
    
    for total_tasks in task_numbers:
        episode_info = {
            'total_tasks': total_tasks,
            'completed_tasks': total_tasks,  # 100%完成
            'makespan': 150.0,
            'failed_tasks': 0,
            'deadlock_occurred': False
        }
        
        reward, _ = reward_func.calculate_reward({}, episode_info)
        
        print(f"{total_tasks:<8} 100.0%   {reward:<8.0f} 100+50(完美)")
    
    print("\n📊 分析:")
    print("- 不管任务数量多少，100%完成都是150分")
    print("- 奖励只看完成率，不看绝对任务数")
    print("- 这样设计保证了不同DAG大小的公平性")

if __name__ == "__main__":
    test_completion_reward()
    compare_completion_scenarios()
    test_different_task_numbers()
    print("\n✅ 完成率专注奖励函数测试完成！")
    print("💡 建议：先用这个奖励训练基础完成能力，再考虑加入makespan优化。")
