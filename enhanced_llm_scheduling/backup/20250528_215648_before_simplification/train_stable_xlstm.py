#!/usr/bin/env python3
"""
稳定的xLSTM训练脚本 - 专门解决训练不稳定问题
改进的奖励函数和训练策略
"""

import os
import sys
import torch
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import json

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from enhanced_data_loader import EnhancedDataLoader
from improved_reward_function import ImprovedRewardFunction
from xlstm_sac_agent import XLSTMSACAgent
import config

def calculate_memory_utilization(memory_status, total_memory):
    """计算内存利用率"""
    if not memory_status or not total_memory:
        return 0.0
    
    total_used = sum(memory_status)
    total_capacity = sum(total_memory)
    return total_used / total_capacity if total_capacity > 0 else 0.0

def calculate_load_balance_score(machine_finish_times):
    """计算负载均衡分数"""
    if not machine_finish_times or len(machine_finish_times) <= 1:
        return 1.0
    
    finish_times = [t for t in machine_finish_times if t > 0]
    if len(finish_times) <= 1:
        return 1.0
    
    mean_time = np.mean(finish_times)
    std_time = np.std(finish_times)
    
    # 标准差越小，负载越均衡
    balance_score = 1.0 / (1.0 + std_time / (mean_time + 1e-6))
    return balance_score

def train_stable_xlstm(device_num=6, episodes=3000, save_interval=1000, 
                      detailed_debug=False, data_path=None):
    """
    稳定的xLSTM训练函数
    """
    print(f"🚀 开始稳定xLSTM训练")
    print(f"   设备数量: {device_num}")
    print(f"   训练回合: {episodes}")
    print(f"   保存间隔: {save_interval}")
    
    # 设置设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"   使用设备: {device}")
    
    # 数据路径
    if data_path is None:
        data_path = f"../dataset/training/DAG_30_edges_{device_num}_mem_25-38GB_density_0.4_0.6"
    
    print(f"   数据路径: {data_path}")
    
    # 创建数据加载器
    data_loader = EnhancedDataLoader(
        data_path=data_path,
        num_machines=device_num + 1,
        debug=detailed_debug
    )
    
    # 创建智能体
    agent = XLSTMSACAgent(
        state_dim=config.ENHANCED_STATE_DIM,
        action_dim=2,
        hidden_dim=256,
        seq_len=config.SEQ_LEN,
        num_machines=device_num + 1,
        xlstm_layers=2,
        lr=1e-4,  # 降低学习率提高稳定性
        device=device
    )
    
    # 创建稳定的奖励函数
    reward_function = ImprovedRewardFunction(
        max_makespan=120.0,       # 更合理的makespan期望
        completion_weight=80.0,   # 高完成率权重
        makespan_weight=25.0,     # 适中的makespan权重
        efficiency_weight=8.0,    # 效率权重
        penalty_weight=40.0,      # 惩罚权重
        step_reward_scale=0.005   # 很小的步骤奖励减少噪声
    )
    
    # 创建保存目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_dir = f"models/stable_xlstm_sac_{device_num}devices_{timestamp}"
    os.makedirs(model_dir, exist_ok=True)
    
    # 训练记录
    training_log = {
        'episodes': [],
        'rewards': [],
        'completion_rates': [],
        'makespans': [],
        'efficiency_scores': [],
        'actor_losses': [],
        'critic_losses': []
    }
    
    print(f"\n📊 开始训练...")
    
    for episode in range(episodes):
        # 加载DAG数据
        task_features, adj_matrix, machine_resources, comm_speed, memory_status = data_loader.load_random_dag()
        num_tasks = len(task_features)
        
        # 初始化环境
        data_loader._init_step_state(task_features, adj_matrix, machine_resources, comm_speed, memory_status)
        
        # 获取初始状态
        state = data_loader.process_state_as_enhanced_sequence(
            task_features, adj_matrix, machine_resources, comm_speed, memory_status)
        
        episode_reward = 0.0
        step_count = 0
        max_steps = num_tasks * 3  # 限制最大步数
        
        # 训练损失记录
        episode_actor_losses = []
        episode_critic_losses = []
        
        while step_count < max_steps:
            try:
                # 获取ready_mask
                ready_mask = data_loader.get_ready_mask()
                if not np.any(ready_mask):
                    if detailed_debug:
                        print(f"    ⚠️  没有可执行任务，结束回合")
                    break
                
                # 选择动作
                action = agent.select_action(state, ready_mask, deterministic=False)
                
                # 执行动作
                next_state, reward, done, info = data_loader.step_enhanced_sequence(action)
                
                # 计算改进的奖励
                step_info = {
                    'valid_action': info.get('executed_task', -1) >= 0,
                    'task_completed': info.get('executed_task', -1) >= 0,
                    'memory_utilization': calculate_memory_utilization(
                        data_loader._step_state.get('memory_status', []),
                        list(data_loader._machine_total_memory.values()) if hasattr(data_loader, '_machine_total_memory') else None
                    ),
                    'load_balance_score': calculate_load_balance_score(
                        data_loader._step_state.get('machine_finish_time', [])
                    )
                }
                
                improved_reward, _ = reward_function.calculate_reward(step_info)
                episode_reward += improved_reward
                
                # 存储经验
                next_ready_mask = data_loader.get_ready_mask() if not done else None
                agent.store_transition(state, action, improved_reward, next_state, done, ready_mask, next_ready_mask)
                
                # 训练智能体
                if len(agent.replay_buffer) > 1000:  # 等待足够的经验
                    train_info = agent.train()
                    if train_info:
                        episode_actor_losses.append(train_info.get('actor_loss', 0))
                        episode_critic_losses.append(train_info.get('critic_loss', 0))
                
                if done:
                    # 计算最终奖励
                    completed_tasks_data = info.get('completed_tasks', set())
                    completed_tasks = len(completed_tasks_data) if hasattr(completed_tasks_data, '__len__') else 0
                    makespan = data_loader._step_state.get('total_completion_time', 0) / 1000.0
                    
                    episode_info = {
                        'total_tasks': num_tasks,
                        'completed_tasks': completed_tasks,
                        'makespan': makespan,
                        'failed_tasks': num_tasks - completed_tasks,
                        'deadlock_occurred': info.get('deadlock_detected', False)
                    }
                    
                    final_reward, _ = reward_function.calculate_reward({}, episode_info)
                    episode_reward += final_reward
                    
                    # 记录训练数据
                    completion_rate = completed_tasks / num_tasks
                    efficiency_score = completed_tasks / max(makespan, 1.0)
                    
                    training_log['episodes'].append(episode)
                    training_log['rewards'].append(episode_reward)
                    training_log['completion_rates'].append(completion_rate)
                    training_log['makespans'].append(makespan)
                    training_log['efficiency_scores'].append(efficiency_score)
                    training_log['actor_losses'].append(np.mean(episode_actor_losses) if episode_actor_losses else 0)
                    training_log['critic_losses'].append(np.mean(episode_critic_losses) if episode_critic_losses else 0)
                    
                    if episode % 100 == 0 or detailed_debug:
                        print(f"回合 {episode:4d}: 奖励={episode_reward:8.2f}, "
                              f"完成率={completion_rate:.1%}, Makespan={makespan:6.1f}秒, "
                              f"效率={efficiency_score:.3f}")
                    
                    break
                
                state = next_state
                step_count += 1
                
            except Exception as e:
                if detailed_debug:
                    print(f"    ❌ 执行错误: {e}")
                episode_reward -= 5.0  # 错误惩罚
                break
        
        # 定期保存模型和训练日志
        if (episode + 1) % save_interval == 0:
            # 保存模型
            model_path = os.path.join(model_dir, f"model_episode_{episode+1}.pth")
            agent.save_model(model_path)
            
            # 保存训练日志
            log_path = os.path.join(model_dir, f"training_log_episode_{episode+1}.json")
            with open(log_path, 'w') as f:
                json.dump(training_log, f, indent=2)
            
            print(f"💾 已保存模型和日志: {model_path}")
    
    # 最终保存
    final_model_path = os.path.join(model_dir, "final_model.pth")
    agent.save_model(final_model_path)
    
    final_log_path = os.path.join(model_dir, "final_training_log.json")
    with open(final_log_path, 'w') as f:
        json.dump(training_log, f, indent=2)
    
    print(f"\n✅ 训练完成！")
    print(f"   最终模型: {final_model_path}")
    print(f"   训练日志: {final_log_path}")
    
    return training_log, model_dir

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='稳定xLSTM训练')
    parser.add_argument('--devices', type=int, default=6, help='边缘设备数量')
    parser.add_argument('--episodes', type=int, default=3000, help='训练回合数')
    parser.add_argument('--save_interval', type=int, default=1000, help='保存间隔')
    parser.add_argument('--debug', action='store_true', help='详细调试模式')
    parser.add_argument('--data_path', type=str, help='数据路径')
    
    args = parser.parse_args()
    
    # 开始训练
    training_log, model_dir = train_stable_xlstm(
        device_num=args.devices,
        episodes=args.episodes,
        save_interval=args.save_interval,
        detailed_debug=args.debug,
        data_path=args.data_path
    )
    
    print(f"\n📈 训练统计:")
    print(f"   平均奖励: {np.mean(training_log['rewards'][-100:]):.2f}")
    print(f"   平均完成率: {np.mean(training_log['completion_rates'][-100:]):.1%}")
    print(f"   平均Makespan: {np.mean(training_log['makespans'][-100:]):.1f}秒")
    print(f"   平均效率: {np.mean(training_log['efficiency_scores'][-100:]):.3f}")
