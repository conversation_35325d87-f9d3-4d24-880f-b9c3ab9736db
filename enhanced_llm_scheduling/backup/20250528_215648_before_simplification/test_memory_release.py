#!/usr/bin/env python3
"""
测试增强数据加载器的内存释放机制
验证内存是否正确释放，防止死锁问题
"""

import numpy as np
import sys
import os

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from enhanced_data_loader import EnhancedDataLoader
from enhanced_config import EnhancedLLMConfig

def create_simple_test_dag():
    """创建一个简单的测试DAG"""
    # 3个任务的简单DAG: 0 -> 1 -> 2
    num_tasks = 3

    # 任务特征: [CPU周期, 数据传输, 是否LLM, token数, 内存需求]
    task_features = np.array([
        [1e9, 2e9, 3e9],      # CPU周期
        [100, 200, 150],      # 数据传输大小(MB)
        [0, 1, 1],            # 是否LLM任务
        [0, 1000, 1500],      # token数
        [2.0, 8.0, 12.0]      # 内存需求(GB)
    ])

    # 邻接矩阵: 0 -> 1 -> 2
    adj_matrix = np.array([
        [0, 1, 0],  # 任务0 -> 任务1
        [0, 0, 1],  # 任务1 -> 任务2
        [0, 0, 0]   # 任务2无后继
    ])

    # 机器资源 (CPU频率 GHz)
    machine_resources = np.array([2.5, 3.0, 3.5])  # 本地 + 2个边缘设备

    # 通信速度矩阵 (Mbps)
    comm_speed = np.array([
        [0, 100, 120],    # 本地到各设备
        [100, 0, 80],     # 边缘1到各设备
        [120, 80, 0]      # 边缘2到各设备
    ])

    return task_features, adj_matrix, machine_resources, comm_speed

def test_memory_release():
    """测试内存释放机制"""
    print("=" * 60)
    print("测试增强数据加载器的内存释放机制")
    print("=" * 60)

    # 创建配置和数据加载器
    config = EnhancedLLMConfig()
    config.NUM_EDGE_SERVERS = 2  # 设置2个边缘设备
    config.update_edge_servers(2)

    loader = EnhancedDataLoader(config, debug=True)

    # 创建测试数据
    task_features, adj_matrix, machine_resources, comm_speed = create_simple_test_dag()

    print(f"\n初始任务特征:")
    print(f"任务0: CPU={task_features[0,0]:.0e}, 数据={task_features[1,0]}MB, "
          f"LLM={task_features[2,0]}, 内存={task_features[4,0]}GB")
    print(f"任务1: CPU={task_features[0,1]:.0e}, 数据={task_features[1,1]}MB, "
          f"LLM={task_features[2,1]}, 内存={task_features[4,1]}GB")
    print(f"任务2: CPU={task_features[0,2]:.0e}, 数据={task_features[1,2]}MB, "
          f"LLM={task_features[2,2]}, 内存={task_features[4,2]}GB")

    print(f"\n机器资源: {machine_resources}")
    print(f"初始内存状态: 本地={config.LOCAL_MEMORY_LIMIT}GB, 边缘={config.MEMORY_LIMIT}GB")

    # 执行调度步骤
    step = 0
    done = False

    # 首先进行初始化调用
    print(f"\n{'='*20} 初始化 {'='*20}")
    task_choice = -0.8  # 映射到第一个就绪任务
    machine_choice = -0.8  # 映射到第一个机器
    compound_action = [task_choice, machine_choice]

    try:
        next_state, reward, done, info = loader.step_enhanced_task(
            compound_action,
            adj_matrix=adj_matrix,
            task_features=task_features,
            machine_resources=machine_resources,
            comm_speed=comm_speed,
            debug=True
        )
        print(f"初始化完成，就绪任务: {loader.get_ready_tasks()}")
    except Exception as e:
        print(f"初始化时出错: {e}")
        return

    while not done and step < 10:  # 最多10步，防止无限循环
        step += 1
        print(f"\n{'='*20} 步骤 {step} {'='*20}")

        # 获取就绪任务
        ready_tasks = loader.get_ready_tasks()
        print(f"就绪任务: {ready_tasks}")

        if not ready_tasks:
            print("没有就绪任务，可能发生死锁")
            break

        # 选择第一个就绪任务和第一个机器（简单策略）
        task_choice = -0.8  # 映射到第一个就绪任务
        machine_choice = -0.8  # 映射到第一个机器
        compound_action = [task_choice, machine_choice]

        print(f"执行动作: 任务选择={task_choice}, 机器选择={machine_choice}")

        # 执行步骤
        try:
            next_state, reward, done, info = loader.step_enhanced_task(
                compound_action,
                adj_matrix=adj_matrix,
                task_features=task_features,
                machine_resources=machine_resources,
                comm_speed=comm_speed,
                debug=True
            )

            print(f"奖励: {reward:.2f}")
            print(f"完成: {done}")
            print(f"已完成任务: {info.get('completed_tasks', set())}")

            # 检查内存状态
            if loader._step_state and 'memory_status' in loader._step_state:
                memory_status = loader._step_state['memory_status']
                print(f"当前内存状态: {[f'{mem:.2f}GB' for mem in memory_status]}")

                # 检查是否有内存被释放
                if 'memory_released_tasks' in loader._step_state:
                    released_tasks = loader._step_state['memory_released_tasks']
                    print(f"已释放内存的任务: {released_tasks}")

        except Exception as e:
            print(f"执行步骤时出错: {e}")
            break

    print(f"\n{'='*60}")
    print("测试完成")

    if done:
        print("✅ DAG成功完成")
        if 'dag_failed' in info and info['dag_failed']:
            print("⚠️  但DAG标记为失败")
    else:
        print("❌ DAG未完成（可能死锁）")

    # 最终状态检查
    if loader._step_state:
        completed_tasks = loader._step_state.get('completed_tasks', set())
        failed_tasks = loader._step_state.get('failed_tasks', set())
        memory_released_tasks = loader._step_state.get('memory_released_tasks', set())

        print(f"\n最终统计:")
        print(f"完成任务: {completed_tasks}")
        print(f"失败任务: {failed_tasks}")
        print(f"已释放内存的任务: {memory_released_tasks}")

        # 验证内存释放
        if len(completed_tasks) > 0:
            if len(memory_released_tasks) == len(completed_tasks):
                print("✅ 所有完成任务的内存都已正确释放")
            else:
                print("❌ 部分完成任务的内存未释放")
                unreleased = completed_tasks - memory_released_tasks
                print(f"   未释放内存的任务: {unreleased}")

if __name__ == "__main__":
    test_memory_release()
