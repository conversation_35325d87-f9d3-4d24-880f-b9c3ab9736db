"""
DAG难度分析工具
分析数据集中不同DAG的难度特征，帮助理解周期性性能变化
"""
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import os
from enhanced_data_loader import EnhancedDataLoader
import config

class DAGDifficultyAnalyzer:
    """DAG难度分析器"""
    
    def __init__(self, data_path):
        """
        初始化分析器
        Args:
            data_path: 数据集路径
        """
        self.data_path = data_path
        self.data_loader = EnhancedDataLoader(config, data_path, debug=False)
        self.dag_features = []
        
    def analyze_all_dags(self, num_dags=200):
        """
        分析所有DAG的难度特征
        Args:
            num_dags: 要分析的DAG数量
        """
        print(f"🔍 开始分析 {num_dags} 个DAG的难度特征...")
        
        for dag_idx in range(num_dags):
            try:
                features = self._analyze_single_dag(dag_idx)
                features['dag_idx'] = dag_idx
                self.dag_features.append(features)
                
                if (dag_idx + 1) % 50 == 0:
                    print(f"  已分析 {dag_idx + 1}/{num_dags} 个DAG")
                    
            except Exception as e:
                print(f"  ❌ DAG {dag_idx} 分析失败: {e}")
                continue
        
        # 转换为DataFrame
        self.df = pd.DataFrame(self.dag_features)
        print(f"✅ 完成分析，共 {len(self.df)} 个有效DAG")
        
    def _analyze_single_dag(self, dag_idx):
        """
        分析单个DAG的特征
        Args:
            dag_idx: DAG索引
        Returns:
            dict: DAG特征字典
        """
        # 加载DAG数据
        task_features = self.data_loader.load_task_features(dag_idx)
        adj_matrix = self.data_loader.load_adjacency_matrix(dag_idx)
        
        num_tasks = task_features.shape[1]
        
        # 1. 基础特征
        features = {
            'num_tasks': num_tasks,
            'num_edges': np.sum(adj_matrix),
            'dag_density': np.sum(adj_matrix) / (num_tasks * (num_tasks - 1)) if num_tasks > 1 else 0
        }
        
        # 2. LLM任务特征
        llm_mask = task_features[2, :] == 1
        features['num_llm_tasks'] = np.sum(llm_mask)
        features['llm_ratio'] = features['num_llm_tasks'] / num_tasks
        
        # 3. 内存需求特征
        memory_reqs = task_features[4, :]
        features['total_memory_req'] = np.sum(memory_reqs)
        features['avg_memory_req'] = np.mean(memory_reqs)
        features['max_memory_req'] = np.max(memory_reqs)
        features['llm_memory_req'] = np.sum(memory_reqs[llm_mask]) if np.any(llm_mask) else 0
        
        # 4. 计算复杂度特征
        cpu_cycles = task_features[0, :]
        features['total_cpu_cycles'] = np.sum(cpu_cycles)
        features['avg_cpu_cycles'] = np.mean(cpu_cycles)
        features['max_cpu_cycles'] = np.max(cpu_cycles)
        
        # 5. 数据传输特征
        data_transfer = task_features[1, :]
        features['total_data_transfer'] = np.sum(data_transfer)
        features['avg_data_transfer'] = np.mean(data_transfer)
        
        # 6. Token特征（LLM任务）
        token_counts = task_features[3, :]
        features['total_tokens'] = np.sum(token_counts)
        features['avg_tokens'] = np.mean(token_counts[llm_mask]) if np.any(llm_mask) else 0
        
        # 7. 图结构复杂度
        in_degrees = np.sum(adj_matrix, axis=0)
        out_degrees = np.sum(adj_matrix, axis=1)
        features['max_in_degree'] = np.max(in_degrees)
        features['max_out_degree'] = np.max(out_degrees)
        features['avg_in_degree'] = np.mean(in_degrees)
        features['avg_out_degree'] = np.mean(out_degrees)
        
        # 8. 关键路径长度（简化估算）
        features['critical_path_estimate'] = self._estimate_critical_path_length(adj_matrix, cpu_cycles)
        
        # 9. 难度评分（综合指标）
        features['difficulty_score'] = self._calculate_difficulty_score(features)
        
        return features
    
    def _estimate_critical_path_length(self, adj_matrix, cpu_cycles):
        """
        估算关键路径长度
        Args:
            adj_matrix: 邻接矩阵
            cpu_cycles: CPU周期数组
        Returns:
            float: 关键路径长度估算
        """
        num_tasks = len(cpu_cycles)
        
        # 简化的关键路径计算：找到最长的依赖链
        max_path_length = 0
        
        # 对每个任务进行DFS，找最长路径
        for start_task in range(num_tasks):
            if np.sum(adj_matrix[:, start_task]) == 0:  # 入度为0的任务
                path_length = self._dfs_longest_path(adj_matrix, cpu_cycles, start_task, set())
                max_path_length = max(max_path_length, path_length)
        
        return max_path_length
    
    def _dfs_longest_path(self, adj_matrix, cpu_cycles, current_task, visited):
        """
        DFS计算最长路径
        """
        if current_task in visited:
            return 0
        
        visited.add(current_task)
        max_length = cpu_cycles[current_task]
        
        # 找到所有后继任务
        successors = np.where(adj_matrix[current_task, :] == 1)[0]
        
        for successor in successors:
            path_length = self._dfs_longest_path(adj_matrix, cpu_cycles, successor, visited.copy())
            max_length = max(max_length, cpu_cycles[current_task] + path_length)
        
        return max_length
    
    def _calculate_difficulty_score(self, features):
        """
        计算DAG难度评分
        Args:
            features: DAG特征字典
        Returns:
            float: 难度评分（0-100，越高越难）
        """
        score = 0
        
        # LLM任务比例权重 (0-30分)
        score += features['llm_ratio'] * 30
        
        # 内存需求权重 (0-25分)
        memory_score = min(features['total_memory_req'] / 100, 1.0) * 25
        score += memory_score
        
        # 图复杂度权重 (0-20分)
        complexity_score = min(features['dag_density'] * 2, 1.0) * 20
        score += complexity_score
        
        # 计算复杂度权重 (0-15分)
        cpu_score = min(features['total_cpu_cycles'] / 1e12, 1.0) * 15
        score += cpu_score
        
        # 关键路径权重 (0-10分)
        path_score = min(features['critical_path_estimate'] / 1e11, 1.0) * 10
        score += path_score
        
        return min(score, 100)
    
    def generate_difficulty_report(self, save_path=None):
        """
        生成难度分析报告
        Args:
            save_path: 保存路径
        """
        if self.df is None or len(self.df) == 0:
            print("❌ 没有分析数据，请先运行 analyze_all_dags()")
            return
        
        print("\n📊 DAG难度分析报告")
        print("=" * 50)
        
        # 基础统计
        print(f"总DAG数量: {len(self.df)}")
        print(f"平均任务数: {self.df['num_tasks'].mean():.1f}")
        print(f"平均LLM任务比例: {self.df['llm_ratio'].mean():.2%}")
        print(f"平均内存需求: {self.df['total_memory_req'].mean():.1f}GB")
        print(f"平均难度评分: {self.df['difficulty_score'].mean():.1f}")
        
        # 难度分布
        print(f"\n🎯 难度分布:")
        easy_dags = self.df[self.df['difficulty_score'] < 30]
        medium_dags = self.df[(self.df['difficulty_score'] >= 30) & (self.df['difficulty_score'] < 60)]
        hard_dags = self.df[self.df['difficulty_score'] >= 60]
        
        print(f"简单DAG (难度<30): {len(easy_dags)} 个 ({len(easy_dags)/len(self.df):.1%})")
        print(f"中等DAG (30≤难度<60): {len(medium_dags)} 个 ({len(medium_dags)/len(self.df):.1%})")
        print(f"困难DAG (难度≥60): {len(hard_dags)} 个 ({len(hard_dags)/len(self.df):.1%})")
        
        # 保存详细数据
        if save_path:
            os.makedirs(save_path, exist_ok=True)
            
            # 保存完整分析数据
            self.df.to_excel(f"{save_path}/dag_difficulty_analysis.xlsx", index=False)
            
            # 保存分类结果
            classification = pd.DataFrame({
                'dag_idx': self.df['dag_idx'],
                'difficulty_score': self.df['difficulty_score'],
                'difficulty_level': pd.cut(self.df['difficulty_score'], 
                                         bins=[0, 30, 60, 100], 
                                         labels=['Easy', 'Medium', 'Hard'])
            })
            classification.to_excel(f"{save_path}/dag_classification.xlsx", index=False)
            
            # 绘制分布图
            self._plot_difficulty_distribution(save_path)
            
            print(f"✅ 报告已保存到: {save_path}")
    
    def _plot_difficulty_distribution(self, save_path):
        """绘制难度分布图"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # 难度评分分布
        ax1.hist(self.df['difficulty_score'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax1.set_xlabel('Difficulty Score')
        ax1.set_ylabel('Count')
        ax1.set_title('DAG Difficulty Score Distribution')
        ax1.grid(True, alpha=0.3)
        
        # LLM任务比例分布
        ax2.hist(self.df['llm_ratio'], bins=20, alpha=0.7, color='lightgreen', edgecolor='black')
        ax2.set_xlabel('LLM Task Ratio')
        ax2.set_ylabel('Count')
        ax2.set_title('LLM Task Ratio Distribution')
        ax2.grid(True, alpha=0.3)
        
        # 内存需求分布
        ax3.hist(self.df['total_memory_req'], bins=20, alpha=0.7, color='salmon', edgecolor='black')
        ax3.set_xlabel('Total Memory Requirement (GB)')
        ax3.set_ylabel('Count')
        ax3.set_title('Memory Requirement Distribution')
        ax3.grid(True, alpha=0.3)
        
        # 难度评分 vs LLM比例散点图
        scatter = ax4.scatter(self.df['llm_ratio'], self.df['difficulty_score'], 
                            c=self.df['total_memory_req'], cmap='viridis', alpha=0.6)
        ax4.set_xlabel('LLM Task Ratio')
        ax4.set_ylabel('Difficulty Score')
        ax4.set_title('Difficulty vs LLM Ratio (colored by memory req)')
        plt.colorbar(scatter, ax=ax4, label='Memory Req (GB)')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f"{save_path}/dag_difficulty_distribution.png", dpi=300, bbox_inches='tight')
        plt.close()

def main():
    """主函数"""
    data_path = "../dataset/training/DAG_30_edges_5_mem_25-38GB_density_0.4_0.6"
    save_path = "dag_analysis_results"
    
    print("🔍 启动DAG难度分析...")
    
    analyzer = DAGDifficultyAnalyzer(data_path)
    analyzer.analyze_all_dags(200)
    analyzer.generate_difficulty_report(save_path)
    
    print("🎉 DAG难度分析完成!")

if __name__ == "__main__":
    main()
