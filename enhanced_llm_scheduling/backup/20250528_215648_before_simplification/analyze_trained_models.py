#!/usr/bin/env python3
"""
分析已训练模型的Makespan性能
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import re

def analyze_model_performance():
    """分析所有训练好的模型性能"""
    
    models_dir = Path("models")
    results = []
    
    # 查找所有final模型
    final_models = list(models_dir.glob("*_final_*"))
    
    print(f"🔍 找到 {len(final_models)} 个最终模型")
    
    for model_dir in final_models:
        try:
            # 解析模型信息
            model_name = model_dir.name
            
            # 提取模型类型和设备数
            if "lstm_sac" in model_name:
                model_type = "LSTM"
            elif "xlstm_sac" in model_name:
                model_type = "xLSTM"
            else:
                continue
                
            # 提取设备数
            device_match = re.search(r'(\d+)devices', model_name)
            if device_match:
                device_num = int(device_match.group(1))
            else:
                continue
            
            # 读取训练历史
            history_file = model_dir / "final_training_history.xlsx"
            if not history_file.exists():
                print(f"❌ {model_name}: 缺少训练历史文件")
                continue
                
            df = pd.read_excel(history_file)
            
            # 检查是否有makespan数据
            has_makespan = 'Makespan' in df.columns
            
            # 计算统计信息
            stats = {
                'Model_Type': model_type,
                'Device_Num': device_num,
                'Model_Name': model_name,
                'Total_Episodes': len(df),
                'Final_Reward': df['Reward'].iloc[-1] if len(df) > 0 else 0,
                'Avg_Reward': df['Reward'].mean(),
                'Final_Completion': df['Completion_Ratio'].iloc[-1] if len(df) > 0 else 0,
                'Avg_Completion': df['Completion_Ratio'].mean(),
                'Max_Completion': df['Completion_Ratio'].max(),
                'Has_Makespan': has_makespan
            }
            
            if has_makespan:
                stats.update({
                    'Final_Makespan': df['Makespan'].iloc[-1],
                    'Avg_Makespan': df['Makespan'].mean(),
                    'Best_Makespan': df['Makespan'].min()
                })
            else:
                stats.update({
                    'Final_Makespan': None,
                    'Avg_Makespan': None,
                    'Best_Makespan': None
                })
            
            results.append(stats)
            
            status = "✅ 有Makespan" if has_makespan else "❌ 无Makespan"
            print(f"{status} {model_type}-{device_num}设备: "
                  f"完成率={stats['Avg_Completion']:.2%}, "
                  f"奖励={stats['Avg_Reward']:.1f}")
            
        except Exception as e:
            print(f"❌ 分析 {model_dir.name} 失败: {e}")
            continue
    
    # 转换为DataFrame并保存
    results_df = pd.DataFrame(results)
    
    if len(results_df) > 0:
        # 按设备数和模型类型排序
        results_df = results_df.sort_values(['Device_Num', 'Model_Type'])
        
        # 保存结果
        results_df.to_excel("model_performance_analysis.xlsx", index=False)
        
        # 打印汇总
        print(f"\n📊 模型性能汇总:")
        print("=" * 80)
        
        for device_num in sorted(results_df['Device_Num'].unique()):
            device_models = results_df[results_df['Device_Num'] == device_num]
            print(f"\n🔧 {device_num}设备:")
            
            for _, row in device_models.iterrows():
                makespan_info = ""
                if row['Has_Makespan']:
                    makespan_info = f", Makespan={row['Avg_Makespan']:.1f}s"
                
                print(f"  {row['Model_Type']:>5}: "
                      f"完成率={row['Avg_Completion']:>6.1%}, "
                      f"奖励={row['Avg_Reward']:>6.1f}"
                      f"{makespan_info}")
        
        # 绘制对比图
        plot_model_comparison(results_df)
        
        return results_df
    else:
        print("❌ 没有找到有效的模型数据")
        return None

def plot_model_comparison(results_df):
    """绘制模型对比图"""
    
    # 设置绘图样式
    plt.style.use('ggplot')
    plt.rcParams['font.family'] = 'DejaVu Sans'
    plt.rcParams['font.size'] = 12
    
    device_nums = sorted(results_df['Device_Num'].unique())
    model_types = sorted(results_df['Model_Type'].unique())
    
    # 1. 完成率对比
    plt.figure(figsize=(12, 8))
    
    for model_type in model_types:
        model_data = results_df[results_df['Model_Type'] == model_type]
        completion_rates = []
        
        for device_num in device_nums:
            device_data = model_data[model_data['Device_Num'] == device_num]
            if len(device_data) > 0:
                completion_rates.append(device_data['Avg_Completion'].iloc[0])
            else:
                completion_rates.append(0)
        
        plt.plot(device_nums, completion_rates, 'o-', linewidth=2, 
                label=f'{model_type} SAC', markersize=8)
    
    plt.xlabel('Number of Edge Devices')
    plt.ylabel('Average Completion Rate')
    plt.title('Model Performance Comparison: Completion Rate vs Device Number')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.ylim(0, 1.1)
    plt.gca().yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: f'{y:.0%}'))
    plt.savefig('model_completion_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. 奖励对比
    plt.figure(figsize=(12, 8))
    
    for model_type in model_types:
        model_data = results_df[results_df['Model_Type'] == model_type]
        rewards = []
        
        for device_num in device_nums:
            device_data = model_data[model_data['Device_Num'] == device_num]
            if len(device_data) > 0:
                rewards.append(device_data['Avg_Reward'].iloc[0])
            else:
                rewards.append(0)
        
        plt.plot(device_nums, rewards, 's-', linewidth=2, 
                label=f'{model_type} SAC', markersize=8)
    
    plt.xlabel('Number of Edge Devices')
    plt.ylabel('Average Reward')
    plt.title('Model Performance Comparison: Reward vs Device Number')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig('model_reward_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. Makespan对比（如果有数据）
    makespan_data = results_df[results_df['Has_Makespan'] == True]
    if len(makespan_data) > 0:
        plt.figure(figsize=(12, 8))
        
        for model_type in model_types:
            model_data = makespan_data[makespan_data['Model_Type'] == model_type]
            makespans = []
            valid_devices = []
            
            for device_num in device_nums:
                device_data = model_data[model_data['Device_Num'] == device_num]
                if len(device_data) > 0:
                    makespans.append(device_data['Avg_Makespan'].iloc[0])
                    valid_devices.append(device_num)
            
            if makespans:
                plt.plot(valid_devices, makespans, '^-', linewidth=2, 
                        label=f'{model_type} SAC', markersize=8)
        
        plt.xlabel('Number of Edge Devices')
        plt.ylabel('Average Makespan (seconds)')
        plt.title('Model Performance Comparison: Makespan vs Device Number')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.savefig('model_makespan_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("📈 Makespan对比图已生成")
    else:
        print("⚠️  没有Makespan数据，无法生成Makespan对比图")
    
    print("📈 模型对比图已生成:")
    print("  📊 model_completion_comparison.png")
    print("  📊 model_reward_comparison.png")

if __name__ == "__main__":
    print("🔍 开始分析训练好的模型...")
    results = analyze_model_performance()
    
    if results is not None:
        print(f"\n✅ 分析完成！结果保存到 model_performance_analysis.xlsx")
        print("📈 对比图已生成")
    else:
        print("❌ 分析失败")
