#!/usr/bin/env python3
"""
xLSTM版本的增强LLM调度系统训练脚本
实现"先选任务，再分配机器"的清晰逻辑，包含最新的内存改进
使用复合动作架构（与增强版/LSTM版本保持一致）
"""

import torch
import numpy as np
import argparse
import os
import sys
import time
from datetime import datetime
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端

# 添加路径
sys.path.append('..')
sys.path.append('.')

from enhanced_config import EnhancedLLMConfig
from enhanced_data_loader import EnhancedDataLoader
from xlstm_sac_agent import XLSTMSACAgent

def calculate_memory_utilization(memory_status, machine_total_memory=None):
    """计算内存利用率"""
    if not memory_status or len(memory_status) == 0:
        return 0.0

    # 如果没有提供机器总内存信息，使用默认值
    if machine_total_memory is None:
        # 默认：用户设备16GB，边缘设备32GB
        machine_total_memory = [16.0] + [32.0] * (len(memory_status) - 1)

    total_available = sum(memory_status)
    total_capacity = sum(machine_total_memory[:len(memory_status)])

    if total_capacity <= 0:
        return 0.0

    utilization = 1.0 - (total_available / total_capacity)
    return max(0.0, min(1.0, utilization))

def calculate_load_balance_score(machine_finish_times):
    """计算负载均衡分数"""
    if not machine_finish_times or len(machine_finish_times) <= 1:
        return 1.0

    # 计算机器完成时间的标准差
    times = np.array(machine_finish_times)
    if np.max(times) == 0:
        return 1.0

    # 归一化标准差
    normalized_std = np.std(times) / (np.mean(times) + 1e-8)

    # 标准差越小，负载均衡越好
    balance_score = 1.0 / (1.0 + normalized_std)
    return max(0.0, min(1.0, balance_score))

def create_replay_buffer():
    """创建简单的经验回放缓冲区"""
    return {
        'states': [],
        'actions': [],
        'rewards': [],
        'next_states': [],
        'dones': [],
        'ready_masks': [],
        'next_ready_masks': []
    }

def add_to_replay_buffer(buffer, state, action, reward, next_state, done, ready_mask, next_ready_mask):
    """添加经验到回放缓冲区"""
    buffer['states'].append(state)
    buffer['actions'].append(action)
    buffer['rewards'].append(reward)
    buffer['next_states'].append(next_state)
    buffer['dones'].append(done)
    buffer['ready_masks'].append(ready_mask)
    buffer['next_ready_masks'].append(next_ready_mask)

    # 限制缓冲区大小
    max_size = 1000000
    if len(buffer['states']) > max_size:
        for key in buffer:
            buffer[key] = buffer[key][-max_size:]

def sample_from_replay_buffer(buffer, batch_size):
    """从回放缓冲区采样"""
    if len(buffer['states']) < batch_size:
        return None

    indices = np.random.choice(len(buffer['states']), batch_size, replace=False)

    batch = {}
    for key in buffer:
        batch[key] = [buffer[key][i] for i in indices]

    return batch

def train_xlstm_sac(edge_num, task_num, episodes, save_interval, data_path=None):
    """训练xLSTM版SAC智能体"""
    print(f"🔧 xLSTM版训练配置:")
    print(f"  边缘设备数量: {edge_num}")
    print(f"  最大任务数量: {task_num}")
    print(f"  训练回合数: {episodes}")
    print(f"  保存间隔: {save_interval}")
    if data_path:
        print(f"  自定义数据路径: {data_path}")

    # 创建配置
    config = EnhancedLLMConfig()
    config.NUM_EDGE_SERVERS = edge_num
    config.update_edge_servers(edge_num)

    if data_path:
        config.set_custom_data_path(data_path)

    print(f"🚀 开始xLSTM版训练 - {edge_num}个边缘设备")

    # 创建数据加载器和智能体
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")

    data_loader = EnhancedDataLoader(config, debug=True)
    agent = XLSTMSACAgent(config, device=device)

    # 创建经验回放缓冲区
    replay_buffer = create_replay_buffer()

    print(f"\n📊 训练配置:")
    print(f"  边缘设备数: {edge_num}")
    print(f"  状态维度: {config.ENHANCED_STATE_DIM}")
    print(f"  学习率: {config.LEARNING_RATE}")
    print(f"  批次大小: {config.BATCH_SIZE}")
    if data_path:
        print(f"  数据路径: {data_path}")

    # 训练循环
    episode_rewards = []
    episode_completion_rates = []
    episode_makespans = []  # 添加makespan追踪
    successful_episodes = 0
    nan_count = 0
    start_time = time.time()

    for episode in range(episodes):
        try:
            # 加载DAG数据
            dag_idx = episode % 200  # 循环使用200个DAG
            task_features = data_loader.load_task_features(dag_idx)
            adj_matrix = data_loader.load_adjacency_matrix(dag_idx)
            machine_resources = data_loader.load_machines_resource()
            comm_speed = data_loader.load_machine_commu_speed()
            memory_status = data_loader.load_memory_status()
            num_tasks = task_features.shape[1]

            if episode % 100 == 0:
                print(f"[TRAIN] Episode {episode}: 加载DAG {dag_idx}, 任务数: {num_tasks}")
                llm_tasks = np.sum(task_features[2, :] == 1)
                print(f"[TRAIN] LLM任务数量: {llm_tasks}/{num_tasks}")

            # 初始化环境
            data_loader._init_step_state(task_features, adj_matrix, machine_resources, comm_speed, memory_status)

            # 获取初始状态
            state = data_loader.process_state_as_enhanced_sequence(
                task_features, adj_matrix, machine_resources, comm_speed, memory_status)

            # 训练episode
            step_count = 0
            max_steps = num_tasks * 2
            episode_reward = 0
            episode_nan_count = 0

            while step_count < max_steps:
                step_count += 1

                # 获取就绪任务掩码
                ready_tasks = data_loader.get_ready_tasks()
                ready_mask = np.zeros(config.SEQ_LEN)
                for i, _ in enumerate(ready_tasks[:config.SEQ_LEN]):
                    if i < config.SEQ_LEN:
                        ready_mask[i] = 1.0

                # 选择动作
                action = agent.select_action(state, ready_mask, deterministic=False)

                # 检查NaN
                if np.isnan(action).any():
                    episode_nan_count += 1
                    action = np.array([0.0, 0.0])

                # 执行动作
                next_state, reward, done, info = data_loader.step_enhanced_task(
                    action, debug=(episode % 100 == 0 and step_count < 3)
                )

                # 获取下一状态的就绪掩码
                next_ready_tasks = data_loader.get_ready_tasks()
                next_ready_mask = np.zeros(config.SEQ_LEN)
                for i, _ in enumerate(next_ready_tasks[:config.SEQ_LEN]):
                    if i < config.SEQ_LEN:
                        next_ready_mask[i] = 1.0

                # 添加到经验回放
                add_to_replay_buffer(
                    replay_buffer, state, action, reward, next_state, done,
                    ready_mask, next_ready_mask
                )

                # 更新网络
                if len(replay_buffer['states']) >= config.BATCH_SIZE and step_count % 4 == 0:
                    _ = agent.update(replay_buffer, config.BATCH_SIZE)

                # 更新状态
                state = next_state
                episode_reward += reward

                if done:
                    break

            # 记录episode结果
            completed_tasks = len(info.get('completed_tasks', set()))
            completion_rate = completed_tasks / num_tasks

            # 获取makespan（DAG完成时间）
            if data_loader._step_state is not None and 'total_completion_time' in data_loader._step_state:
                makespan = data_loader._step_state['total_completion_time'] / 1000.0  # 转换为秒
            else:
                makespan = info.get('total_time', 0) / 1000.0  # 备用方案

            episode_rewards.append(episode_reward)
            episode_completion_rates.append(completion_rate)
            episode_makespans.append(makespan)

            if completion_rate >= 0.8:
                successful_episodes += 1

            nan_count += episode_nan_count

            # 计算移动平均
            window_size = min(20, len(episode_rewards))
            avg_reward = np.mean(episode_rewards[-window_size:])
            avg_completion = np.mean(episode_completion_rates[-window_size:])
            avg_makespan = np.mean(episode_makespans[-window_size:])

            # 打印进度
            if episode % 20 == 0:
                print(f"Episode {episode:4d}: DAG {dag_idx}, Reward={episode_reward:8.2f}, "
                      f"Completion={completion_rate:.2f}, Makespan={makespan:.2f}s, "
                      f"Avg20_Reward={avg_reward:8.2f}, Avg20_Completion={avg_completion:.2f}, "
                      f"Avg20_Makespan={avg_makespan:.2f}s, NaN_Count={episode_nan_count}, "
                      f"Time={time.time()-start_time:.2f}s")

            # 保存模型
            if episode % save_interval == 0 and episode > 0:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                model_dir = f"models/xlstm_sac_{edge_num}devices_{timestamp}_ep{episode}"
                os.makedirs(model_dir, exist_ok=True)

                model_path = os.path.join(model_dir, "xlstm_sac_model.pth")
                agent.save_models(model_path)

                # 保存训练历史数据
                history_df = pd.DataFrame({
                    'Episode': range(1, len(episode_rewards) + 1),
                    'Reward': episode_rewards,
                    'Completion_Ratio': episode_completion_rates,
                    'Makespan': episode_makespans
                })
                history_df.to_excel(os.path.join(model_dir, "training_history.xlsx"), index=False)

                # 绘制训练曲线
                if len(episode_rewards) >= 50:
                    plot_training_history(episode_rewards, episode_completion_rates, episode_makespans, model_dir)

                print(f"✅ 模型和训练数据已保存到: {model_dir}")

        except Exception as e:
            print(f"❌ Episode {episode} 训练失败: {e}")
            continue

    # 保存最终模型和完整训练数据
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    final_model_dir = f"models/xlstm_sac_{edge_num}devices_final_{timestamp}"
    os.makedirs(final_model_dir, exist_ok=True)

    # 保存最终模型
    final_model_path = os.path.join(final_model_dir, "xlstm_sac_model.pth")
    agent.save_models(final_model_path)

    # 保存完整训练历史数据
    if episode_rewards:
        final_history_df = pd.DataFrame({
            'Episode': range(1, len(episode_rewards) + 1),
            'Reward': episode_rewards,
            'Completion_Ratio': episode_completion_rates,
            'Makespan': episode_makespans
        })
        final_history_df.to_excel(os.path.join(final_model_dir, "final_training_history.xlsx"), index=False)

        # 绘制最终训练曲线
        plot_training_history(episode_rewards, episode_completion_rates, episode_makespans, final_model_dir)

        # 保存训练统计摘要
        summary_stats = {
            'Total_Episodes': len(episode_rewards),
            'Successful_Episodes': successful_episodes,
            'Average_Reward': np.mean(episode_rewards),
            'Average_Completion_Rate': np.mean(episode_completion_rates),
            'Average_Makespan': np.mean(episode_makespans),
            'Total_NaN_Count': nan_count,
            'Success_Rate': successful_episodes/len(episode_rewards)*100,
            'Final_Reward': episode_rewards[-1] if episode_rewards else 0,
            'Final_Completion_Rate': episode_completion_rates[-1] if episode_completion_rates else 0,
            'Final_Makespan': episode_makespans[-1] if episode_makespans else 0
        }

        summary_df = pd.DataFrame([summary_stats])
        summary_df.to_excel(os.path.join(final_model_dir, "training_summary.xlsx"), index=False)

    print(f"🏆 最终模型和训练数据已保存到: {final_model_dir}")

    return agent, episode_rewards, episode_completion_rates, episode_makespans

def plot_training_history(rewards, completion_ratios, makespans, save_dir):
    """绘制训练历史曲线 - 按照原版代码风格，包含makespan"""
    # 创建保存目录
    os.makedirs(save_dir, exist_ok=True)

    # 设置绘图样式 - 按照原版代码
    plt.style.use('ggplot')
    plt.rcParams['font.family'] = 'DejaVu Sans'
    plt.rcParams['font.size'] = 12

    # 定义平滑窗口
    window_size = min(50, len(rewards))

    # 计算移动平均
    def moving_average(data, window_size):
        return pd.Series(data).rolling(window_size).mean().iloc[window_size-1:].values

    # 计算平滑曲线
    rewards_smooth = moving_average(rewards, window_size)
    completion_smooth = moving_average(completion_ratios, window_size)
    makespans_smooth = moving_average(makespans, window_size)

    # 1. 绘制奖励曲线
    plt.figure(figsize=(10, 6), dpi=100)
    plt.plot(rewards, alpha=0.3, color='blue', label='Raw Rewards')
    plt.plot(range(window_size-1, len(rewards)), rewards_smooth, color='blue', linewidth=2, label=f'Smoothed Rewards (window={window_size})')
    plt.xlabel('Episode')
    plt.ylabel('Reward')
    plt.title('xLSTM SAC Training Rewards')
    plt.legend()
    plt.grid(True)
    plt.savefig(f"{save_dir}/xlstm_training_rewards.png", dpi=300, bbox_inches='tight')
    plt.close()

    # 2. 绘制完成率曲线
    plt.figure(figsize=(10, 6), dpi=100)
    plt.plot(completion_ratios, alpha=0.3, color='green', label='Raw Completion Rate')
    plt.plot(range(window_size-1, len(completion_ratios)), completion_smooth, color='green', linewidth=2, label=f'Smoothed Completion Rate (window={window_size})')
    plt.xlabel('Episode')
    plt.ylabel('Completion Rate')
    plt.title('xLSTM SAC Task Completion Rate')
    plt.legend()
    plt.grid(True)
    plt.ylim(0, 1.1)
    # 添加百分比刻度
    plt.yticks(np.arange(0, 1.1, 0.1), [f'{int(x*100)}%' for x in np.arange(0, 1.1, 0.1)])
    plt.savefig(f"{save_dir}/xlstm_training_completion_rate.png", dpi=300, bbox_inches='tight')
    plt.close()
    # 3. 绘制Makespan曲线
    plt.figure(figsize=(10, 6), dpi=100)
    plt.plot(makespans, alpha=0.3, color='red', label='Raw Makespan')
    plt.plot(range(window_size-1, len(makespans)), makespans_smooth, color='red', linewidth=2, label=f'Smoothed Makespan (window={window_size})')
    plt.xlabel('Episode')
    plt.ylabel('Makespan (seconds)')
    plt.title('xLSTM SAC Makespan (DAG Completion Time)')
    plt.legend()
    plt.grid(True)
    plt.savefig(f"{save_dir}/xlstm_training_makespan.png", dpi=300, bbox_inches='tight')
    plt.close()

    # 4. 绘制组合图表
    _, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 15))

    # 奖励曲线
    ax1.plot(rewards, alpha=0.3, color='blue', label='Raw Rewards')
    ax1.plot(range(window_size-1, len(rewards)), rewards_smooth, color='blue', linewidth=2, label=f'Smoothed Rewards (window={window_size})')
    ax1.set_xlabel('Episode')
    ax1.set_ylabel('Reward')
    ax1.set_title('xLSTM SAC Training Rewards')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 完成率曲线
    ax2.plot(completion_ratios, alpha=0.3, color='green', label='Raw Completion Rate')
    ax2.plot(range(window_size-1, len(completion_ratios)), completion_smooth, color='green', linewidth=2, label=f'Smoothed Completion Rate (window={window_size})')
    ax2.set_xlabel('Episode')
    ax2.set_ylabel('Completion Rate')
    ax2.set_title('xLSTM SAC Task Completion Rate')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(0, 1.1)
    ax2.set_yticks(np.arange(0, 1.1, 0.1))
    ax2.set_yticklabels([f'{int(x*100)}%' for x in np.arange(0, 1.1, 0.1)])

    # Makespan曲线
    ax3.plot(makespans, alpha=0.3, color='red', label='Raw Makespan')
    ax3.plot(range(window_size-1, len(makespans)), makespans_smooth, color='red', linewidth=2, label=f'Smoothed Makespan (window={window_size})')
    ax3.set_xlabel('Episode')
    ax3.set_ylabel('Makespan (seconds)')
    ax3.set_title('xLSTM SAC Makespan (DAG Completion Time)')
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # 调整布局并保存
    plt.tight_layout()
    plt.savefig(f"{save_dir}/xlstm_training_history.png", dpi=300, bbox_inches='tight')
    plt.close()

    # 保存数据为Excel格式
    plot_data = pd.DataFrame({
        'Episode': range(1, len(rewards) + 1),
        'Reward': rewards,
        'Reward_Smoothed': np.append(np.full(window_size-1, np.nan), rewards_smooth),
        'Completion_Ratio': completion_ratios,
        'Completion_Ratio_Smoothed': np.append(np.full(window_size-1, np.nan), completion_smooth),
        'Makespan': makespans,
        'Makespan_Smoothed': np.append(np.full(window_size-1, np.nan), makespans_smooth)
    })

    plot_data.to_excel(f"{save_dir}/xlstm_training_plots_data.xlsx", index=False)

    print(f"xLSTM training history plots saved to {save_dir}")

def main():
    parser = argparse.ArgumentParser(description='xLSTM版增强LLM调度系统训练')
    parser.add_argument('--edge_num', type=int, default=5, help='边缘设备数量 (2-6)')
    parser.add_argument('--task_num', type=int, default=30, help='最大任务数量')
    parser.add_argument('--episodes', type=int, default=5000, help='训练回合数')
    parser.add_argument('--save_interval', type=int, default=500, help='模型保存间隔')
    parser.add_argument('--data_path', type=str, default=None, help='自定义数据路径')

    args = parser.parse_args()

    # 验证参数
    if args.edge_num < 2 or args.edge_num > 6:
        print("❌ 边缘设备数量必须在2-6之间")
        return

    if args.episodes <= 0:
        print("❌ 训练回合数必须大于0")
        return

    # 开始训练
    start_time = time.time()

    try:
        result = train_xlstm_sac(
            edge_num=args.edge_num,
            task_num=args.task_num,
            episodes=args.episodes,
            save_interval=args.save_interval,
            data_path=args.data_path
        )
    except Exception as e:
        print(f"❌ 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return

    if result is None:
        print("❌ 训练失败，请检查数据路径和配置")
        return

    _, rewards, completion_rates, makespans = result
    print("🎉 xLSTM版训练成功完成!")

    # 显示最终统计
    if rewards:
        print(f"\n📊 最终训练统计:")
        print(f"  总回合数: {len(rewards)}")
        print(f"  平均奖励: {np.mean(rewards):.2f}")
        print(f"  最终奖励: {rewards[-1]:.2f}")
        print(f"  平均完成率: {np.mean(completion_rates):.2%}")
        print(f"  最终完成率: {completion_rates[-1]:.2%}")
        print(f"  最高完成率: {np.max(completion_rates):.2%}")
        print(f"  平均Makespan: {np.mean(makespans):.2f}秒")
        print(f"  最终Makespan: {makespans[-1]:.2f}秒")
        print(f"  最佳Makespan: {np.min(makespans):.2f}秒")

        # 计算收敛性指标
        if len(rewards) >= 100:
            last_100_avg = np.mean(rewards[-100:])
            first_100_avg = np.mean(rewards[:100])
            improvement = ((last_100_avg - first_100_avg) / abs(first_100_avg)) * 100
            print(f"  训练改进: {improvement:.1f}% (最后100 vs 前100回合)")

    print(f"📁 所有训练数据和图表已保存到 models/ 目录")
    print(f"⏱️ 总训练时间: {time.time() - start_time:.1f}秒")

if __name__ == "__main__":
    main()
