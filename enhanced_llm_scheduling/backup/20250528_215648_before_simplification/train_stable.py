"""
稳定版增强LLM调度系统训练脚本
专门解决NaN值问题
"""
import torch
import numpy as np
import argparse
import os
import sys
import time
from datetime import datetime
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端

# 添加路径
sys.path.append('..')
sys.path.append('.')

from enhanced_config import EnhancedLLMConfig
from enhanced_data_loader import EnhancedDataLoader
from enhanced_sac_agent import EnhancedSACAgent

def create_ready_mask(data_loader, seq_len):
    """创建就绪任务掩码"""
    ready_tasks = data_loader.get_ready_tasks()
    ready_mask = np.zeros(seq_len)

    for task_idx in ready_tasks:
        if task_idx < seq_len:
            ready_mask[task_idx] = 1.0

    return ready_mask

def check_nan_values(tensor, name):
    """检查张量中的NaN值"""
    if torch.isnan(tensor).any():
        print(f"警告: {name} 包含NaN值")
        return True
    return False

def train_stable_sac(edge_num=5, task_num=200, episodes=300, save_interval=50, data_path=None):
    """训练稳定版增强SAC智能体"""

    print(f"🚀 开始稳定版训练 - {edge_num}个边缘设备")

    # 创建配置
    config = EnhancedLLMConfig()
    config.update_edge_servers(edge_num, task_num)

    # 如果提供了自定义数据路径，使用它
    if data_path:
        if not config.set_custom_data_path(data_path):
            print(f"无法使用指定的数据路径: {data_path}")
            return None

    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() and config.CUDA else 'cpu')
    print(f"使用设备: {device}")

    # 初始化数据加载器和智能体
    data_loader = EnhancedDataLoader(config, debug=False)
    agent = EnhancedSACAgent(config, device)

    # 训练统计
    episode_rewards = []
    episode_completion_rates = []
    nan_count = 0
    successful_episodes = 0

    print(f"\n📊 训练配置:")
    print(f"  边缘设备数: {config.NUM_EDGE_SERVERS}")
    print(f"  状态维度: {config.ENHANCED_STATE_DIM}")
    print(f"  学习率: {config.LEARNING_RATE}")
    print(f"  批次大小: {config.BATCH_SIZE}")
    print(f"  数据路径: {config.TRAINING_DATA_PATH}")

    for episode in range(episodes):
        episode_start_time = time.time()

        # 随机选择一个DAG
        dag_idx = np.random.randint(1, 201)

        try:
            # 加载DAG数据
            task_features = data_loader.load_task_features(dag_idx)
            adj_matrix = data_loader.load_adjacency_matrix(dag_idx)
            machine_resources = data_loader.load_machines_resource()
            comm_speed = data_loader.load_machine_commu_speed()
            memory_status = data_loader.load_memory_status()

            num_tasks = task_features.shape[1]

            if episode % 20 == 0:  # 每20个episode显示DAG信息
                print(f"\n[TRAIN] Episode {episode}: 加载DAG {dag_idx}, 任务数: {num_tasks}")
                llm_count = sum(1 for i in range(num_tasks) if task_features[2, i] == 1)
                print(f"[TRAIN] LLM任务数量: {llm_count}/{num_tasks}")

            # 重置环境
            data_loader._step_state = None

            # 获取初始状态
            state = data_loader.process_state_as_enhanced_sequence(
                task_features, adj_matrix, machine_resources, comm_speed, memory_status
            )

            # 检查初始状态
            if check_nan_values(torch.tensor(state), "初始状态"):
                print(f"Episode {episode}: 跳过，初始状态包含NaN")
                continue

            # 初始化环境状态
            data_loader._init_step_state(task_features, adj_matrix, machine_resources,
                                       comm_speed, memory_status)

            ready_mask = create_ready_mask(data_loader, config.SEQ_LEN)

            episode_reward = 0
            step_count = 0
            max_steps = num_tasks * 2
            episode_nan_count = 0

            while step_count < max_steps:
                # 选择动作
                action = agent.select_action(state, ready_mask, deterministic=False)

                # 检查动作
                if check_nan_values(torch.tensor(action), f"动作 step {step_count}"):
                    episode_nan_count += 1
                    # 使用默认动作
                    action = np.array([0.0, 0.0])

                # 执行动作
                next_state, reward, done, info = data_loader.step_enhanced_task(
                    action, debug=(episode % 20 == 0)  # 每20个episode显示详细调试信息
                )

                # 检查奖励和下一状态
                if np.isnan(reward):
                    reward = 0.0

                if check_nan_values(torch.tensor(next_state), f"下一状态 step {step_count}"):
                    # 使用当前状态作为下一状态
                    next_state = state.copy()

                # 创建下一个就绪掩码
                next_ready_mask = create_ready_mask(data_loader, config.SEQ_LEN)

                # 存储经验
                agent.store_transition(state, action, reward, next_state, done,
                                     ready_mask, next_ready_mask)

                # 更新状态
                state = next_state
                ready_mask = next_ready_mask
                episode_reward += reward
                step_count += 1

                # 每步显示简要信息（仅在调试episode中）
                if episode % 20 == 0:
                    executed_task = info.get('executed_task')
                    failed_task = info.get('failed_task')
                    current_completed = len(info.get('completed_tasks', set()))
                    current_failed = len(info.get('failed_tasks', set()))

                    if executed_task is not None:
                        print(f"    步骤{step_count}: 执行任务{executed_task}, 奖励{reward:.2f}, "
                              f"进度{current_completed + current_failed}/{num_tasks}")
                    elif failed_task is not None:
                        reason = info.get('reason', '未知')
                        print(f"    步骤{step_count}: 任务{failed_task}失败({reason}), 奖励{reward:.2f}, "
                              f"进度{current_completed + current_failed}/{num_tasks}")

                # 训练智能体（每5步训练一次，减少频率）
                if len(agent.replay_buffer) > agent.batch_size and step_count % 5 == 0:
                    try:
                        update_info = agent.update()
                        # 检查更新是否产生NaN
                        if any(np.isnan(v) for v in update_info.values() if isinstance(v, (int, float))):
                            print(f"警告: 更新产生NaN值")
                    except Exception as e:
                        print(f"更新失败: {e}")

                if done:
                    break

            # 计算完成率
            completed_tasks = len(info.get('completed_tasks', set()))
            failed_tasks = len(info.get('failed_tasks', set()))
            completion_rate = completed_tasks / num_tasks

            # 记录统计信息
            episode_rewards.append(episode_reward)
            episode_completion_rates.append(completion_rate)

            if episode_nan_count > 0:
                nan_count += episode_nan_count
            else:
                successful_episodes += 1

            # 详细的episode结束信息
            if episode % 20 == 0:
                print(f"[TRAIN] Episode {episode} 结束:")
                print(f"  完成任务: {completed_tasks}/{num_tasks} ({completion_rate:.1%})")
                print(f"  失败任务: {failed_tasks}")
                print(f"  总步数: {step_count}")
                print(f"  总奖励: {episode_reward:.2f}")
                print(f"  NaN计数: {episode_nan_count}")

                # 显示详细的任务完成情况
                if 'completed_tasks' in info:
                    completed_set = info['completed_tasks']
                    print(f"  已完成任务列表: {sorted(completed_set)}")

                if 'failed_tasks' in info:
                    failed_set = info.get('failed_tasks', set())
                    if failed_set:
                        print(f"  失败任务列表: {sorted(failed_set)}")

                # 显示DAG是否失败
                if info.get('dag_failed', False):
                    print(f"  ⚠️ DAG标记为失败")
                    if info.get('deadlock_detected', False):
                        print(f"  ❌ 检测到死锁")

                # 显示总完成时间
                if 'total_time' in info:
                    total_time_sec = info['total_time'] / 1000.0
                    print(f"  总完成时间: {total_time_sec:.3f}秒")

            # 打印进度
            if episode % 20 == 0:
                avg_reward = np.mean(episode_rewards[-20:]) if len(episode_rewards) >= 20 else np.mean(episode_rewards)
                avg_completion = np.mean(episode_completion_rates[-20:]) if len(episode_completion_rates) >= 20 else np.mean(episode_completion_rates)
                episode_time = time.time() - episode_start_time

                print(f"Episode {episode:4d}: "
                      f"DAG{dag_idx:3d}, "
                      f"Reward={episode_reward:8.2f}, "
                      f"Completion={completion_rate:.2f}, "
                      f"Avg20_Reward={avg_reward:8.2f}, "
                      f"Avg20_Completion={avg_completion:.2f}, "
                      f"NaN_Count={episode_nan_count}, "
                      f"Time={episode_time:.2f}s")

            # 保存模型和训练数据
            if episode % save_interval == 0 and episode > 0:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                model_dir = f"models/stable_sac_{edge_num}devices_{timestamp}_ep{episode}"
                os.makedirs(model_dir, exist_ok=True)

                # 保存模型
                model_path = os.path.join(model_dir, "stable_sac_model.pth")
                agent.save_models(model_path)

                # 保存训练历史数据
                history_df = pd.DataFrame({
                    'Episode': range(1, len(episode_rewards) + 1),
                    'Reward': episode_rewards,
                    'Completion_Ratio': episode_completion_rates
                })
                history_df.to_excel(os.path.join(model_dir, "training_history.xlsx"), index=False)

                # 绘制训练曲线
                if len(episode_rewards) >= 50:  # 至少有50个episode才绘制
                    plot_training_history(episode_rewards, episode_completion_rates, model_dir)

                print(f"✅ 模型和训练数据已保存到: {model_dir}")

        except Exception as e:
            print(f"Episode {episode} 出错: {e}")
            continue

    print("\n🎯 训练完成!")

    # 最终统计
    if episode_rewards:
        print(f"\n=== 稳定版训练统计 ===")
        print(f"总回合数: {len(episode_rewards)}")
        print(f"成功回合数: {successful_episodes}")
        print(f"平均奖励: {np.mean(episode_rewards):.2f}")
        print(f"平均完成率: {np.mean(episode_completion_rates):.2f}")
        print(f"总NaN计数: {nan_count}")
        print(f"成功率: {successful_episodes/len(episode_rewards)*100:.1f}%")

    # 保存最终模型和完整训练数据
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    final_model_dir = f"models/stable_sac_{edge_num}devices_final_{timestamp}"
    os.makedirs(final_model_dir, exist_ok=True)

    # 保存最终模型
    final_model_path = os.path.join(final_model_dir, "stable_sac_model.pth")
    agent.save_models(final_model_path)

    # 保存完整训练历史数据
    if episode_rewards:
        final_history_df = pd.DataFrame({
            'Episode': range(1, len(episode_rewards) + 1),
            'Reward': episode_rewards,
            'Completion_Ratio': episode_completion_rates
        })
        final_history_df.to_excel(os.path.join(final_model_dir, "final_training_history.xlsx"), index=False)

        # 绘制最终训练曲线
        plot_training_history(episode_rewards, episode_completion_rates, final_model_dir)

        # 保存训练统计摘要
        summary_stats = {
            'Total_Episodes': len(episode_rewards),
            'Successful_Episodes': successful_episodes,
            'Average_Reward': np.mean(episode_rewards),
            'Average_Completion_Rate': np.mean(episode_completion_rates),
            'Total_NaN_Count': nan_count,
            'Success_Rate': successful_episodes/len(episode_rewards)*100,
            'Final_Reward': episode_rewards[-1] if episode_rewards else 0,
            'Final_Completion_Rate': episode_completion_rates[-1] if episode_completion_rates else 0
        }

        summary_df = pd.DataFrame([summary_stats])
        summary_df.to_excel(os.path.join(final_model_dir, "training_summary.xlsx"), index=False)

    print(f"🏆 最终模型和训练数据已保存到: {final_model_dir}")

    return agent, episode_rewards, episode_completion_rates

def plot_training_history(rewards, completion_ratios, save_dir):
    """绘制训练历史曲线 - 按照原版代码风格"""
    # 创建保存目录
    os.makedirs(save_dir, exist_ok=True)

    # 设置绘图样式 - 按照原版代码
    plt.style.use('ggplot')
    plt.rcParams['font.family'] = 'DejaVu Sans'
    plt.rcParams['font.size'] = 12

    # 定义平滑窗口
    window_size = min(50, len(rewards))

    # 计算移动平均
    def moving_average(data, window_size):
        return pd.Series(data).rolling(window_size).mean().iloc[window_size-1:].values

    # 计算平滑曲线
    rewards_smooth = moving_average(rewards, window_size)
    completion_smooth = moving_average(completion_ratios, window_size)

    # 1. 绘制奖励曲线
    plt.figure(figsize=(10, 6), dpi=100)
    plt.plot(rewards, alpha=0.3, color='blue', label='Raw Rewards')
    plt.plot(range(window_size-1, len(rewards)), rewards_smooth, color='blue', linewidth=2, label=f'Smoothed Rewards (window={window_size})')
    plt.xlabel('Episode')
    plt.ylabel('Reward')
    plt.title('Training Rewards')
    plt.legend()
    plt.grid(True)
    plt.savefig(f"{save_dir}/training_rewards.png", dpi=300, bbox_inches='tight')
    plt.close()

    # 2. 绘制完成率曲线
    plt.figure(figsize=(10, 6), dpi=100)
    plt.plot(completion_ratios, alpha=0.3, color='green', label='Raw Completion Rate')
    plt.plot(range(window_size-1, len(completion_ratios)), completion_smooth, color='green', linewidth=2, label=f'Smoothed Completion Rate (window={window_size})')
    plt.xlabel('Episode')
    plt.ylabel('Completion Rate')
    plt.title('Task Completion Rate')
    plt.legend()
    plt.grid(True)
    plt.ylim(0, 1.1)
    # 添加百分比刻度
    plt.yticks(np.arange(0, 1.1, 0.1), [f'{int(x*100)}%' for x in np.arange(0, 1.1, 0.1)])
    plt.savefig(f"{save_dir}/training_completion_rate.png", dpi=300, bbox_inches='tight')
    plt.close()

    # 3. 绘制组合图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

    # 奖励曲线
    ax1.plot(rewards, alpha=0.3, color='blue', label='Raw Rewards')
    ax1.plot(range(window_size-1, len(rewards)), rewards_smooth, color='blue', linewidth=2, label=f'Smoothed Rewards (window={window_size})')
    ax1.set_xlabel('Episode')
    ax1.set_ylabel('Reward')
    ax1.set_title('Training Rewards')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 完成率曲线
    ax2.plot(completion_ratios, alpha=0.3, color='green', label='Raw Completion Rate')
    ax2.plot(range(window_size-1, len(completion_ratios)), completion_smooth, color='green', linewidth=2, label=f'Smoothed Completion Rate (window={window_size})')
    ax2.set_xlabel('Episode')
    ax2.set_ylabel('Completion Rate')
    ax2.set_title('Task Completion Rate')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(0, 1.1)
    ax2.set_yticks(np.arange(0, 1.1, 0.1))
    ax2.set_yticklabels([f'{int(x*100)}%' for x in np.arange(0, 1.1, 0.1)])

    # 调整布局并保存
    plt.tight_layout()
    plt.savefig(f"{save_dir}/training_history.png", dpi=300, bbox_inches='tight')
    plt.close()

    # 保存数据为Excel格式
    plot_data = pd.DataFrame({
        'Episode': range(1, len(rewards) + 1),
        'Reward': rewards,
        'Reward_Smoothed': np.append(np.full(window_size-1, np.nan), rewards_smooth),
        'Completion_Ratio': completion_ratios,
        'Completion_Ratio_Smoothed': np.append(np.full(window_size-1, np.nan), completion_smooth)
    })

    plot_data.to_excel(f"{save_dir}/training_plots_data.xlsx", index=False)

    print(f"Training history plots saved to {save_dir}")

def main():
    parser = argparse.ArgumentParser(description='稳定版增强LLM调度系统训练')
    parser.add_argument('--edge_num', type=int, default=5, help='边缘设备数量 (2-6)')
    parser.add_argument('--task_num', type=int, default=30, help='最大任务数量')
    parser.add_argument('--episodes', type=int, default=300, help='训练回合数')
    parser.add_argument('--save_interval', type=int, default=50, help='模型保存间隔')
    parser.add_argument('--data_path', type=str, default=None, help='自定义数据路径')

    args = parser.parse_args()


    print(f"🔧 稳定版训练配置:")
    print(f"  边缘设备数量: {args.edge_num}")
    print(f"  最大任务数量: {args.task_num}")
    print(f"  训练回合数: {args.episodes}")
    print(f"  保存间隔: {args.save_interval}")
    if args.data_path:
        print(f"  自定义数据路径: {args.data_path}")

    # 开始训练
    result = train_stable_sac(
        edge_num=args.edge_num,
        task_num=args.task_num,
        episodes=args.episodes,
        save_interval=args.save_interval,
        data_path=args.data_path
    )

    if result is None:
        print("❌ 训练失败，请检查数据路径和配置")
        return

    agent, rewards, completion_rates = result
    print("🎉 稳定版训练成功完成!")

    # 显示最终统计
    if rewards:
        print(f"\n📊 最终训练统计:")
        print(f"  总回合数: {len(rewards)}")
        print(f"  平均奖励: {np.mean(rewards):.2f}")
        print(f"  最终奖励: {rewards[-1]:.2f}")
        print(f"  平均完成率: {np.mean(completion_rates):.2%}")
        print(f"  最终完成率: {completion_rates[-1]:.2%}")
        print(f"  最高完成率: {np.max(completion_rates):.2%}")

        # 计算收敛性指标
        if len(rewards) >= 100:
            last_100_avg = np.mean(rewards[-100:])
            first_100_avg = np.mean(rewards[:100])
            improvement = ((last_100_avg - first_100_avg) / abs(first_100_avg)) * 100
            print(f"  训练改进: {improvement:.1f}% (最后100 vs 前100回合)")

    print(f"📁 所有训练数据和图表已保存到 models/ 目录")

if __name__ == "__main__":
    main()
