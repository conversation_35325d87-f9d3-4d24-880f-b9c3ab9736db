"""
增强的经验回放缓冲区
支持存储任务索引和任务嵌入，解决SAC训练中的任务嵌入一致性问题
"""
import numpy as np
import torch

class EnhancedSequenceReplayBuffer:
    """增强的序列经验回放缓冲区 - 存储任务索引和嵌入"""
    
    def __init__(self, seq_len, feature_dim, action_dim, max_size=1000000):
        """
        初始化增强的序列经验回放缓冲区
        
        参数:
            seq_len: 序列长度
            feature_dim: 特征维度
            action_dim: 动作维度
            max_size: 缓冲区最大容量
        """
        self.max_size = max_size
        self.ptr = 0
        self.size = 0
        self.seq_len = seq_len
        self.feature_dim = feature_dim
        
        # 基础经验数据
        self.state = np.zeros((max_size, seq_len, feature_dim))
        self.action = np.zeros((max_size, action_dim))
        self.next_state = np.zeros((max_size, seq_len, feature_dim))
        self.reward = np.zeros((max_size, 1))
        self.done = np.zeros((max_size, 1))
        
        # 增强数据：任务选择相关
        self.selected_task_indices = np.zeros((max_size, 1), dtype=np.int32)  # 选定的任务索引
        self.ready_masks = np.zeros((max_size, seq_len), dtype=np.bool_)      # 就绪任务掩码
        self.next_ready_masks = np.zeros((max_size, seq_len), dtype=np.bool_) # 下一状态的就绪掩码
        
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        print(f"[ENHANCED_REPLAY_BUFFER] 初始化完成")
        print(f"  序列长度: {seq_len}, 特征维度: {feature_dim}")
        print(f"  动作维度: {action_dim}, 最大容量: {max_size}")
    
    def add(self, state, action, next_state, reward, done, 
            selected_task_index, ready_mask=None, next_ready_mask=None):
        """
        添加经验到缓冲区
        
        参数:
            state: 状态 [seq_len, feature_dim]
            action: 动作 [action_dim]
            next_state: 下一个状态 [seq_len, feature_dim]
            reward: 奖励 [1]
            done: 是否结束 [1]
            selected_task_index: 选定的任务索引 [1]
            ready_mask: 就绪任务掩码 [seq_len]
            next_ready_mask: 下一状态的就绪掩码 [seq_len]
        """
        self.state[self.ptr] = state
        self.action[self.ptr] = action
        self.next_state[self.ptr] = next_state
        self.reward[self.ptr] = reward
        self.done[self.ptr] = done
        self.selected_task_indices[self.ptr] = selected_task_index
        
        if ready_mask is not None:
            self.ready_masks[self.ptr] = ready_mask
        else:
            self.ready_masks[self.ptr] = np.ones(self.seq_len, dtype=np.bool_)
            
        if next_ready_mask is not None:
            self.next_ready_masks[self.ptr] = next_ready_mask
        else:
            self.next_ready_masks[self.ptr] = np.ones(self.seq_len, dtype=np.bool_)
        
        self.ptr = (self.ptr + 1) % self.max_size
        self.size = min(self.size + 1, self.max_size)
    
    def sample(self, batch_size):
        """
        从缓冲区中采样经验
        
        参数:
            batch_size: 批量大小
            
        返回:
            batch: 字典包含所有采样的数据
        """
        ind = np.random.randint(0, self.size, size=batch_size)
        
        batch = {
            'state': torch.FloatTensor(self.state[ind]).to(self.device),
            'action': torch.FloatTensor(self.action[ind]).to(self.device),
            'next_state': torch.FloatTensor(self.next_state[ind]).to(self.device),
            'reward': torch.FloatTensor(self.reward[ind]).to(self.device),
            'done': torch.FloatTensor(self.done[ind]).to(self.device),
            'selected_task_indices': torch.LongTensor(self.selected_task_indices[ind].flatten()).to(self.device),
            'ready_masks': torch.BoolTensor(self.ready_masks[ind]).to(self.device),
            'next_ready_masks': torch.BoolTensor(self.next_ready_masks[ind]).to(self.device)
        }
        
        return batch
    
    def __len__(self):
        """返回缓冲区中的样本数量"""
        return self.size
    
    def save(self, filename):
        """
        保存缓冲区
        
        参数:
            filename: 文件名
        """
        np.savez(
            filename,
            state=self.state[:self.size],
            action=self.action[:self.size],
            next_state=self.next_state[:self.size],
            reward=self.reward[:self.size],
            done=self.done[:self.size],
            selected_task_indices=self.selected_task_indices[:self.size],
            ready_masks=self.ready_masks[:self.size],
            next_ready_masks=self.next_ready_masks[:self.size]
        )
        print(f"[ENHANCED_REPLAY_BUFFER] 已保存到 {filename}")
    
    def load(self, filename):
        """
        加载缓冲区
        
        参数:
            filename: 文件名
        """
        data = np.load(filename)
        self.size = len(data['state'])
        self.state[:self.size] = data['state']
        self.action[:self.size] = data['action']
        self.next_state[:self.size] = data['next_state']
        self.reward[:self.size] = data['reward']
        self.done[:self.size] = data['done']
        self.selected_task_indices[:self.size] = data['selected_task_indices']
        self.ready_masks[:self.size] = data['ready_masks']
        self.next_ready_masks[:self.size] = data['next_ready_masks']
        print(f"[ENHANCED_REPLAY_BUFFER] 已从 {filename} 加载 {self.size} 个样本")

class TaskEmbeddingCache:
    """任务嵌入缓存 - 用于存储和检索任务嵌入"""
    
    def __init__(self, max_cache_size=10000):
        """
        初始化任务嵌入缓存
        
        参数:
            max_cache_size: 最大缓存大小
        """
        self.max_cache_size = max_cache_size
        self.cache = {}  # {(state_hash, task_index): embedding}
        self.access_order = []  # LRU缓存
        
    def _get_state_hash(self, state):
        """获取状态的哈希值"""
        # 简单的哈希方法，实际使用中可能需要更复杂的方法
        return hash(state.data.tobytes())
    
    def store(self, state, task_index, embedding):
        """
        存储任务嵌入
        
        参数:
            state: 状态张量
            task_index: 任务索引
            embedding: 任务嵌入
        """
        state_hash = self._get_state_hash(state)
        key = (state_hash, task_index.item())
        
        # 如果缓存已满，删除最旧的条目
        if len(self.cache) >= self.max_cache_size:
            oldest_key = self.access_order.pop(0)
            if oldest_key in self.cache:
                del self.cache[oldest_key]
        
        self.cache[key] = embedding.clone().detach()
        self.access_order.append(key)
    
    def retrieve(self, state, task_index):
        """
        检索任务嵌入
        
        参数:
            state: 状态张量
            task_index: 任务索引
            
        返回:
            embedding: 任务嵌入，如果不存在则返回None
        """
        state_hash = self._get_state_hash(state)
        key = (state_hash, task_index.item())
        
        if key in self.cache:
            # 更新访问顺序
            self.access_order.remove(key)
            self.access_order.append(key)
            return self.cache[key]
        
        return None
    
    def clear(self):
        """清空缓存"""
        self.cache.clear()
        self.access_order.clear()
    
    def size(self):
        """返回缓存大小"""
        return len(self.cache)

def test_enhanced_replay_buffer():
    """测试增强的replay buffer"""
    print("🧪 测试增强的Replay Buffer")
    
    # 创建测试数据
    seq_len, feature_dim, action_dim = 32, 41, 2
    buffer = EnhancedSequenceReplayBuffer(seq_len, feature_dim, action_dim, max_size=1000)
    
    # 添加一些测试数据
    for i in range(10):
        state = np.random.randn(seq_len, feature_dim)
        action = np.random.randn(action_dim)
        next_state = np.random.randn(seq_len, feature_dim)
        reward = np.random.randn(1)
        done = np.array([False])
        selected_task_index = np.array([i % seq_len])
        ready_mask = np.random.choice([True, False], size=seq_len)
        next_ready_mask = np.random.choice([True, False], size=seq_len)
        
        buffer.add(state, action, next_state, reward, done, 
                  selected_task_index, ready_mask, next_ready_mask)
    
    print(f"✅ 添加了 {len(buffer)} 个样本")
    
    # 采样测试
    batch = buffer.sample(5)
    print(f"✅ 采样批次包含以下键: {list(batch.keys())}")
    print(f"✅ 状态形状: {batch['state'].shape}")
    print(f"✅ 动作形状: {batch['action'].shape}")
    print(f"✅ 选定任务索引形状: {batch['selected_task_indices'].shape}")
    print(f"✅ 就绪掩码形状: {batch['ready_masks'].shape}")
    
    print("🎉 增强Replay Buffer测试通过！")

if __name__ == "__main__":
    test_enhanced_replay_buffer()
