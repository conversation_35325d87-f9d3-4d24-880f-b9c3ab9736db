#!/usr/bin/env python3
"""
简化版xLSTM训练脚本 - 更接近原版的设计
目标：验证原版能收敛的关键因素
"""

import os
import sys
import time
import argparse
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
from datetime import datetime

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_data_loader import EnhancedDataLoader
from improved_xlstm_networks import ImprovedXLSTMSACAgent
import config

def simple_reward_function(step_info, episode_info=None):
    """
    简化的奖励函数 - 更接近原版设计
    """
    if episode_info is not None:
        # 回合级奖励 - 只关注完成率
        completion_rate = episode_info['completed_tasks'] / episode_info['total_tasks']
        return completion_rate * 100.0, {'completion_reward': completion_rate * 100.0}
    
    # 步骤级奖励 - 极简设计
    reward = 0.0
    components = {}
    
    if step_info.get('task_completed_successfully', False):
        reward += 1.0  # 简单的任务完成奖励
        components['task_completion'] = 1.0
    
    if step_info.get('task_failed_memory', False):
        reward -= 5.0  # 内存失败惩罚
        components['memory_fail'] = -5.0
    
    return reward, components

def train_simplified_xlstm(data_path, num_episodes=1000, save_interval=500, 
                          device_num=5, device='cuda'):
    """简化版训练函数"""
    
    print(f"🚀 启动简化版xLSTM训练")
    print(f"  数据路径: {data_path}")
    print(f"  训练回合: {num_episodes}")
    print(f"  边缘设备数: {device_num}")
    print(f"  计算设备: {device}")
    
    # 创建数据加载器
    data_loader = EnhancedDataLoader(config, debug=False)  # 关闭详细调试
    
    # 创建智能体 - 使用更小的网络
    agent = ImprovedXLSTMSACAgent(
        state_dim=config.STATE_DIM,
        action_dim=2,
        hidden_dim=128,  # 减小隐藏维度
        seq_len=config.SEQ_LEN,
        num_machines=device_num + 1,
        xlstm_layers=1,  # 减少层数
        lr=1e-3,  # 更大的学习率
        device=device
    )
    
    # 设置温度参数增强探索
    agent.actor.temperature = 3.0
    
    print(f"\n🎯 简化奖励函数: 任务完成+1, 内存失败-5, 回合完成率×100")
    
    # 创建保存目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_dir = f"models/simplified_xlstm_sac_{device_num}devices_{timestamp}"
    os.makedirs(model_dir, exist_ok=True)
    
    # 训练历史记录
    episode_rewards = []
    episode_completion_rates = []
    episode_makespans = []
    successful_episodes = 0
    start_time = time.time()
    
    print(f"\n🎯 开始简化训练...")
    
    for episode in range(num_episodes):
        try:
            # 随机选择DAG
            dag_idx = np.random.randint(0, 200)
            
            # 加载DAG数据
            task_features = data_loader.load_task_features(dag_idx)
            adj_matrix = data_loader.load_adjacency_matrix(dag_idx)
            machine_resources = data_loader.load_machines_resource()
            comm_speed = data_loader.load_machine_commu_speed()
            memory_status = data_loader.load_memory_status()
            num_tasks = task_features.shape[1]
            
            # 初始化环境
            data_loader._init_step_state(task_features, adj_matrix, machine_resources, comm_speed, memory_status)
            
            # 获取初始状态
            state = data_loader.process_state_as_enhanced_sequence(
                task_features, adj_matrix, machine_resources, comm_speed, memory_status)
            
            episode_reward = 0
            step_count = 0
            max_steps = num_tasks * 2
            
            # 执行回合
            while step_count < max_steps:
                step_count += 1
                
                # 获取就绪任务掩码
                ready_tasks = data_loader.get_ready_tasks()
                if not ready_tasks:
                    break
                
                ready_mask = np.zeros(config.SEQ_LEN)
                for i, _ in enumerate(ready_tasks[:config.SEQ_LEN]):
                    if i < config.SEQ_LEN:
                        ready_mask[i] = 1.0
                
                # 转换为tensor
                state_tensor = torch.FloatTensor(state).unsqueeze(0).to(device)
                ready_mask_tensor = torch.FloatTensor(ready_mask).unsqueeze(0).to(device)
                
                # 选择动作
                action = agent.select_action(state_tensor, ready_mask_tensor, deterministic=False)
                
                # 检查NaN
                if np.isnan(action).any():
                    action = np.array([0.0, 0.0])
                
                # 执行动作
                try:
                    next_state, _, done, info = data_loader.step_enhanced_task(action, debug=False)
                    
                    # 简化的步骤奖励
                    executed_task = info.get('executed_task', -1)
                    failed_task = info.get('failed_task', -1)
                    
                    step_info = {
                        'task_completed_successfully': executed_task >= 0,
                        'task_failed_memory': failed_task >= 0 and info.get('reason') == 'memory_insufficient'
                    }
                    
                    step_reward, _ = simple_reward_function(step_info)
                    episode_reward += step_reward
                    
                    # 存储经验
                    agent.store_transition(state, action, step_reward, next_state, done, ready_mask)
                    
                    # 更新网络
                    if len(agent.replay_buffer) > 256:
                        agent.update(batch_size=128)  # 更小的batch size
                    
                    if done:
                        # 回合结束奖励
                        completed_tasks = len(info.get('completed_tasks', set()))
                        episode_info = {
                            'total_tasks': num_tasks,
                            'completed_tasks': completed_tasks
                        }
                        final_reward, _ = simple_reward_function({}, episode_info)
                        episode_reward += final_reward
                        break
                    
                    state = next_state
                    
                except Exception as e:
                    episode_reward -= 1.0
                    break
            
            # 记录episode结果
            if 'info' in locals() and info is not None:
                completed_tasks = len(info.get('completed_tasks', set()))
            else:
                completed_tasks = 0
            completion_rate = completed_tasks / num_tasks
            
            # 获取makespan
            if data_loader._step_state is not None and 'total_completion_time' in data_loader._step_state:
                makespan = data_loader._step_state['total_completion_time'] / 1000.0
            else:
                makespan = 0.0
            
            episode_rewards.append(episode_reward)
            episode_completion_rates.append(completion_rate)
            episode_makespans.append(makespan)
            
            if completion_rate >= 0.8:
                successful_episodes += 1
            
            # 打印进度
            if episode % 50 == 0:
                avg_reward = np.mean(episode_rewards[-50:]) if len(episode_rewards) >= 50 else np.mean(episode_rewards)
                avg_completion = np.mean(episode_completion_rates[-50:]) if len(episode_completion_rates) >= 50 else np.mean(episode_completion_rates)
                print(f"Episode {episode:4d}: Reward={episode_reward:6.2f}, "
                      f"Completion={completion_rate:.2f}, "
                      f"Avg50_Reward={avg_reward:6.2f}, Avg50_Completion={avg_completion:.2f}, "
                      f"Success_Rate={successful_episodes/(episode+1)*100:.1f}%")
            
            # 定期保存
            if (episode + 1) % save_interval == 0:
                print(f"\n💾 保存中间结果 (Episode {episode + 1})...")
                intermediate_model_dir = f"{model_dir}/checkpoint_{episode + 1}"
                os.makedirs(intermediate_model_dir, exist_ok=True)
                agent.save_models(f"{intermediate_model_dir}/simplified_xlstm_sac_model.pth")
                
                # 保存训练历史
                history_df = pd.DataFrame({
                    'Episode': range(1, len(episode_rewards) + 1),
                    'Reward': episode_rewards,
                    'Completion_Ratio': episode_completion_rates,
                    'Makespan': episode_makespans
                })
                history_df.to_excel(f"{intermediate_model_dir}/training_history.xlsx", index=False)
                print(f"✅ 中间结果已保存到: {intermediate_model_dir}")
        
        except Exception as e:
            print(f"❌ Episode {episode} 训练失败: {e}")
            episode_rewards.append(-10.0)
            episode_completion_rates.append(0.0)
            episode_makespans.append(0.0)
            continue
    
    # 保存最终结果
    final_model_dir = f"{model_dir}/final"
    os.makedirs(final_model_dir, exist_ok=True)
    
    print(f"\n💾 保存最终模型...")
    agent.save_models(f"{final_model_dir}/simplified_xlstm_sac_model.pth")
    
    if episode_rewards:
        final_history_df = pd.DataFrame({
            'Episode': range(1, len(episode_rewards) + 1),
            'Reward': episode_rewards,
            'Completion_Ratio': episode_completion_rates,
            'Makespan': episode_makespans
        })
        final_history_df.to_excel(f"{final_model_dir}/final_training_history.xlsx", index=False)
        
        print(f"\n📊 简化版训练完成统计:")
        print(f"  总回合数: {len(episode_rewards)}")
        print(f"  成功回合数: {successful_episodes}")
        print(f"  平均奖励: {np.mean(episode_rewards):.2f}")
        print(f"  平均完成率: {np.mean(episode_completion_rates):.2%}")
        print(f"  平均Makespan: {np.mean(episode_makespans):.2f}秒")
        print(f"  成功率: {successful_episodes/len(episode_rewards)*100:.1f}%")
    
    print(f"🏆 简化版模型和训练数据已保存到: {final_model_dir}")
    
    return agent, episode_rewards, episode_completion_rates, episode_makespans

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='训练简化版xLSTM调度系统')
    parser.add_argument('--data_path', type=str,
                       default='../dataset/training/DAG_30_edges_5_mem_115-128GB_density_0.4_0.8',
                       help='训练数据路径')
    parser.add_argument('--episodes', type=int, default=1000, help='训练回合数')
    parser.add_argument('--save_interval', type=int, default=500, help='保存间隔')
    parser.add_argument('--device_num', type=int, default=5, help='边缘设备数量')
    parser.add_argument('--device', type=str, default='cuda', help='计算设备')
    
    args = parser.parse_args()
    
    print("🚀 启动简化版xLSTM调度系统训练")
    print(f"参数: {args}")
    
    try:
        result = train_simplified_xlstm(
            data_path=args.data_path,
            num_episodes=args.episodes,
            save_interval=args.save_interval,
            device_num=args.device_num,
            device=args.device
        )
        
        _, rewards, completion_rates, makespans = result
        print("🎉 简化版xLSTM训练成功完成!")
        
        # 显示最终统计
        if rewards:
            print(f"\n📊 最终训练统计:")
            print(f"  总回合数: {len(rewards)}")
            print(f"  平均奖励: {np.mean(rewards):.2f}")
            print(f"  最终奖励: {rewards[-1]:.2f}")
            print(f"  平均完成率: {np.mean(completion_rates):.2%}")
            print(f"  最终完成率: {completion_rates[-1]:.2%}")
            print(f"  最高完成率: {np.max(completion_rates):.2%}")
    
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
