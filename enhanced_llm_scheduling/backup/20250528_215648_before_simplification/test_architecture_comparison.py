#!/usr/bin/env python3
"""
测试三种网络架构的差异
"""

import torch
import numpy as np
import time
import sys
import os

# 添加路径
sys.path.append('..')
sys.path.append('.')

from enhanced_config import EnhancedLLMConfig
from enhanced_sac_networks import EnhancedActorNetwork, EnhancedCriticNetwork
from lstm_sac_networks import LSTMActorNetwork, LSTMCriticNetwork
from xlstm_sac_networks import XLSTMActorNetwork, XLSTMCriticNetwork

def create_test_data(batch_size=4, seq_len=32, state_dim=41):
    """创建测试数据"""
    # 创建随机状态数据
    state = torch.randn(batch_size, seq_len, state_dim)

    # 创建就绪掩码
    ready_mask = torch.ones(batch_size, seq_len)
    # 随机设置一些任务为不就绪
    for i in range(batch_size):
        num_ready = np.random.randint(5, seq_len)
        ready_mask[i, num_ready:] = 0

    # 创建动作数据
    action = torch.randn(batch_size, 2)  # 复合动作

    return state, ready_mask, action

def test_network_architecture(network_class, network_name, state, ready_mask, action, **kwargs):
    """测试单个网络架构"""
    print(f"\n🔧 测试 {network_name} 架构")
    print("-" * 50)

    try:
        # 创建网络
        if 'Actor' in network_class.__name__:
            if 'XLSTM' in network_class.__name__:
                network = network_class(
                    state_dim=state.shape[-1],
                    action_dim=2,
                    seq_len=state.shape[1],
                    task_feature_dim=state.shape[-1],
                    **kwargs
                )
            else:
                network = network_class(
                    state_dim=state.shape[-1],
                    action_dim=2,
                    seq_len=state.shape[1],
                    **kwargs
                )
        else:  # Critic
            if 'XLSTM' in network_class.__name__:
                network = network_class(
                    state_dim=state.shape[-1],
                    action_dim=2,
                    seq_len=state.shape[1],
                    task_feature_dim=state.shape[-1],
                    **kwargs
                )
            else:
                network = network_class(
                    state_dim=state.shape[-1],
                    action_dim=2,
                    seq_len=state.shape[1],
                    **kwargs
                )

        # 计算参数数量
        total_params = sum(p.numel() for p in network.parameters())
        trainable_params = sum(p.numel() for p in network.parameters() if p.requires_grad)

        print(f"  总参数数量: {total_params:,}")
        print(f"  可训练参数: {trainable_params:,}")

        # 测试前向传播
        start_time = time.time()

        if 'Actor' in network_class.__name__:
            if 'XLSTM' in network_class.__name__:
                # xLSTM版本支持task_idx参数
                output = network(state, ready_mask)
                if hasattr(network, 'sample'):
                    actions, log_probs = network.sample(state, ready_mask)
                    print(f"  采样动作形状: {actions.shape}")
                    print(f"  log概率形状: {log_probs.shape}")
            else:
                output = network(state, ready_mask)
                if hasattr(network, 'sample'):
                    actions, log_probs = network.sample(state, ready_mask)
                    print(f"  采样动作形状: {actions.shape}")
                    print(f"  log概率形状: {log_probs.shape}")

            print(f"  输出长度: {len(output)}")
            print(f"  输出形状: {[o.shape for o in output]}")
        else:  # Critic
            q_value = network(state, action)
            print(f"  Q值形状: {q_value.shape}")
            print(f"  Q值范围: [{q_value.min().item():.3f}, {q_value.max().item():.3f}]")

        forward_time = time.time() - start_time
        print(f"  前向传播时间: {forward_time*1000:.2f}ms")

        # 测试梯度计算
        start_time = time.time()
        if 'Actor' in network_class.__name__:
            loss = sum(o.mean() for o in output)
        else:
            loss = q_value.mean()

        loss.backward()
        backward_time = time.time() - start_time
        print(f"  反向传播时间: {backward_time*1000:.2f}ms")

        # 检查梯度
        grad_norms = []
        for name, param in network.named_parameters():
            if param.grad is not None:
                grad_norm = param.grad.norm().item()
                grad_norms.append(grad_norm)

        if grad_norms:
            print(f"  梯度范数: 平均={np.mean(grad_norms):.6f}, 最大={np.max(grad_norms):.6f}")

        print(f"  ✅ {network_name} 测试成功")

        return {
            'name': network_name,
            'total_params': total_params,
            'trainable_params': trainable_params,
            'forward_time': forward_time * 1000,
            'backward_time': backward_time * 1000,
            'avg_grad_norm': np.mean(grad_norms) if grad_norms else 0,
            'max_grad_norm': np.max(grad_norms) if grad_norms else 0,
            'success': True
        }

    except Exception as e:
        print(f"  ❌ {network_name} 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return {
            'name': network_name,
            'success': False,
            'error': str(e)
        }

def main():
    print("🧪 网络架构对比测试")
    print("=" * 80)

    # 创建测试数据
    config = EnhancedLLMConfig()
    batch_size = 4
    seq_len = 32
    state_dim = config.ENHANCED_STATE_DIM

    print(f"📊 测试配置:")
    print(f"  批次大小: {batch_size}")
    print(f"  序列长度: {seq_len}")
    print(f"  状态维度: {state_dim}")

    state, ready_mask, action = create_test_data(batch_size, seq_len, state_dim)
    print(f"  状态形状: {state.shape}")
    print(f"  就绪掩码形状: {ready_mask.shape}")
    print(f"  动作形状: {action.shape}")

    # 定义要测试的网络架构
    architectures = [
        # Actor网络
        (EnhancedActorNetwork, "增强版Actor"),
        (LSTMActorNetwork, "LSTM版Actor"),
        (XLSTMActorNetwork, "真正xLSTM版Actor"),

        # Critic网络
        (EnhancedCriticNetwork, "增强版Critic"),
        (LSTMCriticNetwork, "LSTM版Critic"),
        (XLSTMCriticNetwork, "真正xLSTM版Critic"),
    ]

    # 测试所有架构
    results = []
    for network_class, network_name in architectures:
        result = test_network_architecture(
            network_class, network_name, state, ready_mask, action
        )
        results.append(result)

    # 汇总结果
    print("\n" + "=" * 80)
    print("📋 测试结果汇总")
    print("=" * 80)

    successful_results = [r for r in results if r.get('success', False)]
    failed_results = [r for r in results if not r.get('success', False)]

    if successful_results:
        print(f"\n✅ 成功的架构 ({len(successful_results)}):")
        print(f"{'架构名称':<20} {'参数数量':<12} {'前向时间':<10} {'反向时间':<10} {'平均梯度':<12}")
        print("-" * 70)

        for result in successful_results:
            print(f"{result['name']:<20} "
                  f"{result['total_params']:>10,} "
                  f"{result['forward_time']:>8.2f}ms "
                  f"{result['backward_time']:>8.2f}ms "
                  f"{result['avg_grad_norm']:>10.6f}")

    if failed_results:
        print(f"\n❌ 失败的架构 ({len(failed_results)}):")
        for result in failed_results:
            print(f"  - {result['name']}: {result.get('error', '未知错误')}")

    # 性能对比
    if len(successful_results) >= 2:
        print(f"\n📊 性能对比:")

        # 参数数量对比
        params = [(r['name'], r['total_params']) for r in successful_results]
        params.sort(key=lambda x: x[1])
        print(f"  参数数量排序 (少→多):")
        for name, param_count in params:
            print(f"    {name}: {param_count:,}")

        # 速度对比
        speeds = [(r['name'], r['forward_time'] + r['backward_time']) for r in successful_results]
        speeds.sort(key=lambda x: x[1])
        print(f"  总体速度排序 (快→慢):")
        for name, total_time in speeds:
            print(f"    {name}: {total_time:.2f}ms")

    # 架构差异总结
    print(f"\n🎯 架构差异总结:")
    print(f"  1. 增强版和LSTM版本: 复合动作架构，同时输出任务选择和机器分配")
    print(f"  2. 真正xLSTM版本: Encoder-Decoder架构，支持逐任务调度")
    print(f"  3. 参数数量: xLSTM > 增强版 ≈ LSTM版本")
    print(f"  4. 计算速度: LSTM版本 > 增强版 > xLSTM版本")
    print(f"  5. 表达能力: xLSTM版本 > 增强版 > LSTM版本")

    success_rate = len(successful_results) / len(results)
    print(f"\n🏆 总体测试结果:")
    print(f"  成功率: {success_rate:.1%} ({len(successful_results)}/{len(results)})")

    if success_rate >= 0.8:
        print(f"  ✅ 大部分架构测试通过，系统运行正常")
        return True
    else:
        print(f"  ⚠️ 部分架构测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 架构对比测试完成！")
    else:
        print("\n💥 架构对比测试存在问题！")
        sys.exit(1)
