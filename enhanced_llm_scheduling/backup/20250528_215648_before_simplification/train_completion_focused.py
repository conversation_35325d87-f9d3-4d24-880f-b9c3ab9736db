#!/usr/bin/env python3
"""
完成率专注训练脚本
第一阶段训练：专注于提高任务完成率，不考虑makespan
"""

import os
import sys
import torch
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import json

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from enhanced_data_loader import EnhancedDataLoader
from improved_reward_function import ImprovedRewardFunction
from xlstm_sac_agent import XLSTMSACAgent
import config

def train_completion_focused(device_num=6, episodes=1500, save_interval=300, 
                           detailed_debug=False, data_path=None):
    """
    完成率专注训练函数
    """
    print(f"🎯 开始完成率专注训练")
    print(f"   设备数量: {device_num}")
    print(f"   训练回合: {episodes}")
    print(f"   保存间隔: {save_interval}")
    print(f"   训练目标: 最大化任务完成率")
    
    # 设置设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"   使用设备: {device}")
    
    # 数据路径
    if data_path is None:
        data_path = f"../dataset/training/DAG_30_edges_{device_num}_mem_25-38GB_density_0.4_0.6"
    
    print(f"   数据路径: {data_path}")
    
    # 创建数据加载器
    data_loader = EnhancedDataLoader(
        data_path=data_path,
        num_machines=device_num + 1,
        debug=detailed_debug
    )
    
    # 创建智能体
    agent = XLSTMSACAgent(
        state_dim=config.ENHANCED_STATE_DIM,
        action_dim=2,
        hidden_dim=256,
        seq_len=config.SEQ_LEN,
        num_machines=device_num + 1,
        xlstm_layers=2,
        lr=3e-4,  # 标准学习率
        device=device
    )
    
    # 创建完成率专注奖励函数
    reward_function = ImprovedRewardFunction(
        max_makespan=150.0,       # 不使用，保留兼容性
        completion_weight=0.0,    # 不使用
        makespan_weight=0.0,      # 不使用
        efficiency_weight=0.0,    # 不使用
        penalty_weight=0.0,       # 不使用
        step_reward_scale=0.0     # 步骤奖励完全关闭
    )
    
    print(f"\n🎯 完成率专注奖励函数:")
    print(f"   奖励 = 完成率 × 100")
    print(f"   完美完成额外奖励: +50")
    print(f"   奖励范围: 0-150分")
    print(f"   不考虑makespan、内存、负载等因素")
    
    # 创建保存目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_dir = f"models/completion_focused_{device_num}devices_{timestamp}"
    os.makedirs(model_dir, exist_ok=True)
    
    # 训练记录
    training_log = {
        'episodes': [],
        'rewards': [],
        'completion_rates': [],
        'makespans': [],
        'perfect_completions': []  # 记录完美完成的次数
    }
    
    print(f"\n📊 开始训练...")
    
    perfect_count = 0  # 完美完成计数
    
    for episode in range(episodes):
        # 加载DAG数据
        task_features, adj_matrix, machine_resources, comm_speed, memory_status = data_loader.load_random_dag()
        num_tasks = len(task_features)
        
        # 初始化环境
        data_loader._init_step_state(task_features, adj_matrix, machine_resources, comm_speed, memory_status)
        
        # 获取初始状态
        state = data_loader.process_state_as_enhanced_sequence(
            task_features, adj_matrix, machine_resources, comm_speed, memory_status)
        
        episode_reward = 0.0
        step_count = 0
        max_steps = num_tasks * 3  # 限制最大步数
        
        while step_count < max_steps:
            try:
                # 获取ready_mask
                ready_mask = data_loader.get_ready_mask()
                if not np.any(ready_mask):
                    if detailed_debug:
                        print(f"    ⚠️  没有可执行任务，结束回合")
                    break
                
                # 选择动作
                action = agent.select_action(state, ready_mask, deterministic=False)
                
                # 执行动作
                next_state, original_reward, done, info = data_loader.step_enhanced_sequence(action)
                
                # 使用完成率专注奖励函数（步骤级奖励为0）
                step_reward, _ = reward_function.calculate_reward({})
                episode_reward += step_reward  # 应该是0
                
                # 存储经验
                next_ready_mask = data_loader.get_ready_mask() if not done else None
                agent.store_transition(state, action, step_reward, next_state, done, ready_mask, next_ready_mask)
                
                # 训练智能体
                if len(agent.replay_buffer) > 1000:  # 等待足够的经验
                    agent.train()
                
                if done:
                    # 计算最终奖励（主要奖励来源）
                    completed_tasks_data = info.get('completed_tasks', set())
                    completed_tasks = len(completed_tasks_data) if hasattr(completed_tasks_data, '__len__') else 0
                    makespan = data_loader._step_state.get('total_completion_time', 0) / 1000.0
                    
                    episode_info = {
                        'total_tasks': num_tasks,
                        'completed_tasks': completed_tasks,
                        'makespan': makespan,
                        'failed_tasks': num_tasks - completed_tasks,
                        'deadlock_occurred': info.get('deadlock_detected', False)
                    }
                    
                    final_reward, final_components = reward_function.calculate_reward({}, episode_info)
                    episode_reward += final_reward
                    
                    # 记录训练数据
                    completion_rate = completed_tasks / num_tasks
                    is_perfect = completion_rate == 1.0
                    if is_perfect:
                        perfect_count += 1
                    
                    training_log['episodes'].append(episode)
                    training_log['rewards'].append(episode_reward)
                    training_log['completion_rates'].append(completion_rate)
                    training_log['makespans'].append(makespan)
                    training_log['perfect_completions'].append(perfect_count)
                    
                    if episode % 50 == 0 or detailed_debug:
                        print(f"回合 {episode:4d}: 奖励={episode_reward:6.1f}, "
                              f"完成率={completion_rate:.1%}, Makespan={makespan:6.1f}秒, "
                              f"完美次数={perfect_count}")
                        if detailed_debug:
                            print(f"    奖励组件: {final_components}")
                    
                    break
                
                state = next_state
                step_count += 1
                
            except Exception as e:
                if detailed_debug:
                    print(f"    ❌ 执行错误: {e}")
                episode_reward += 0.0  # 不给惩罚，专注完成率
                break
        
        # 定期保存模型和训练日志
        if (episode + 1) % save_interval == 0:
            # 保存模型
            model_path = os.path.join(model_dir, f"model_episode_{episode+1}.pth")
            agent.save_model(model_path)
            
            # 保存训练日志
            log_path = os.path.join(model_dir, f"training_log_episode_{episode+1}.json")
            with open(log_path, 'w') as f:
                json.dump(training_log, f, indent=2)
            
            # 计算最近100回合的统计
            recent_episodes = min(100, len(training_log['rewards']))
            if recent_episodes > 0:
                recent_rewards = training_log['rewards'][-recent_episodes:]
                recent_completion = training_log['completion_rates'][-recent_episodes:]
                recent_makespan = training_log['makespans'][-recent_episodes:]
                recent_perfect = perfect_count - training_log['perfect_completions'][-recent_episodes]
                
                print(f"\n📈 最近{recent_episodes}回合统计:")
                print(f"   平均奖励: {np.mean(recent_rewards):.1f}")
                print(f"   平均完成率: {np.mean(recent_completion):.1%}")
                print(f"   平均Makespan: {np.mean(recent_makespan):.1f}秒")
                print(f"   完美完成次数: {recent_perfect}")
                print(f"   完美完成率: {recent_perfect/recent_episodes:.1%}")
            
            print(f"💾 已保存模型: {model_path}")
    
    # 最终保存
    final_model_path = os.path.join(model_dir, "final_model.pth")
    agent.save_model(final_model_path)
    
    final_log_path = os.path.join(model_dir, "final_training_log.json")
    with open(final_log_path, 'w') as f:
        json.dump(training_log, f, indent=2)
    
    print(f"\n✅ 完成率专注训练完成！")
    print(f"   最终模型: {final_model_path}")
    print(f"   训练日志: {final_log_path}")
    
    # 最终统计
    if len(training_log['rewards']) > 0:
        final_100_completion = np.mean(training_log['completion_rates'][-100:])
        final_100_makespan = np.mean(training_log['makespans'][-100:])
        final_perfect_rate = perfect_count / episodes
        
        print(f"\n📊 最终训练统计:")
        print(f"   总回合数: {len(training_log['rewards'])}")
        print(f"   最终100回合平均完成率: {final_100_completion:.1%}")
        print(f"   最终100回合平均Makespan: {final_100_makespan:.1f}秒")
        print(f"   总完美完成次数: {perfect_count}")
        print(f"   完美完成率: {final_perfect_rate:.1%}")
        print(f"   最高完成率: {max(training_log['completion_rates']):.1%}")
        
        # 判断是否可以进入下一阶段
        if final_100_completion >= 0.8:
            print(f"\n🎉 恭喜！完成率已达到80%以上，可以考虑进入makespan优化阶段！")
        elif final_100_completion >= 0.6:
            print(f"\n👍 完成率达到60%以上，继续训练或考虑调整策略")
        else:
            print(f"\n💪 完成率还需提升，建议继续训练或检查配置")
    
    return training_log, model_dir

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='完成率专注训练')
    parser.add_argument('--devices', type=int, default=6, help='边缘设备数量')
    parser.add_argument('--episodes', type=int, default=1500, help='训练回合数')
    parser.add_argument('--save_interval', type=int, default=300, help='保存间隔')
    parser.add_argument('--debug', action='store_true', help='详细调试模式')
    parser.add_argument('--data_path', type=str, help='数据路径')
    
    args = parser.parse_args()
    
    # 开始训练
    training_log, model_dir = train_completion_focused(
        device_num=args.devices,
        episodes=args.episodes,
        save_interval=args.save_interval,
        detailed_debug=args.debug,
        data_path=args.data_path
    )
    
    print(f"\n🎯 完成率专注训练的优势:")
    print("1. 专注于基础能力：确保任务能够完成")
    print("2. 奖励简单明确：完成率越高奖励越高")
    print("3. 避免复杂优化：不被makespan等因素干扰")
    print("4. 建立基础策略：为后续优化打下基础")
    print("5. 训练稳定：奖励范围明确(0-150)")
    
    print(f"\n📋 下一步建议:")
    final_completion = np.mean(training_log['completion_rates'][-100:]) if training_log['completion_rates'] else 0
    if final_completion >= 0.8:
        print("✅ 可以开始makespan优化训练")
        print("   使用makespan专注奖励函数进一步优化")
    else:
        print("🔄 继续完成率训练")
        print("   或者调整网络结构、学习率等超参数")
