"""
xLSTM版本的SAC智能体
使用复合动作架构（与增强版/LSTM版本保持一致）
"""
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
import os
import sys

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from xlstm_sac_networks import XLSTMActorNetwork, XLSTMCriticNetwork

class XLSTMSACAgent:
    """xLSTM版本的SAC智能体"""
    
    def __init__(self, config, device='cuda'):
        self.config = config
        self.device = device
        
        # 网络参数
        self.state_dim = config.ENHANCED_STATE_DIM
        self.action_dim = config.COMPOUND_ACTION_DIM
        self.hidden_dim = 256
        self.seq_len = config.SEQ_LEN
        self.num_machines = config.NUM_EDGE_SERVERS + 1
        self.xlstm_layers = 2
        
        # 训练参数
        self.lr = config.LEARNING_RATE
        self.gamma = 0.99
        self.tau = 0.005
        self.alpha = 0.2  # 熵权重
        self.target_entropy = -self.action_dim  # 目标熵
        
        # 创建网络
        self._create_networks()
        
        # 创建优化器
        self._create_optimizers()
        
        # 自动调节熵权重
        self.log_alpha = torch.tensor(np.log(self.alpha), requires_grad=True, device=device)
        self.alpha_optimizer = optim.Adam([self.log_alpha], lr=self.lr)
        
        print(f"[xLSTM_SAC] 初始化完成")
        print(f"  状态维度: {self.state_dim}")
        print(f"  动作维度: {self.action_dim}")
        print(f"  序列长度: {self.seq_len}")
        print(f"  xLSTM层数: {self.xlstm_layers}")
        print(f"  目标熵: {self.target_entropy}")
    
    def _create_networks(self):
        """创建网络"""
        # Actor网络
        self.actor = XLSTMActorNetwork(
            state_dim=self.state_dim,
            action_dim=self.action_dim,
            hidden_dim=self.hidden_dim,
            seq_len=self.seq_len,
            num_machines=self.num_machines,
            xlstm_layers=self.xlstm_layers
        ).to(self.device)
        
        # Critic网络
        self.critic1 = XLSTMCriticNetwork(
            state_dim=self.state_dim,
            action_dim=self.action_dim,
            hidden_dim=self.hidden_dim,
            seq_len=self.seq_len,
            xlstm_layers=self.xlstm_layers
        ).to(self.device)
        
        self.critic2 = XLSTMCriticNetwork(
            state_dim=self.state_dim,
            action_dim=self.action_dim,
            hidden_dim=self.hidden_dim,
            seq_len=self.seq_len,
            xlstm_layers=self.xlstm_layers
        ).to(self.device)
        
        # 目标网络
        self.target_critic1 = XLSTMCriticNetwork(
            state_dim=self.state_dim,
            action_dim=self.action_dim,
            hidden_dim=self.hidden_dim,
            seq_len=self.seq_len,
            xlstm_layers=self.xlstm_layers
        ).to(self.device)
        
        self.target_critic2 = XLSTMCriticNetwork(
            state_dim=self.state_dim,
            action_dim=self.action_dim,
            hidden_dim=self.hidden_dim,
            seq_len=self.seq_len,
            xlstm_layers=self.xlstm_layers
        ).to(self.device)
        
        # 初始化目标网络
        self._hard_update(self.target_critic1, self.critic1)
        self._hard_update(self.target_critic2, self.critic2)
    
    def _create_optimizers(self):
        """创建优化器"""
        self.actor_optimizer = optim.Adam(self.actor.parameters(), lr=self.lr)
        self.critic1_optimizer = optim.Adam(self.critic1.parameters(), lr=self.lr)
        self.critic2_optimizer = optim.Adam(self.critic2.parameters(), lr=self.lr)
    
    def _hard_update(self, target, source):
        """硬更新目标网络"""
        for target_param, param in zip(target.parameters(), source.parameters()):
            target_param.data.copy_(param.data)
    
    def _soft_update(self, target, source):
        """软更新目标网络"""
        for target_param, param in zip(target.parameters(), source.parameters()):
            target_param.data.copy_(target_param.data * (1.0 - self.tau) + param.data * self.tau)
    
    def select_action(self, state, ready_mask=None, deterministic=False):
        """选择动作"""
        if not isinstance(state, torch.Tensor):
            state = torch.FloatTensor(state).to(self.device)
        
        if state.dim() == 2:
            state = state.unsqueeze(0)  # 添加batch维度
        
        if ready_mask is not None and not isinstance(ready_mask, torch.Tensor):
            ready_mask = torch.FloatTensor(ready_mask).to(self.device)
            if ready_mask.dim() == 1:
                ready_mask = ready_mask.unsqueeze(0)
        
        with torch.no_grad():
            if deterministic:
                # 确定性动作
                task_mean, _, machine_mean, _ = self.actor(state, ready_mask)
                action = torch.stack([task_mean, machine_mean], dim=-1)
            else:
                # 随机动作
                action, _ = self.actor.sample(state, ready_mask)
        
        return action.cpu().numpy()[0]
    
    def update(self, replay_buffer, batch_size=256):
        """更新网络参数"""
        if len(replay_buffer) < batch_size:
            return {}
        
        # 从经验回放中采样
        batch = replay_buffer.sample(batch_size)
        state_batch = torch.FloatTensor(batch['states']).to(self.device)
        action_batch = torch.FloatTensor(batch['actions']).to(self.device)
        reward_batch = torch.FloatTensor(batch['rewards']).to(self.device)
        next_state_batch = torch.FloatTensor(batch['next_states']).to(self.device)
        done_batch = torch.FloatTensor(batch['dones']).to(self.device)
        ready_mask_batch = torch.FloatTensor(batch['ready_masks']).to(self.device)
        next_ready_mask_batch = torch.FloatTensor(batch['next_ready_masks']).to(self.device)
        
        # 更新Critic网络
        critic_loss = self._update_critics(
            state_batch, action_batch, reward_batch, 
            next_state_batch, done_batch, next_ready_mask_batch
        )
        
        # 更新Actor网络
        actor_loss = self._update_actor(state_batch, ready_mask_batch)
        
        # 更新熵权重
        alpha_loss = self._update_alpha(state_batch, ready_mask_batch)
        
        # 软更新目标网络
        self._soft_update(self.target_critic1, self.critic1)
        self._soft_update(self.target_critic2, self.critic2)
        
        return {
            'critic_loss': critic_loss,
            'actor_loss': actor_loss,
            'alpha_loss': alpha_loss,
            'alpha': self.alpha
        }
    
    def _update_critics(self, states, actions, rewards, next_states, dones, next_ready_masks):
        """更新Critic网络"""
        with torch.no_grad():
            # 计算目标Q值
            next_actions, next_log_probs = self.actor.sample(next_states, next_ready_masks)
            target_q1 = self.target_critic1(next_states, next_actions)
            target_q2 = self.target_critic2(next_states, next_actions)
            target_q = torch.min(target_q1, target_q2) - self.alpha * next_log_probs.unsqueeze(-1)
            target_q = rewards.unsqueeze(-1) + (1 - dones.unsqueeze(-1)) * self.gamma * target_q
        
        # 计算当前Q值
        current_q1 = self.critic1(states, actions)
        current_q2 = self.critic2(states, actions)
        
        # 计算损失
        critic1_loss = F.mse_loss(current_q1, target_q)
        critic2_loss = F.mse_loss(current_q2, target_q)
        
        # 更新Critic1
        self.critic1_optimizer.zero_grad()
        critic1_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.critic1.parameters(), 1.0)
        self.critic1_optimizer.step()
        
        # 更新Critic2
        self.critic2_optimizer.zero_grad()
        critic2_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.critic2.parameters(), 1.0)
        self.critic2_optimizer.step()
        
        return (critic1_loss + critic2_loss).item() / 2
    
    def _update_actor(self, states, ready_masks):
        """更新Actor网络"""
        actions, log_probs = self.actor.sample(states, ready_masks)
        q1 = self.critic1(states, actions)
        q2 = self.critic2(states, actions)
        q = torch.min(q1, q2)
        
        actor_loss = (self.alpha * log_probs.unsqueeze(-1) - q).mean()
        
        self.actor_optimizer.zero_grad()
        actor_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.actor.parameters(), 1.0)
        self.actor_optimizer.step()
        
        return actor_loss.item()
    
    def _update_alpha(self, states, ready_masks):
        """更新熵权重"""
        with torch.no_grad():
            _, log_probs = self.actor.sample(states, ready_masks)
        
        alpha_loss = -(self.log_alpha * (log_probs + self.target_entropy)).mean()
        
        self.alpha_optimizer.zero_grad()
        alpha_loss.backward()
        self.alpha_optimizer.step()
        
        self.alpha = self.log_alpha.exp().item()
        
        return alpha_loss.item()
    
    def save_models(self, filepath):
        """保存模型"""
        torch.save({
            'actor_state_dict': self.actor.state_dict(),
            'critic1_state_dict': self.critic1.state_dict(),
            'critic2_state_dict': self.critic2.state_dict(),
            'target_critic1_state_dict': self.target_critic1.state_dict(),
            'target_critic2_state_dict': self.target_critic2.state_dict(),
            'actor_optimizer_state_dict': self.actor_optimizer.state_dict(),
            'critic1_optimizer_state_dict': self.critic1_optimizer.state_dict(),
            'critic2_optimizer_state_dict': self.critic2_optimizer.state_dict(),
            'alpha_optimizer_state_dict': self.alpha_optimizer.state_dict(),
            'log_alpha': self.log_alpha,
            'config': self.config
        }, filepath)
        print(f"xLSTM SAC模型已保存到: {filepath}")
    
    def load_models(self, filepath):
        """加载模型"""
        checkpoint = torch.load(filepath, map_location=self.device)
        
        self.actor.load_state_dict(checkpoint['actor_state_dict'])
        self.critic1.load_state_dict(checkpoint['critic1_state_dict'])
        self.critic2.load_state_dict(checkpoint['critic2_state_dict'])
        self.target_critic1.load_state_dict(checkpoint['target_critic1_state_dict'])
        self.target_critic2.load_state_dict(checkpoint['target_critic2_state_dict'])
        
        self.actor_optimizer.load_state_dict(checkpoint['actor_optimizer_state_dict'])
        self.critic1_optimizer.load_state_dict(checkpoint['critic1_optimizer_state_dict'])
        self.critic2_optimizer.load_state_dict(checkpoint['critic2_optimizer_state_dict'])
        self.alpha_optimizer.load_state_dict(checkpoint['alpha_optimizer_state_dict'])
        
        self.log_alpha = checkpoint['log_alpha']
        self.alpha = self.log_alpha.exp().item()
        
        print(f"xLSTM SAC模型已从 {filepath} 加载")
