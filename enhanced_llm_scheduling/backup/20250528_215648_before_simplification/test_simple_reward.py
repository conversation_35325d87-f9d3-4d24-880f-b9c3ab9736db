#!/usr/bin/env python3
"""
测试简化的makespan专注奖励函数
"""

from improved_reward_function import ImprovedRewardFunction

def test_simple_reward():
    """测试简化的奖励函数"""
    
    print("🧪 测试简化的makespan专注奖励函数")
    print("=" * 50)
    
    # 创建简化的奖励函数
    reward_func = ImprovedRewardFunction(
        max_makespan=150.0,
        completion_weight=50.0,   # 这些权重在简化版本中不直接使用
        makespan_weight=30.0,
        efficiency_weight=0.0,
        penalty_weight=20.0,
        step_reward_scale=0.0     # 步骤奖励完全关闭
    )
    
    # 测试案例
    test_cases = [
        {
            'name': '理想情况 - 100%完成，低makespan',
            'episode_info': {
                'total_tasks': 30,
                'completed_tasks': 30,
                'makespan': 80.0,
                'failed_tasks': 0,
                'deadlock_occurred': False
            }
        },
        {
            'name': '高完成率 - 90%完成，中等makespan',
            'episode_info': {
                'total_tasks': 30,
                'completed_tasks': 27,
                'makespan': 120.0,
                'failed_tasks': 3,
                'deadlock_occurred': False
            }
        },
        {
            'name': '中等完成率 - 70%完成，高makespan',
            'episode_info': {
                'total_tasks': 30,
                'completed_tasks': 21,
                'makespan': 140.0,
                'failed_tasks': 9,
                'deadlock_occurred': False
            }
        },
        {
            'name': '当前训练水平 - 50%完成',
            'episode_info': {
                'total_tasks': 30,
                'completed_tasks': 15,
                'makespan': 150.0,
                'failed_tasks': 15,
                'deadlock_occurred': False
            }
        },
        {
            'name': '低完成率 - 30%完成',
            'episode_info': {
                'total_tasks': 30,
                'completed_tasks': 9,
                'makespan': 180.0,
                'failed_tasks': 21,
                'deadlock_occurred': False
            }
        },
        {
            'name': '死锁情况',
            'episode_info': {
                'total_tasks': 30,
                'completed_tasks': 10,
                'makespan': 200.0,
                'failed_tasks': 20,
                'deadlock_occurred': True
            }
        },
        {
            'name': 'Makespan过大',
            'episode_info': {
                'total_tasks': 30,
                'completed_tasks': 20,
                'makespan': 300.0,  # 超过max_makespan
                'failed_tasks': 10,
                'deadlock_occurred': False
            }
        }
    ]
    
    print("\n📊 测试结果:")
    print("-" * 80)
    print(f"{'案例':<25} {'完成率':<8} {'Makespan':<10} {'总奖励':<10} {'奖励组件'}")
    print("-" * 80)
    
    for test_case in test_cases:
        episode_info = test_case['episode_info']
        
        # 计算奖励（步骤信息为空，只计算回合奖励）
        reward, components = reward_func.calculate_reward({}, episode_info)
        
        completion_rate = episode_info['completed_tasks'] / episode_info['total_tasks']
        makespan = episode_info['makespan']
        
        # 格式化奖励组件
        comp_str = ", ".join([f"{k}:{v:.1f}" for k, v in components.items() if v != 0])
        
        print(f"{test_case['name']:<25} {completion_rate:<8.1%} {makespan:<10.1f} {reward:<10.1f} {comp_str}")
    
    print("-" * 80)
    
    # 分析奖励特性
    print("\n📈 奖励函数特性分析:")
    print("1. 步骤奖励已完全关闭，只有回合结束时给予奖励")
    print("2. 主要关注完成率和makespan两个指标")
    print("3. 完成率>80%时给予高奖励(50*completion_rate)")
    print("4. 完成率50-80%时给予中等奖励(30*completion_rate)")
    print("5. 完成率<50%时给予低奖励或惩罚(10*completion_rate-5)")
    print("6. 只有完成率>50%时才考虑makespan奖励")
    print("7. Makespan奖励 = 30*(1-makespan/max_makespan)*completion_rate")
    print("8. 失败任务惩罚 = -20*(failed_tasks/total_tasks)")
    print("9. 死锁惩罚 = -15")
    print("10. 完美执行奖励 = +20")
    
    # 奖励范围分析
    print("\n📏 奖励范围分析:")
    print("- 最高奖励: ~90分 (100%完成 + 低makespan + 完美奖励)")
    print("- 中等奖励: ~40分 (70%完成 + 中等makespan)")
    print("- 最低奖励: ~-40分 (低完成率 + 死锁 + 高失败率)")
    print("- 奖励变化相对平稳，避免了之前的剧烈波动")

def compare_with_original():
    """与原始复杂奖励函数对比"""
    
    print("\n🔄 与原始奖励函数对比:")
    print("=" * 50)
    
    # 创建原始复杂奖励函数（注释掉简化逻辑）
    original_func = ImprovedRewardFunction(
        max_makespan=150.0,
        completion_weight=60.0,
        makespan_weight=20.0,
        efficiency_weight=8.0,
        penalty_weight=35.0,
        step_reward_scale=0.002
    )
    
    # 创建简化奖励函数
    simple_func = ImprovedRewardFunction(
        max_makespan=150.0,
        completion_weight=50.0,
        makespan_weight=30.0,
        efficiency_weight=0.0,
        penalty_weight=20.0,
        step_reward_scale=0.0
    )
    
    # 测试案例
    test_episode = {
        'total_tasks': 30,
        'completed_tasks': 21,  # 70%完成率
        'makespan': 120.0,
        'failed_tasks': 9,
        'deadlock_occurred': False
    }
    
    # 计算两种奖励
    # 注意：原始函数现在也使用简化逻辑，所以结果会相同
    # 这里主要是展示配置差异
    simple_reward, simple_components = simple_func.calculate_reward({}, test_episode)
    
    print(f"测试案例: 70%完成率, makespan=120秒")
    print(f"简化奖励函数结果: {simple_reward:.1f}")
    print("简化奖励组件:")
    for comp, value in simple_components.items():
        if value != 0:
            print(f"  {comp}: {value:.1f}")
    
    print("\n✅ 简化优势:")
    print("1. 奖励计算逻辑清晰简单")
    print("2. 减少了奖励噪声和波动")
    print("3. 专注于核心指标：完成率和makespan")
    print("4. 更容易调试和理解")
    print("5. 训练过程更稳定")

if __name__ == "__main__":
    test_simple_reward()
    compare_with_original()
    print("\n✅ 测试完成！建议使用简化的奖励函数进行训练。")
