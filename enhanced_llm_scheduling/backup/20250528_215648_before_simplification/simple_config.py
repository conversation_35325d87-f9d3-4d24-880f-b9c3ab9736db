"""
简化的增强LLM调度系统配置
只使用明确的路径，不做复杂的兼容性处理
"""
import os
import sys

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(parent_dir)

from config import Config

class SimpleEnhancedConfig(Config):
    """简化的增强LLM调度配置类"""
    
    def __init__(self):
        super().__init__()
        
        # 增强特征维度
        self.STATIC_TASK_DIM = 3        # CPU周期, 数据传输, LLM标志
        self.DYNAMIC_FEATURES_DIM = 6   # 任务状态(4) + 预测输出tokens(1) + 预测内存(1)
        
        # 计算增强状态维度
        self.ENHANCED_STATE_DIM = (
            self.STATIC_TASK_DIM +      # 静态任务特征 (3)
            self.DYNAMIC_FEATURES_DIM + # 动态特征 (6)
            self.STATE_COMM_DIM +       # 通信特征 (1)
            self.STATE_MEM_DIM +        # 内存状态 (6)
            self.STATE_COMPUTE_DIM +    # 计算能力 (6)
            self.STATE_TTFT_DIM +       # TTFT (6)
            self.STATE_TPOT_DIM +       # TPOT (6)
            self.STATE_DAG_DIM +        # DAG结构 (6)
            1                           # 有效任务掩码 (1)
        )  # 总计: 41维
        
        # 复合动作空间
        self.TASK_CHOICE_DIM = 1      # 任务选择
        self.MACHINE_CHOICE_DIM = 1   # 机器选择
        self.COMPOUND_ACTION_DIM = 2  # 复合动作维度
        
        # LLM预测参数
        self.MAX_PREDICTED_TOKENS = 3000  # 最大预测输出token数
        self.MAX_RUNTIME_MEMORY = 64      # 最大运行时内存(GB)
        self.TOKEN_PREDICTION_FACTOR = 1.2  # 输出token预测因子
        self.BASE_OUTPUT_TOKENS = 50      # 基础输出token数
        self.BASE_LLM_MEMORY = 8.0       # LLM基础内存需求(GB)
        self.MEMORY_PER_TOKEN = 0.002    # 每个token的额外内存需求(GB)
        
        # 任务状态编码
        self.TASK_STATUS_WAITING = 0     # 未就绪/等待输入
        self.TASK_STATUS_READY = 1       # 就绪可调度
        self.TASK_STATUS_RUNNING = 2     # 运行中
        self.TASK_STATUS_COMPLETED = 3   # 已完成
        
        # 设置默认数据路径（可以被外部覆盖）
        self.TRAINING_DATA_PATH = None
        
        print(f"[SIMPLE_CONFIG] 增强状态维度: {self.ENHANCED_STATE_DIM}")
        print(f"[SIMPLE_CONFIG] 动态特征维度: {self.DYNAMIC_FEATURES_DIM}")
        print(f"[SIMPLE_CONFIG] 复合动作维度: {self.COMPOUND_ACTION_DIM}")
    
    def set_data_path(self, data_path):
        """设置数据路径"""
        self.TRAINING_DATA_PATH = data_path
        
        # 更新相关路径
        self.TASK_FEATURES_PATH = os.path.join(data_path, 'task_{}_features.xlsx')
        self.ADJACENCY_MATRIX_PATH = os.path.join(data_path, 'task_{}_adjacency_matrix.xlsx')
        self.MACHINES_RESOURCE_PATH = os.path.join(data_path, 'machines_resource1.xlsx')
        self.MACHINE_COMMU_SPEED_PATH = os.path.join(data_path, 'machine_commu_speed.xlsx')
        
        print(f"[SIMPLE_CONFIG] 数据路径设置为: {data_path}")
    
    def update_edge_servers(self, num_edge_servers, task_num=None, memory_limit=None):
        """更新边缘服务器配置"""
        super().update_edge_servers(num_edge_servers, task_num, memory_limit)
        
        # 重新计算增强状态维度
        self.ENHANCED_STATE_DIM = (
            self.STATIC_TASK_DIM +
            self.DYNAMIC_FEATURES_DIM +
            self.STATE_COMM_DIM +
            self.STATE_MEM_DIM +
            self.STATE_COMPUTE_DIM +
            self.STATE_TTFT_DIM +
            self.STATE_TPOT_DIM +
            self.STATE_DAG_DIM +
            1
        )
        
        self.TASK_FEATURE_DIM = self.ENHANCED_STATE_DIM
        
        print(f"[SIMPLE_CONFIG] 更新后增强状态维度: {self.ENHANCED_STATE_DIM}")
        
        # 如果没有设置数据路径，使用默认路径
        if self.TRAINING_DATA_PATH is None:
            default_path = f'../dataset/training/DAG_200_edges_{num_edge_servers}_mem_32GB_density_0.4_0.6'
            self.set_data_path(default_path)
    
    def predict_llm_output_tokens(self, input_tokens):
        """预测LLM输出token数量"""
        if input_tokens <= 0:
            return self.BASE_OUTPUT_TOKENS
        
        predicted = input_tokens * self.TOKEN_PREDICTION_FACTOR + self.BASE_OUTPUT_TOKENS
        return min(predicted, self.MAX_PREDICTED_TOKENS)
    
    def predict_llm_runtime_memory(self, input_tokens, output_tokens):
        """预测LLM运行时内存需求"""
        total_tokens = input_tokens + output_tokens
        memory = self.BASE_LLM_MEMORY + total_tokens * self.MEMORY_PER_TOKEN
        return min(memory, self.MAX_RUNTIME_MEMORY)
    
    def normalize_tokens(self, token_count):
        """归一化token数量"""
        return min(token_count / self.MAX_PREDICTED_TOKENS, 1.0)
    
    def normalize_memory(self, memory_gb):
        """归一化内存大小"""
        return min(memory_gb / self.MAX_RUNTIME_MEMORY, 1.0)
    
    def encode_task_status(self, status):
        """将任务状态编码为one-hot向量"""
        status_vector = [0.0, 0.0, 0.0, 0.0]
        if 0 <= status <= 3:
            status_vector[status] = 1.0
        return status_vector
