#!/usr/bin/env python3
"""
测试改进的xLSTM调度系统
验证"先选任务，再分配机器"的逻辑和makespan优化效果
"""

import os
import sys
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch

# 添加路径
sys.path.append('..')
sys.path.append('.')

from enhanced_data_loader import EnhancedDataLoader
from improved_xlstm_networks import ImprovedXLSTMSACAgent
from improved_reward_function import ImprovedRewardFunction
from enhanced_config import EnhancedLLMConfig

def test_improved_xlstm_architecture():
    """测试改进的xLSTM架构的基本功能"""
    print("🧪 测试改进的xLSTM架构...")

    # 设置参数
    device_num = 2
    enhanced_config = EnhancedLLMConfig()
    enhanced_config.NUM_EDGE_SERVERS = device_num
    enhanced_config.update_edge_servers(device_num)

    state_dim = enhanced_config.ENHANCED_STATE_DIM
    seq_len = enhanced_config.SEQ_LEN

    # 创建智能体
    agent = ImprovedXLSTMSACAgent(
        state_dim=state_dim,
        action_dim=2,
        hidden_dim=128,  # 使用较小的隐藏维度进行测试
        seq_len=seq_len,
        num_machines=device_num + 1,
        xlstm_layers=1,  # 使用较少的层数进行测试
        lr=3e-4,
        device='cpu'  # 使用CPU进行测试
    )

    # 创建模拟数据
    batch_size = 4

    # 模拟状态序列
    state_sequence = torch.randn(batch_size, seq_len, state_dim)

    # 模拟就绪掩码（前5个任务就绪）
    ready_mask = torch.zeros(batch_size, seq_len)
    ready_mask[:, :5] = 1.0

    print(f"  状态序列形状: {state_sequence.shape}")
    print(f"  就绪掩码形状: {ready_mask.shape}")
    print(f"  就绪任务数: {ready_mask.sum(dim=1)}")

    # 测试前向传播
    print("\n1. 测试Actor前向传播...")
    task_logits, global_context, encoded = agent.actor.forward(state_sequence, ready_mask)

    print(f"  任务logits形状: {task_logits.shape}")
    print(f"  全局上下文形状: {global_context.shape}")
    print(f"  编码序列形状: {encoded.shape}")
    print(f"  任务logits范围: [{task_logits.min():.3f}, {task_logits.max():.3f}]")

    # 测试采样
    print("\n2. 测试动作采样...")
    actions, log_probs, selected_indices, selected_embeddings = agent.actor.sample(state_sequence, ready_mask)

    print(f"  动作形状: {actions.shape}")
    print(f"  log概率形状: {log_probs.shape}")
    print(f"  选定任务索引: {selected_indices}")
    print(f"  选定任务嵌入形状: {selected_embeddings.shape}")
    print(f"  动作范围: [{actions.min():.3f}, {actions.max():.3f}]")

    # 验证任务选择逻辑
    print("\n3. 验证任务选择逻辑...")
    for i in range(batch_size):
        selected_idx = selected_indices[i].item()
        is_ready = ready_mask[i, selected_idx].item()
        print(f"  批次{i}: 选择任务{selected_idx}, 是否就绪: {bool(is_ready)}")

        if not is_ready:
            print(f"    ❌ 错误: 选择了非就绪任务!")
        else:
            print(f"    ✅ 正确: 选择了就绪任务")

    # 测试Critic
    print("\n4. 测试Critic网络...")
    q_values = agent.critic1(state_sequence, actions, selected_embeddings)
    print(f"  Q值形状: {q_values.shape}")
    print(f"  Q值范围: [{q_values.min():.3f}, {q_values.max():.3f}]")

    # 测试确定性动作选择
    print("\n5. 测试确定性动作选择...")
    deterministic_actions = agent.select_action(state_sequence[0], ready_mask[0], deterministic=True)
    print(f"  确定性动作: {deterministic_actions}")

    print("✅ 架构测试完成!")
    return True

def test_makespan_optimization():
    """测试makespan优化效果"""
    print("\n🎯 测试Makespan优化效果...")

    # 创建奖励函数
    reward_func = ImprovedRewardFunction(
        max_makespan=500.0,
        completion_weight=15.0,
        makespan_weight=10.0,
        efficiency_weight=3.0,
        penalty_weight=25.0
    )

    # 测试不同场景的奖励
    scenarios = [
        {
            'name': '完美执行',
            'episode_info': {
                'total_tasks': 10,
                'completed_tasks': 10,
                'makespan': 100.0,
                'failed_tasks': 0,
                'deadlock_occurred': False
            }
        },
        {
            'name': '高完成率，低makespan',
            'episode_info': {
                'total_tasks': 10,
                'completed_tasks': 9,
                'makespan': 80.0,
                'failed_tasks': 1,
                'deadlock_occurred': False
            }
        },
        {
            'name': '高完成率，高makespan',
            'episode_info': {
                'total_tasks': 10,
                'completed_tasks': 9,
                'makespan': 400.0,
                'failed_tasks': 1,
                'deadlock_occurred': False
            }
        },
        {
            'name': '低完成率',
            'episode_info': {
                'total_tasks': 10,
                'completed_tasks': 5,
                'makespan': 200.0,
                'failed_tasks': 5,
                'deadlock_occurred': False
            }
        },
        {
            'name': '死锁情况',
            'episode_info': {
                'total_tasks': 10,
                'completed_tasks': 3,
                'makespan': 300.0,
                'failed_tasks': 7,
                'deadlock_occurred': True
            }
        }
    ]

    print("奖励函数测试结果:")
    print("-" * 80)

    for scenario in scenarios:
        reward, components = reward_func.calculate_reward({}, scenario['episode_info'])

        print(f"\n📊 {scenario['name']}:")
        print(f"  总奖励: {reward:.2f}")
        print(f"  完成率: {scenario['episode_info']['completed_tasks']}/{scenario['episode_info']['total_tasks']} "
              f"({scenario['episode_info']['completed_tasks']/scenario['episode_info']['total_tasks']:.1%})")
        print(f"  Makespan: {scenario['episode_info']['makespan']:.1f}秒")
        print(f"  奖励组件:")
        for comp_name, comp_value in components.items():
            if comp_value != 0:
                print(f"    {comp_name}: {comp_value:.2f}")

    print("✅ 奖励函数测试完成!")
    return True

def test_training_compatibility():
    """测试训练兼容性"""
    print("\n🔧 测试训练兼容性...")

    device_num = 2
    enhanced_config = EnhancedLLMConfig()
    enhanced_config.NUM_EDGE_SERVERS = device_num
    enhanced_config.update_edge_servers(device_num)

    state_dim = enhanced_config.ENHANCED_STATE_DIM
    seq_len = enhanced_config.SEQ_LEN

    # 创建智能体
    agent = ImprovedXLSTMSACAgent(
        state_dim=state_dim,
        action_dim=2,
        hidden_dim=128,
        seq_len=seq_len,
        num_machines=device_num + 1,
        xlstm_layers=1,
        lr=3e-4,
        device='cpu'
    )

    # 模拟训练数据
    batch_size = 8
    state = torch.randn(batch_size, seq_len, state_dim)
    action = torch.randn(batch_size, 2)
    reward_tensor = torch.randn(batch_size, 1)
    next_state = torch.randn(batch_size, seq_len, state_dim)
    done = torch.zeros(batch_size, 1)
    ready_mask = torch.zeros(batch_size, seq_len)
    ready_mask[:, :5] = 1.0  # 前5个任务就绪

    print(f"  模拟批次大小: {batch_size}")
    print(f"  状态形状: {state.shape}")
    print(f"  动作形状: {action.shape}")

    # 测试前向传播
    print("\n1. 测试批次前向传播...")
    actions, log_probs, selected_indices, selected_embeddings = agent.actor.sample(state, ready_mask)
    q1 = agent.critic1(state, actions, selected_embeddings)
    q2 = agent.critic2(state, actions, selected_embeddings)

    print(f"  采样动作形状: {actions.shape}")
    print(f"  Q1值形状: {q1.shape}")
    print(f"  Q2值形状: {q2.shape}")
    print(f"  无NaN值: {not torch.isnan(actions).any() and not torch.isnan(q1).any() and not torch.isnan(q2).any()}")

    # 测试梯度计算
    print("\n2. 测试梯度计算...")
    loss = q1.mean() + q2.mean() + log_probs.mean()
    loss.backward()

    # 检查梯度
    has_grad = False
    for name, param in agent.actor.named_parameters():
        if param.grad is not None:
            has_grad = True
            break

    print(f"  梯度计算成功: {has_grad}")
    print(f"  损失值: {loss.item():.4f}")

    # 测试模型保存和加载
    print("\n3. 测试模型保存和加载...")
    test_model_path = "test_improved_xlstm_model.pth"

    try:
        agent.save_models(test_model_path)
        print(f"  模型保存成功")

        # 创建新智能体并加载模型
        new_agent = ImprovedXLSTMSACAgent(
            state_dim=state_dim,
            action_dim=2,
            hidden_dim=128,
            seq_len=seq_len,
            num_machines=device_num + 1,
            xlstm_layers=1,
            lr=3e-4,
            device='cpu'
        )

        new_agent.load_models(test_model_path)
        print(f"  模型加载成功")

        # 清理测试文件
        if os.path.exists(test_model_path):
            os.remove(test_model_path)

    except Exception as e:
        print(f"  模型保存/加载失败: {e}")
        return False

    print("✅ 训练兼容性测试完成!")
    return True

def main():
    """主测试函数"""
    print("🧪 改进的xLSTM调度系统测试")
    print("=" * 80)

    test_results = []

    # 运行所有测试
    tests = [
        ("架构功能测试", test_improved_xlstm_architecture),
        ("Makespan优化测试", test_makespan_optimization),
        ("训练兼容性测试", test_training_compatibility)
    ]

    for test_name, test_func in tests:
        print(f"\n🔍 运行 {test_name}...")
        try:
            result = test_func()
            test_results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            test_results.append((test_name, False))

    # 总结测试结果
    print(f"\n📊 测试总结:")
    print("=" * 80)

    passed_tests = sum(1 for _, result in test_results if result)
    total_tests = len(test_results)

    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")

    print(f"\n🎯 总体结果: {passed_tests}/{total_tests} 测试通过")

    if passed_tests == total_tests:
        print("🎉 所有测试通过！改进的xLSTM架构准备就绪。")
        return True
    else:
        print("⚠️  部分测试失败，请检查问题。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
