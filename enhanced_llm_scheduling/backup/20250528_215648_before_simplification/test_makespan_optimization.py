#!/usr/bin/env python3
"""
测试Makespan优化效果
专门测试改进后的奖励函数是否能学习到更好的调度策略
"""

import torch
import numpy as np
import argparse
import time
import matplotlib.pyplot as plt
from enhanced_data_loader import <PERSON>hancedDataLoader
from enhanced_config import EnhancedLLMConfig
from enhanced_sac_agent import EnhancedSACAgent

def test_makespan_optimization():
    """测试makespan优化学习效果"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--device_num", default=5, type=int, help="边缘设备数量")
    parser.add_argument("--data_path", default="../dataset/training/DAG_30_edges_5_mem_57-64GB_density_0.2_0.6", 
                       type=str, help="数据路径")
    parser.add_argument("--episodes", default=200, type=int, help="训练回合数")
    parser.add_argument("--test_dag", default=0, type=int, help="测试用的DAG索引")
    args = parser.parse_args()

    print("🎯 测试Makespan优化学习效果")
    print(f"参数: {args}")

    # 创建配置
    config = EnhancedLLMConfig()
    config.update_edge_servers(args.device_num)
    
    if args.data_path != config.TRAINING_DATA_PATH:
        config.TRAINING_DATA_PATH = args.data_path
        print(f"[CONFIG] 已设置自定义数据路径: {args.data_path}")

    # 创建数据加载器
    data_loader = EnhancedDataLoader(config)
    data_loader.set_debug(False)  # 关闭详细调试，专注性能

    # 创建智能体
    agent = EnhancedSACAgent(config=config, device="cuda")

    print(f"📊 配置信息:")
    print(f"  边缘设备数: {args.device_num}")
    print(f"  测试DAG: {args.test_dag}")
    print(f"  训练回合数: {args.episodes}")

    # 记录训练过程
    episode_makespans = []
    episode_rewards = []
    machine_usage_stats = []  # 记录机器使用统计
    
    print(f"\n🎯 开始训练...")
    start_time = time.time()

    for episode in range(args.episodes):
        # 使用固定的DAG进行测试，观察学习效果
        dag_idx = args.test_dag
        
        # 加载DAG数据
        task_features = data_loader.load_task_features(dag_idx)
        adj_matrix = data_loader.load_adjacency_matrix(dag_idx)
        machine_resources = data_loader.load_machines_resource()
        comm_speed = data_loader.load_machine_commu_speed()
        memory_status = data_loader.load_memory_status()
        
        num_tasks = task_features.shape[1]
        
        # 重置环境
        data_loader._step_state = None
        
        # 获取初始状态
        state = data_loader.process_state_as_enhanced_sequence(
            task_features, adj_matrix, machine_resources, comm_speed, memory_status
        )
        
        episode_reward = 0
        step_count = 0
        done = False
        machine_assignments = []  # 记录机器分配
        
        # 逐步调度
        while not done and step_count < num_tasks * 2:
            # 选择动作 - 在前50个episode增加探索
            deterministic = episode > 50
            compound_action = agent.select_action(state, deterministic=deterministic)
            
            # 执行动作
            next_state, reward, done, info = data_loader.step_enhanced_task(
                compound_action=compound_action,
                adj_matrix=adj_matrix,
                task_features=task_features,
                num_tasks=num_tasks,
                machine_resources=machine_resources,
                comm_speed=comm_speed,
                memory_status=memory_status,
                debug=False
            )
            
            # 记录机器分配
            if 'last_assigned_machine' in info:
                machine_assignments.append(info['last_assigned_machine'])
            
            # 存储经验
            agent.store_transition(state, compound_action, reward, next_state, done)
            
            # 更新状态和奖励
            state = next_state
            episode_reward += reward
            step_count += 1
            
            # 训练智能体
            if len(agent.replay_buffer) > 64:
                agent.update()
        
        # 记录统计信息
        completed_tasks = info.get("completed_tasks", set())
        total_time = info.get("total_time", 1000000) / 1000.0  # 转换为秒
        completion_rate = len(completed_tasks) / num_tasks
        
        # 只记录成功完成的episode的makespan
        if completion_rate == 1.0:
            episode_makespans.append(total_time)
        else:
            episode_makespans.append(1000.0)  # 失败的episode用1000秒表示
            
        episode_rewards.append(episode_reward)
        
        # 统计机器使用情况
        machine_usage = [0] * (args.device_num + 1)
        for machine_idx in machine_assignments:
            if 0 <= machine_idx < len(machine_usage):
                machine_usage[machine_idx] += 1
        machine_usage_stats.append(machine_usage)
        
        # 打印进度
        if (episode + 1) % 20 == 0 or episode < 10:
            recent_makespans = [m for m in episode_makespans[-20:] if m < 1000]
            avg_makespan = np.mean(recent_makespans) if recent_makespans else 1000.0
            avg_reward = np.mean(episode_rewards[-20:])
            
            # 最近20个episode的机器使用统计
            recent_usage = np.mean(machine_usage_stats[-20:], axis=0)
            usage_str = ", ".join([f"M{i}:{usage:.1f}" for i, usage in enumerate(recent_usage)])
            
            elapsed_time = time.time() - start_time
            print(f"Episode {episode+1:3d} | "
                  f"Makespan: {total_time:6.1f}s (平均: {avg_makespan:6.1f}s) | "
                  f"奖励: {episode_reward:6.1f} (平均: {avg_reward:6.1f}) | "
                  f"完成率: {completion_rate:.1%} | "
                  f"机器使用: [{usage_str}] | "
                  f"用时: {elapsed_time:.1f}s")
    
    # 训练完成分析
    print(f"\n📈 训练完成分析:")
    
    # 成功完成的episode统计
    successful_episodes = [m for m in episode_makespans if m < 1000]
    if successful_episodes:
        print(f"  成功完成回合: {len(successful_episodes)}/{args.episodes}")
        print(f"  最佳Makespan: {min(successful_episodes):.2f}秒")
        print(f"  平均Makespan: {np.mean(successful_episodes):.2f}秒")
        print(f"  Makespan标准差: {np.std(successful_episodes):.2f}秒")
        
        # 分析学习趋势
        first_half = successful_episodes[:len(successful_episodes)//2] if len(successful_episodes) > 10 else successful_episodes[:5]
        second_half = successful_episodes[len(successful_episodes)//2:] if len(successful_episodes) > 10 else successful_episodes[5:]
        
        if first_half and second_half:
            improvement = np.mean(first_half) - np.mean(second_half)
            print(f"  学习改进: {improvement:.2f}秒 ({'改进' if improvement > 0 else '退化'})")
    else:
        print(f"  ⚠️ 没有成功完成的回合!")
    
    # 机器使用分析
    print(f"\n🖥️ 机器使用分析:")
    avg_machine_usage = np.mean(machine_usage_stats, axis=0)
    for i, usage in enumerate(avg_machine_usage):
        machine_name = "用户设备" if i == 0 else f"边缘设备{i}"
        print(f"  {machine_name}: 平均使用 {usage:.1f} 次/episode")
    
    # 绘制学习曲线
    if len(successful_episodes) > 10:
        plt.figure(figsize=(12, 8))
        
        # Makespan学习曲线
        plt.subplot(2, 2, 1)
        plt.plot(episode_makespans)
        plt.title('Makespan Learning Curve')
        plt.xlabel('Episode')
        plt.ylabel('Makespan (seconds)')
        plt.ylim(0, min(1000, max(episode_makespans) * 1.1))
        
        # 奖励学习曲线
        plt.subplot(2, 2, 2)
        plt.plot(episode_rewards)
        plt.title('Reward Learning Curve')
        plt.xlabel('Episode')
        plt.ylabel('Episode Reward')
        
        # 机器使用分布
        plt.subplot(2, 2, 3)
        machine_usage_array = np.array(machine_usage_stats)
        for i in range(machine_usage_array.shape[1]):
            machine_name = "用户设备" if i == 0 else f"边缘设备{i}"
            plt.plot(machine_usage_array[:, i], label=machine_name)
        plt.title('Machine Usage Over Time')
        plt.xlabel('Episode')
        plt.ylabel('Tasks Assigned')
        plt.legend()
        
        # Makespan分布直方图
        plt.subplot(2, 2, 4)
        plt.hist(successful_episodes, bins=20, alpha=0.7)
        plt.title('Makespan Distribution')
        plt.xlabel('Makespan (seconds)')
        plt.ylabel('Frequency')
        
        plt.tight_layout()
        plt.savefig('makespan_optimization_results.png', dpi=300, bbox_inches='tight')
        print(f"\n📊 学习曲线已保存到: makespan_optimization_results.png")
    
    return {
        'makespans': episode_makespans,
        'rewards': episode_rewards,
        'machine_usage': machine_usage_stats,
        'best_makespan': min(successful_episodes) if successful_episodes else None,
        'avg_makespan': np.mean(successful_episodes) if successful_episodes else None
    }

if __name__ == "__main__":
    results = test_makespan_optimization()
