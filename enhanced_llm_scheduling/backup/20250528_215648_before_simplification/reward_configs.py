#!/usr/bin/env python3
"""
奖励函数配置文件
包含多种经过分析和优化的奖励函数配置
"""

from improved_reward_function import ImprovedRewardFunction

# 奖励函数配置字典
REWARD_CONFIGS = {
    'conservative': {
        'description': '保守配置 - 适合初期训练，奖励变化平稳',
        'params': {
            'max_makespan': 250.0,
            'completion_weight': 40.0,
            'makespan_weight': 15.0,
            'efficiency_weight': 5.0,
            'penalty_weight': 25.0,
            'step_reward_scale': 0.001
        }
    },
    
    'balanced': {
        'description': '平衡配置 - 推荐用于大多数训练场景',
        'params': {
            'max_makespan': 200.0,
            'completion_weight': 60.0,
            'makespan_weight': 20.0,
            'efficiency_weight': 8.0,
            'penalty_weight': 35.0,
            'step_reward_scale': 0.002
        }
    },
    
    'aggressive': {
        'description': '激进配置 - 适合后期优化，强调高性能',
        'params': {
            'max_makespan': 150.0,
            'completion_weight': 80.0,
            'makespan_weight': 25.0,
            'efficiency_weight': 10.0,
            'penalty_weight': 45.0,
            'step_reward_scale': 0.001
        }
    },
    
    'completion_focused': {
        'description': '完成率专注 - 主要优化任务完成率',
        'params': {
            'max_makespan': 300.0,
            'completion_weight': 100.0,
            'makespan_weight': 10.0,
            'efficiency_weight': 5.0,
            'penalty_weight': 60.0,
            'step_reward_scale': 0.0005
        }
    },
    
    'makespan_focused': {
        'description': 'Makespan专注 - 在保证完成率基础上优化makespan',
        'params': {
            'max_makespan': 120.0,
            'completion_weight': 50.0,
            'makespan_weight': 40.0,
            'efficiency_weight': 15.0,
            'penalty_weight': 30.0,
            'step_reward_scale': 0.001
        }
    },
    
    'stable_training': {
        'description': '稳定训练 - 最小化奖励方差，适合长期训练',
        'params': {
            'max_makespan': 180.0,
            'completion_weight': 45.0,
            'makespan_weight': 18.0,
            'efficiency_weight': 6.0,
            'penalty_weight': 28.0,
            'step_reward_scale': 0.0008
        }
    }
}

def create_reward_function(config_name='balanced'):
    """
    根据配置名称创建奖励函数
    
    Args:
        config_name: 配置名称，可选值见REWARD_CONFIGS
    
    Returns:
        ImprovedRewardFunction实例
    """
    if config_name not in REWARD_CONFIGS:
        available_configs = list(REWARD_CONFIGS.keys())
        raise ValueError(f"未知配置: {config_name}. 可用配置: {available_configs}")
    
    config = REWARD_CONFIGS[config_name]
    print(f"🎯 使用奖励配置: {config_name}")
    print(f"   描述: {config['description']}")
    
    return ImprovedRewardFunction(**config['params'])

def get_config_info(config_name=None):
    """
    获取配置信息
    
    Args:
        config_name: 配置名称，如果为None则返回所有配置信息
    
    Returns:
        配置信息字典或所有配置信息
    """
    if config_name is None:
        return REWARD_CONFIGS
    
    if config_name not in REWARD_CONFIGS:
        available_configs = list(REWARD_CONFIGS.keys())
        raise ValueError(f"未知配置: {config_name}. 可用配置: {available_configs}")
    
    return REWARD_CONFIGS[config_name]

def print_all_configs():
    """打印所有可用的奖励配置"""
    print("🎯 可用的奖励函数配置:")
    print("=" * 60)
    
    for config_name, config in REWARD_CONFIGS.items():
        print(f"\n📋 {config_name}:")
        print(f"   描述: {config['description']}")
        print("   参数:")
        for param_name, param_value in config['params'].items():
            print(f"     {param_name}: {param_value}")

def recommend_config(current_performance):
    """
    根据当前训练表现推荐奖励配置
    
    Args:
        current_performance: 当前性能字典，包含completion_rate, makespan, stability等
    
    Returns:
        推荐的配置名称和原因
    """
    completion_rate = current_performance.get('completion_rate', 0.0)
    makespan = current_performance.get('makespan', float('inf'))
    stability = current_performance.get('stability', 'unknown')  # 'stable', 'unstable', 'unknown'
    
    recommendations = []
    
    # 基于完成率推荐
    if completion_rate < 0.3:
        recommendations.append(('conservative', '完成率较低，建议使用保守配置'))
    elif completion_rate < 0.6:
        recommendations.append(('balanced', '完成率中等，建议使用平衡配置'))
    elif completion_rate < 0.8:
        recommendations.append(('aggressive', '完成率较高，可以使用激进配置'))
    else:
        recommendations.append(('makespan_focused', '完成率很高，可以专注优化makespan'))
    
    # 基于稳定性推荐
    if stability == 'unstable':
        recommendations.append(('stable_training', '训练不稳定，建议使用稳定训练配置'))
    
    # 基于makespan推荐
    if makespan > 200:
        recommendations.append(('completion_focused', 'Makespan较高，建议先专注完成率'))
    
    # 返回第一个推荐
    if recommendations:
        return recommendations[0]
    else:
        return ('balanced', '默认推荐平衡配置')

# 预定义的训练阶段配置
TRAINING_STAGES = {
    'early': {
        'description': '训练初期 (0-500回合)',
        'config': 'conservative',
        'focus': '建立基础策略，确保训练稳定性'
    },
    'middle': {
        'description': '训练中期 (500-1500回合)',
        'config': 'balanced',
        'focus': '平衡各项指标，提升整体性能'
    },
    'late': {
        'description': '训练后期 (1500+回合)',
        'config': 'aggressive',
        'focus': '精细优化，追求最佳性能'
    }
}

def get_stage_config(episode):
    """
    根据训练回合数获取推荐的阶段配置
    
    Args:
        episode: 当前训练回合数
    
    Returns:
        推荐的配置名称和阶段信息
    """
    if episode < 500:
        stage = 'early'
    elif episode < 1500:
        stage = 'middle'
    else:
        stage = 'late'
    
    stage_info = TRAINING_STAGES[stage]
    return stage_info['config'], stage_info

def create_adaptive_reward_function(episode, performance_history=None):
    """
    创建自适应奖励函数
    
    Args:
        episode: 当前训练回合数
        performance_history: 性能历史记录（可选）
    
    Returns:
        ImprovedRewardFunction实例和配置信息
    """
    # 基于训练阶段的基础推荐
    base_config, stage_info = get_stage_config(episode)
    
    # 如果有性能历史，进行进一步调整
    if performance_history:
        # 计算最近100回合的平均性能
        recent_episodes = min(100, len(performance_history))
        if recent_episodes > 0:
            recent_performance = {
                'completion_rate': sum(h.get('completion_rate', 0) for h in performance_history[-recent_episodes:]) / recent_episodes,
                'makespan': sum(h.get('makespan', 200) for h in performance_history[-recent_episodes:]) / recent_episodes,
                'stability': 'stable' if len(set(h.get('completion_rate', 0) > 0.5 for h in performance_history[-recent_episodes:])) == 1 else 'unstable'
            }
            
            # 获取基于性能的推荐
            perf_config, reason = recommend_config(recent_performance)
            
            print(f"📊 性能分析:")
            print(f"   最近{recent_episodes}回合平均完成率: {recent_performance['completion_rate']:.1%}")
            print(f"   最近{recent_episodes}回合平均Makespan: {recent_performance['makespan']:.1f}秒")
            print(f"   训练稳定性: {recent_performance['stability']}")
            print(f"   性能推荐: {perf_config} ({reason})")
            
            # 如果性能推荐与阶段推荐不同，使用性能推荐
            if perf_config != base_config:
                print(f"   采用性能推荐配置: {perf_config}")
                final_config = perf_config
            else:
                final_config = base_config
        else:
            final_config = base_config
    else:
        final_config = base_config
    
    print(f"🎯 第{episode}回合使用配置: {final_config}")
    print(f"   训练阶段: {stage_info['description']}")
    print(f"   关注重点: {stage_info['focus']}")
    
    return create_reward_function(final_config), final_config

if __name__ == "__main__":
    # 打印所有配置
    print_all_configs()
    
    # 测试推荐功能
    print("\n" + "="*60)
    print("🧪 推荐测试:")
    
    test_cases = [
        {'completion_rate': 0.2, 'makespan': 300, 'stability': 'unstable'},
        {'completion_rate': 0.5, 'makespan': 200, 'stability': 'stable'},
        {'completion_rate': 0.8, 'makespan': 150, 'stability': 'stable'},
        {'completion_rate': 0.95, 'makespan': 120, 'stability': 'stable'}
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        config, reason = recommend_config(test_case)
        print(f"\n测试案例{i}: {test_case}")
        print(f"推荐配置: {config} ({reason})")
    
    # 测试自适应功能
    print("\n" + "="*60)
    print("🔄 自适应测试:")
    
    for episode in [100, 800, 2000]:
        reward_func, config = create_adaptive_reward_function(episode)
        print(f"回合{episode}: 使用配置 {config}")
        print()
