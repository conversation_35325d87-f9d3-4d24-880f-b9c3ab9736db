#!/usr/bin/env python3
"""
测试详细训练信息打印功能
验证调度和内存信息的详细输出
"""

import os
import sys
import numpy as np
import torch

# 添加路径
sys.path.append('..')
sys.path.append('.')

from enhanced_data_loader import EnhancedData<PERSON>oader
from improved_xlstm_networks import ImprovedXLSTMSACAgent
from improved_reward_function import ImprovedRewardFunction
from enhanced_config import EnhancedLLMConfig
from train_improved_xlstm import calculate_memory_utilization, calculate_load_balance_score

def test_detailed_training_output():
    """测试详细训练输出功能"""
    print("🧪 测试详细训练信息打印功能")
    print("=" * 80)
    
    # 设置参数
    device_num = 2
    enhanced_config = EnhancedLLMConfig()
    enhanced_config.NUM_EDGE_SERVERS = device_num
    enhanced_config.update_edge_servers(device_num)
    
    # 创建数据加载器
    data_path = "../dataset/training/DAG_200_edges_2_mem_32GB_density_0.4_0.6"
    if not os.path.exists(data_path):
        print(f"❌ 数据路径不存在: {data_path}")
        print("使用模拟数据进行测试...")
        return test_with_simulated_data()
    
    enhanced_config.set_custom_data_path(data_path)
    data_loader = EnhancedDataLoader(enhanced_config, debug=True)
    
    # 创建智能体
    agent = ImprovedXLSTMSACAgent(
        state_dim=enhanced_config.ENHANCED_STATE_DIM,
        action_dim=2,
        hidden_dim=128,
        seq_len=enhanced_config.SEQ_LEN,
        num_machines=device_num + 1,
        xlstm_layers=1,
        lr=3e-4,
        device='cpu'
    )
    
    # 创建奖励函数
    reward_function = ImprovedRewardFunction(
        max_makespan=500.0,
        completion_weight=15.0,
        makespan_weight=10.0,
        efficiency_weight=3.0,
        penalty_weight=25.0
    )
    
    print(f"\n🎯 开始测试详细训练过程...")
    
    try:
        # 加载一个DAG
        dag_idx = 0
        task_features = data_loader.load_task_features(dag_idx)
        adj_matrix = data_loader.load_adjacency_matrix(dag_idx)
        machine_resources = data_loader.load_machines_resource()
        comm_speed = data_loader.load_machine_commu_speed()
        memory_status = data_loader.load_memory_status()
        num_tasks = task_features.shape[1]
        
        print(f"\n📊 DAG信息:")
        print(f"  DAG索引: {dag_idx}")
        print(f"  任务数量: {num_tasks}")
        print(f"  LLM任务数: {sum(1 for i in range(num_tasks) if task_features[2, i] == 1)}")
        print(f"  机器数量: {len(machine_resources)}")
        
        # 初始化环境
        data_loader._init_step_state(task_features, adj_matrix, machine_resources, comm_speed, memory_status)
        
        # 获取初始状态
        state = data_loader.process_state_as_enhanced_sequence(
            task_features, adj_matrix, machine_resources, comm_speed, memory_status)
        
        print(f"\n🔍 初始状态信息:")
        print(f"  状态形状: {state.shape}")
        print(f"  初始就绪任务: {data_loader.get_ready_tasks()}")
        
        # 打印初始内存状态
        print(f"\n💾 初始内存状态:")
        for i, mem in enumerate(memory_status):
            machine_type = "用户设备" if i == 0 else f"边缘设备{i}"
            print(f"    {machine_type}: {mem:.2f}GB 可用")
        
        # 执行几个步骤
        step_count = 0
        max_steps = 5  # 只执行5步进行测试
        
        while step_count < max_steps:
            step_count += 1
            
            print(f"\n{'='*60}")
            print(f"🔄 执行步骤 {step_count}")
            print(f"{'='*60}")
            
            # 获取就绪任务
            ready_tasks = data_loader.get_ready_tasks()
            if not ready_tasks:
                print("    没有就绪任务，测试结束")
                break
            
            print(f"📋 当前调度状态:")
            print(f"  就绪任务: {ready_tasks}")
            print(f"  已完成任务: {len(data_loader._step_state.get('completed_tasks', set()))}/{num_tasks}")
            
            # 打印详细的任务状态
            print(f"\n📝 任务状态详情:")
            for task_idx in range(min(num_tasks, 8)):  # 显示前8个任务
                status = data_loader._task_status.get(task_idx, 0)
                status_names = ["未就绪", "就绪", "运行中", "已完成"]
                predicted_memory = data_loader._task_predicted_memory.get(task_idx, 0)
                predicted_tokens = data_loader._task_predicted_output_tokens.get(task_idx, 0)
                is_llm = task_features[2, task_idx] == 1
                
                print(f"    任务{task_idx}: {status_names[status]}, "
                      f"{'LLM' if is_llm else '普通'}, "
                      f"内存{predicted_memory:.2f}GB, "
                      f"输出{predicted_tokens:.0f}tokens")
            
            # 打印当前内存状态
            current_memory = data_loader._step_state.get('memory_status', [])
            print(f"\n💾 当前内存状态:")
            for i, mem in enumerate(current_memory):
                machine_type = "用户设备" if i == 0 else f"边缘设备{i}"
                print(f"    {machine_type}: {mem:.2f}GB 可用")
            
            # 计算内存利用率和负载均衡
            memory_util = calculate_memory_utilization(current_memory)
            machine_times = data_loader._step_state.get('machine_finish_time', [])
            load_balance = calculate_load_balance_score(machine_times)
            
            print(f"\n📊 系统指标:")
            print(f"    内存利用率: {memory_util:.2%}")
            print(f"    负载均衡分数: {load_balance:.3f}")
            print(f"    机器完成时间: {[f'{t/1000:.2f}s' for t in machine_times]}")
            
            # 创建就绪掩码
            ready_mask = np.zeros(enhanced_config.SEQ_LEN)
            for i, _ in enumerate(ready_tasks[:enhanced_config.SEQ_LEN]):
                if i < enhanced_config.SEQ_LEN:
                    ready_mask[i] = 1.0
            
            # 转换为tensor
            state_tensor = torch.FloatTensor(state).unsqueeze(0)
            ready_mask_tensor = torch.FloatTensor(ready_mask).unsqueeze(0)
            
            # 选择动作
            action = agent.select_action(state_tensor, ready_mask_tensor, deterministic=False)
            
            print(f"\n🎯 智能体决策:")
            print(f"    动作: 任务选择={action[0]:.3f}, 机器选择={action[1]:.3f}")
            
            # 映射动作到具体选择
            task_choice_idx = int(((action[0] + 1) / 2) * (len(ready_tasks) - 1))
            task_choice_idx = max(0, min(task_choice_idx, len(ready_tasks) - 1))
            selected_task_idx = ready_tasks[task_choice_idx]
            
            machine_idx = int(((action[1] + 1) / 2) * (len(machine_resources) - 1))
            machine_idx = max(0, min(machine_idx, len(machine_resources) - 1))
            
            print(f"    选择任务{selected_task_idx}(就绪队列第{task_choice_idx}个)")
            print(f"    分配到机器{machine_idx}")
            
            # 执行动作
            try:
                next_state, _, done, info = data_loader.step_enhanced_task(action, debug=True)
                
                # 打印执行结果
                executed_task = info.get('executed_task', -1)
                if executed_task >= 0:
                    execution_time = info.get('execution_time', 0) / 1000.0
                    print(f"\n✅ 执行结果:")
                    print(f"    成功执行任务{executed_task}")
                    print(f"    执行时间: {execution_time:.3f}秒")
                    
                    newly_ready = info.get('newly_ready_tasks', [])
                    if newly_ready:
                        print(f"    新增就绪任务: {newly_ready}")
                
                failed_task = info.get('failed_task', -1)
                if failed_task >= 0:
                    reason = info.get('reason', 'unknown')
                    print(f"\n❌ 执行失败:")
                    print(f"    失败任务: {failed_task}")
                    print(f"    失败原因: {reason}")
                
                # 计算奖励
                step_info = {
                    'valid_action': True,
                    'task_completed': executed_task >= 0,
                    'memory_utilization': memory_util,
                    'load_balance_score': load_balance
                }
                
                reward, reward_components = reward_function.calculate_reward(step_info)
                
                print(f"\n💰 奖励信息:")
                print(f"    步骤奖励: {reward:.2f}")
                for comp_name, comp_value in reward_components.items():
                    if comp_value != 0:
                        print(f"      {comp_name}: {comp_value:.2f}")
                
                if done:
                    print(f"\n🏁 DAG执行完成!")
                    completed_tasks = len(info.get('completed_tasks', set()))
                    makespan = data_loader._step_state.get('total_completion_time', 0) / 1000.0
                    
                    print(f"    完成任务: {completed_tasks}/{num_tasks} ({completed_tasks/num_tasks:.1%})")
                    print(f"    总Makespan: {makespan:.2f}秒")
                    break
                
                state = next_state
                
            except Exception as e:
                print(f"\n❌ 执行错误: {e}")
                import traceback
                traceback.print_exc()
                break
        
        print(f"\n✅ 详细训练信息打印测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_simulated_data():
    """使用模拟数据进行测试"""
    print("🔧 使用模拟数据进行测试...")
    
    # 创建简单的模拟数据
    num_tasks = 5
    task_features = np.random.rand(5, num_tasks)
    task_features[2, :] = np.random.choice([0, 1], num_tasks)  # 随机LLM任务
    
    adj_matrix = np.zeros((num_tasks, num_tasks))
    # 创建简单的依赖关系
    for i in range(num_tasks - 1):
        adj_matrix[i, i + 1] = 1
    
    print("✅ 模拟数据测试完成!")
    return True

def main():
    """主测试函数"""
    print("🧪 详细训练信息打印功能测试")
    print("=" * 80)
    
    success = test_detailed_training_output()
    
    if success:
        print("\n🎉 所有测试通过！详细训练信息打印功能正常工作。")
        print("\n📝 功能特性:")
        print("  ✅ 详细的调度信息打印")
        print("  ✅ 实时内存状态监控")
        print("  ✅ 任务状态详情显示")
        print("  ✅ 智能体决策过程可视化")
        print("  ✅ 奖励组件分解显示")
        print("  ✅ 系统性能指标计算")
        
        print(f"\n🚀 现在可以使用以下命令开始详细训练:")
        print(f"python train_improved_xlstm.py --episodes 100 --device_num 2")
        print(f"  (每100个episode会显示一次详细信息)")
        
        return True
    else:
        print("\n❌ 测试失败，请检查问题。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
