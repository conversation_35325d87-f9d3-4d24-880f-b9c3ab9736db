#!/usr/bin/env python3
"""
测试TTFT和TPOT值是否正确从machines_resource1.xlsx读取并使用
"""

import numpy as np
import pandas as pd
import sys
import os

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from enhanced_data_loader import EnhancedDataLoader
from enhanced_config import EnhancedLLMConfig

def create_test_machine_resource_file():
    """创建测试用的机器资源文件"""
    # 创建测试数据
    machine_data = {
        'CPU_frequence': [2.5, 3.0, 3.5],  # GHz
        'Memory_Available': [16, 32, 32],   # GB
        'token_per_second': [0.010, 0.015, 0.020],  # TPOT值
        'base_execution_time': [0.20, 0.25, 0.30]   # TTFT值
    }
    
    df = pd.DataFrame(machine_data)
    
    # 确保目录存在
    os.makedirs('test_data', exist_ok=True)
    
    # 保存文件
    file_path = 'test_data/machines_resource1.xlsx'
    df.to_excel(file_path, index=False)
    
    print(f"创建测试机器资源文件: {file_path}")
    print("机器资源数据:")
    print(df)
    
    return file_path, machine_data

def test_ttft_tpot_loading():
    """测试TTFT和TPOT值的加载和使用"""
    print("=" * 60)
    print("测试TTFT和TPOT值的加载和使用")
    print("=" * 60)
    
    # 创建测试机器资源文件
    file_path, expected_data = create_test_machine_resource_file()
    
    # 创建配置
    config = EnhancedLLMConfig()
    config.NUM_EDGE_SERVERS = 2
    config.update_edge_servers(2)
    
    # 设置机器资源文件路径
    config.MACHINES_RESOURCE_PATH = file_path
    
    # 创建数据加载器
    loader = EnhancedDataLoader(config, debug=True)
    
    # 加载机器资源
    machine_resources = loader.load_machines_resource()
    print(f"\n加载的机器资源 (CPU频率): {machine_resources}")
    
    # 检查是否正确加载了token_per_second和base_execution_time
    print(f"\n检查加载的TTFT和TPOT值:")
    
    if hasattr(loader, 'token_per_second'):
        print(f"token_per_second (TPOT): {loader.token_per_second}")
        print(f"期望值: {expected_data['token_per_second']}")
        
        if np.allclose(loader.token_per_second, expected_data['token_per_second']):
            print("✅ TPOT值加载正确")
        else:
            print("❌ TPOT值加载不正确")
    else:
        print("❌ 未找到token_per_second属性")
    
    if hasattr(loader, 'machine_base_execution_time'):
        print(f"base_execution_time (TTFT): {loader.machine_base_execution_time}")
        print(f"期望值: {expected_data['base_execution_time']}")
        
        if np.allclose(loader.machine_base_execution_time, expected_data['base_execution_time']):
            print("✅ TTFT值加载正确")
        else:
            print("❌ TTFT值加载不正确")
    else:
        print("❌ 未找到machine_base_execution_time属性")
    
    # 测试状态生成中的TTFT和TPOT使用
    print(f"\n测试状态生成中的TTFT和TPOT使用:")
    
    # 创建简单的测试数据
    task_features = np.array([
        [1e9, 2e9],           # CPU周期
        [100, 200],           # 数据传输
        [0, 1],               # 是否LLM
        [0, 1000],            # token数
        [2.0, 8.0]            # 内存需求
    ])
    
    adj_matrix = np.array([
        [0, 1],  # 任务0 -> 任务1
        [0, 0]   # 任务1无后继
    ])
    
    comm_speed = np.array([
        [0, 100, 120],
        [100, 0, 80],
        [120, 80, 0]
    ])
    
    # 初始化状态
    loader._init_step_state(task_features, adj_matrix, machine_resources, comm_speed, None)
    
    # 测试TTFT和TPOT获取方法
    ttft_values = loader._get_machine_ttft_values()
    tpot_values = loader._get_machine_tpot_values()
    
    print(f"获取的TTFT值: {ttft_values}")
    print(f"获取的TPOT值: {tpot_values}")
    
    # 生成状态并检查TTFT/TPOT部分
    enhanced_state = loader.process_state_as_enhanced_sequence(
        task_features, adj_matrix, machine_resources, comm_speed
    )
    
    print(f"\n生成的增强状态形状: {enhanced_state.shape}")
    
    # 分析状态中的TTFT和TPOT部分
    # 根据配置，TTFT和TPOT应该在特定位置
    feature_dim = config.ENHANCED_STATE_DIM
    num_machines = len(machine_resources)
    
    # 计算TTFT和TPOT在状态中的位置
    idx = 3 + 6 + 1 + num_machines + num_machines  # 静态特征 + 动态特征 + 通信 + 内存 + 计算
    ttft_start = idx
    ttft_end = idx + num_machines
    tpot_start = ttft_end
    tpot_end = tpot_start + num_machines
    
    print(f"\n状态中TTFT位置: [{ttft_start}:{ttft_end}]")
    print(f"状态中TPOT位置: [{tpot_start}:{tpot_end}]")
    
    # 检查第一个任务的TTFT和TPOT值
    task_0_ttft = enhanced_state[0, ttft_start:ttft_end]
    task_0_tpot = enhanced_state[0, tpot_start:tpot_end]
    
    print(f"任务0的TTFT值 (归一化): {task_0_ttft}")
    print(f"任务0的TPOT值 (归一化): {task_0_tpot}")
    
    # 验证归一化是否正确
    if ttft_values is not None:
        max_ttft = np.max(ttft_values)
        expected_ttft_normalized = ttft_values / max_ttft
        print(f"期望的TTFT归一化值: {expected_ttft_normalized}")
        
        if np.allclose(task_0_ttft, expected_ttft_normalized, atol=1e-6):
            print("✅ TTFT归一化正确")
        else:
            print("❌ TTFT归一化不正确")
    
    if tpot_values is not None:
        # TPOT使用倒数归一化
        tpot_inv = 1.0 / (tpot_values + 1e-8)
        max_tpot_inv = np.max(tpot_inv)
        expected_tpot_normalized = tpot_inv / max_tpot_inv
        print(f"期望的TPOT归一化值: {expected_tpot_normalized}")
        
        if np.allclose(task_0_tpot, expected_tpot_normalized, atol=1e-6):
            print("✅ TPOT归一化正确")
        else:
            print("❌ TPOT归一化不正确")
    
    # 清理测试文件
    if os.path.exists(file_path):
        os.remove(file_path)
        print(f"\n清理测试文件: {file_path}")
    
    print(f"\n{'='*60}")
    print("测试完成")

if __name__ == "__main__":
    test_ttft_tpot_loading()
