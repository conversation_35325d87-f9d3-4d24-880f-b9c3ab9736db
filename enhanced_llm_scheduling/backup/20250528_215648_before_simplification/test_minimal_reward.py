#!/usr/bin/env python3
"""
测试极简的makespan奖励函数
"""

from improved_reward_function import ImprovedRewardFunction

def test_minimal_reward():
    """测试极简的makespan奖励函数"""
    
    print("🎯 测试极简makespan奖励函数")
    print("=" * 50)
    
    # 创建极简奖励函数（参数不重要，因为只用makespan）
    reward_func = ImprovedRewardFunction(
        max_makespan=150.0,  # 不使用
        completion_weight=50.0,  # 不使用
        makespan_weight=30.0,  # 不使用
        efficiency_weight=0.0,  # 不使用
        penalty_weight=20.0,  # 不使用
        step_reward_scale=0.0  # 步骤奖励关闭
    )
    
    # 测试案例
    test_cases = [
        {
            'name': '理想情况 - 低makespan',
            'episode_info': {
                'total_tasks': 30,
                'completed_tasks': 30,
                'makespan': 80.0,
                'failed_tasks': 0,
                'deadlock_occurred': False
            }
        },
        {
            'name': '中等makespan',
            'episode_info': {
                'total_tasks': 30,
                'completed_tasks': 25,
                'makespan': 120.0,
                'failed_tasks': 5,
                'deadlock_occurred': False
            }
        },
        {
            'name': '高makespan',
            'episode_info': {
                'total_tasks': 30,
                'completed_tasks': 20,
                'makespan': 200.0,
                'failed_tasks': 10,
                'deadlock_occurred': False
            }
        },
        {
            'name': '完全失败 - 0完成',
            'episode_info': {
                'total_tasks': 30,
                'completed_tasks': 0,
                'makespan': 300.0,
                'failed_tasks': 30,
                'deadlock_occurred': True
            }
        },
        {
            'name': '部分完成 - 低makespan',
            'episode_info': {
                'total_tasks': 30,
                'completed_tasks': 15,
                'makespan': 90.0,
                'failed_tasks': 15,
                'deadlock_occurred': False
            }
        }
    ]
    
    print("\n📊 测试结果:")
    print("-" * 70)
    print(f"{'案例':<25} {'完成率':<8} {'Makespan':<10} {'奖励':<12} {'说明'}")
    print("-" * 70)
    
    for test_case in test_cases:
        episode_info = test_case['episode_info']
        
        # 计算奖励（步骤信息为空，只计算回合奖励）
        reward, components = reward_func.calculate_reward({}, episode_info)
        
        completion_rate = episode_info['completed_tasks'] / episode_info['total_tasks']
        makespan = episode_info['makespan']
        
        # 说明
        if episode_info['completed_tasks'] == 0:
            explanation = f"=-{makespan}-1000(无完成惩罚)"
        else:
            explanation = f"=-{makespan}"
        
        print(f"{test_case['name']:<25} {completion_rate:<8.1%} {makespan:<10.1f} {reward:<12.1f} {explanation}")
    
    print("-" * 70)
    
    # 分析奖励特性
    print("\n📈 极简奖励函数特性:")
    print("1. 奖励 = -makespan")
    print("2. 如果完成任务数=0，额外惩罚-1000")
    print("3. 步骤奖励完全关闭")
    print("4. 不考虑完成率、内存、负载等其他因素")
    print("5. 直接优化makespan，逻辑最简单")
    
    print("\n🎯 奖励范围:")
    print("- 最好情况: -80 (makespan=80秒)")
    print("- 一般情况: -150 (makespan=150秒)")
    print("- 较差情况: -300 (makespan=300秒)")
    print("- 最差情况: -1300 (makespan=300秒 + 无完成惩罚)")
    
    print("\n💡 优势:")
    print("1. 逻辑极其简单，容易理解")
    print("2. 直接优化目标指标makespan")
    print("3. 没有复杂的权重调节")
    print("4. 奖励变化平稳，易于训练")
    print("5. 避免了多目标优化的复杂性")
    
    print("\n⚠️  注意事项:")
    print("1. 需要确保任务能够完成，否则makespan无意义")
    print("2. 可能需要在训练初期给予一些完成率引导")
    print("3. 如果经常出现0完成率，可能需要调整惩罚值")

def compare_makespan_rewards():
    """比较不同makespan值的奖励"""
    
    print("\n🔄 Makespan奖励对比:")
    print("=" * 40)
    
    reward_func = ImprovedRewardFunction()
    
    # 不同makespan值
    makespan_values = [50, 80, 100, 120, 150, 200, 300, 500]
    
    print(f"{'Makespan(秒)':<12} {'奖励':<10} {'相对差异'}")
    print("-" * 35)
    
    base_reward = None
    for makespan in makespan_values:
        episode_info = {
            'total_tasks': 30,
            'completed_tasks': 30,  # 全部完成
            'makespan': makespan,
            'failed_tasks': 0,
            'deadlock_occurred': False
        }
        
        reward, _ = reward_func.calculate_reward({}, episode_info)
        
        if base_reward is None:
            base_reward = reward
            relative_diff = "基准"
        else:
            relative_diff = f"{reward - base_reward:+.0f}"
        
        print(f"{makespan:<12.0f} {reward:<10.0f} {relative_diff}")
    
    print("\n📊 分析:")
    print("- makespan每增加1秒，奖励减少1分")
    print("- 奖励变化线性，易于优化")
    print("- 50秒vs500秒的奖励差异为450分")

if __name__ == "__main__":
    test_minimal_reward()
    compare_makespan_rewards()
    print("\n✅ 极简奖励函数测试完成！")
    print("💡 建议：如果训练初期完成率太低，可以考虑加入少量完成率引导。")
