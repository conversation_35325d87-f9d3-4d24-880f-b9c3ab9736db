"""
测试任务选择修复效果
验证任务选择器是否不再总是选择第一个任务
"""
import torch
import numpy as np
import sys
import os

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from improved_xlstm_networks import ImprovedXLSTMActorNetwork
import config

def test_task_selection_diversity():
    """测试任务选择的多样性"""
    print("🔍 测试任务选择修复效果...")

    # 创建网络
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    state_dim = 41  # 增强状态维度
    action_dim = 2
    hidden_dim = 256
    seq_len = config.SEQ_LEN
    num_machines = 6

    actor = ImprovedXLSTMActorNetwork(
        state_dim=state_dim,
        action_dim=action_dim,
        hidden_dim=hidden_dim,
        seq_len=seq_len,
        num_machines=num_machines,
        xlstm_layers=2
    ).to(device)

    # 创建测试数据
    batch_size = 1
    state = torch.randn(batch_size, seq_len, state_dim).to(device)

    # 测试不同的就绪任务配置
    test_cases = [
        # 案例1：前5个任务就绪
        torch.tensor([[1, 1, 1, 1, 1] + [0] * (seq_len - 5)], dtype=torch.float32).to(device),
        # 案例2：中间5个任务就绪
        torch.tensor([[0] * 10 + [1, 1, 1, 1, 1] + [0] * (seq_len - 15)], dtype=torch.float32).to(device),
        # 案例3：后5个任务就绪
        torch.tensor([[0] * (seq_len - 5) + [1, 1, 1, 1, 1]], dtype=torch.float32).to(device),
        # 案例4：分散的任务就绪
        torch.tensor([[1, 0, 1, 0, 1, 0, 1, 0] + [0] * (seq_len - 8)], dtype=torch.float32).to(device),
    ]

    case_names = ["前5个任务", "中间5个任务", "后5个任务", "分散任务"]

    for case_idx, (ready_mask, case_name) in enumerate(zip(test_cases, case_names)):
        print(f"\n📋 测试案例 {case_idx + 1}: {case_name}")

        # 获取就绪任务索引
        ready_indices = torch.nonzero(ready_mask[0]).squeeze(-1).tolist()
        print(f"就绪任务索引: {ready_indices}")

        # 多次采样，统计选择分布
        selection_counts = {}
        num_samples = 100

        actor.eval()
        with torch.no_grad():
            for _ in range(num_samples):
                # 采样动作
                actions, log_probs, selected_indices, _ = actor.sample(state, ready_mask)
                selected_idx = selected_indices[0].item()

                if selected_idx in selection_counts:
                    selection_counts[selected_idx] += 1
                else:
                    selection_counts[selected_idx] = 1

        # 分析选择分布
        print(f"选择统计 (共{num_samples}次采样):")
        total_selections = sum(selection_counts.values())

        for task_idx in ready_indices:
            count = selection_counts.get(task_idx, 0)
            percentage = (count / total_selections) * 100 if total_selections > 0 else 0
            print(f"  任务{task_idx}: {count}次 ({percentage:.1f}%)")

        # 计算选择多样性指标
        if len(ready_indices) > 1:
            # 计算熵（多样性指标）
            probs = []
            for task_idx in ready_indices:
                count = selection_counts.get(task_idx, 0)
                prob = count / total_selections if total_selections > 0 else 0
                probs.append(prob)

            # 计算熵
            entropy = -sum(p * np.log(p + 1e-10) for p in probs if p > 0)
            max_entropy = np.log(len(ready_indices))  # 均匀分布的最大熵
            normalized_entropy = entropy / max_entropy if max_entropy > 0 else 0

            print(f"选择多样性: {normalized_entropy:.3f} (1.0为完全均匀)")

            # 检查是否存在强烈偏好
            max_prob = max(probs) if probs else 0
            if max_prob > 0.8:
                print(f"⚠️ 检测到强烈偏好: 最高概率 = {max_prob:.3f}")
            elif normalized_entropy > 0.8:
                print(f"✅ 选择分布良好: 多样性 = {normalized_entropy:.3f}")
            else:
                print(f"⚠️ 选择分布不够均匀: 多样性 = {normalized_entropy:.3f}")

        # 检查是否总是选择第一个就绪任务
        first_ready_idx = ready_indices[0]
        first_task_selections = selection_counts.get(first_ready_idx, 0)
        first_task_ratio = first_task_selections / total_selections if total_selections > 0 else 0

        if first_task_ratio > 0.9:
            print(f"❌ 仍然偏向第一个任务: {first_task_ratio:.1%}")
        elif first_task_ratio < 0.6:
            print(f"✅ 第一个任务偏好已修复: {first_task_ratio:.1%}")
        else:
            print(f"⚠️ 第一个任务偏好部分修复: {first_task_ratio:.1%}")

def test_logits_stability():
    """测试logits数值稳定性"""
    print("\n🔍 测试logits数值稳定性...")

    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    state_dim = 41  # 增强状态维度
    action_dim = 2
    hidden_dim = 256
    seq_len = config.SEQ_LEN
    num_machines = 6

    actor = ImprovedXLSTMActorNetwork(
        state_dim=state_dim,
        action_dim=action_dim,
        hidden_dim=hidden_dim,
        seq_len=seq_len,
        num_machines=num_machines,
        xlstm_layers=2
    ).to(device)

    # 创建测试数据
    batch_size = 1
    state = torch.randn(batch_size, seq_len, state_dim).to(device)
    ready_mask = torch.tensor([[1, 1, 1, 0, 0] + [0] * (seq_len - 5)], dtype=torch.float32).to(device)

    actor.eval()
    with torch.no_grad():
        # 前向传播
        task_logits, global_context, encoded = actor.forward(state, ready_mask)

        print(f"Logits统计:")
        print(f"  最小值: {task_logits.min().item():.3f}")
        print(f"  最大值: {task_logits.max().item():.3f}")
        print(f"  标准差: {task_logits.std().item():.3f}")
        print(f"  均值: {task_logits.mean().item():.3f}")

        # 检查是否有极端值
        if task_logits.min().item() < -1000:
            print("❌ 检测到极端负值，可能存在数值不稳定")
        elif task_logits.max().item() - task_logits.min().item() > 1000:
            print("❌ 检测到极大的数值范围，可能存在数值不稳定")
        else:
            print("✅ Logits数值范围正常")

        # 检查就绪任务的logits
        ready_indices = torch.nonzero(ready_mask[0]).squeeze(-1).tolist()
        ready_logits = task_logits[0][ready_indices]
        print(f"就绪任务logits: {ready_logits.tolist()}")

        # 计算softmax概率
        ready_probs = torch.softmax(ready_logits, dim=0)
        print(f"就绪任务概率: {ready_probs.tolist()}")

def main():
    """主函数"""
    print("🚀 开始测试任务选择修复效果")

    try:
        test_logits_stability()
        test_task_selection_diversity()
        print("\n🎉 测试完成!")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
