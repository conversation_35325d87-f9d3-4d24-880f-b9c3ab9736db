#!/usr/bin/env python3
"""
测试修改后的训练脚本，验证图像和数据保存功能
"""

import os
import sys
import subprocess
import time

def test_training_with_plots():
    """测试训练脚本的图像和数据保存功能"""
    print("=" * 80)
    print("测试增强训练脚本的图像和数据保存功能")
    print("=" * 80)
    
    # 检查数据路径
    data_path = "../dataset/training/DAG_30_edges_5_mem_25-38GB_density_0.4_0.6"
    if not os.path.exists(data_path):
        print(f"❌ 数据路径不存在: {data_path}")
        return False
    
    print(f"✅ 数据路径存在: {data_path}")
    
    # 运行短期训练测试
    print(f"\n🚀 开始短期训练测试 (10个episodes)...")
    
    cmd = [
        "python", "train_stable.py",
        "--edge_num", "5",
        "--task_num", "30", 
        "--episodes", "10",  # 只训练10个episodes用于测试
        "--save_interval", "5",  # 每5个episodes保存一次
        "--data_path", data_path
    ]
    
    try:
        # 运行训练命令
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)  # 5分钟超时
        end_time = time.time()
        
        print(f"训练耗时: {end_time - start_time:.1f}秒")
        
        if result.returncode == 0:
            print("✅ 训练成功完成")
            print("\n📋 训练输出:")
            print(result.stdout[-1000:])  # 显示最后1000个字符
        else:
            print("❌ 训练失败")
            print("\n📋 错误输出:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 训练超时")
        return False
    except Exception as e:
        print(f"❌ 运行训练时出错: {e}")
        return False
    
    # 检查生成的文件
    print(f"\n🔍 检查生成的文件...")
    
    models_dir = "models"
    if not os.path.exists(models_dir):
        print(f"❌ models目录不存在")
        return False
    
    # 查找生成的模型目录
    model_dirs = [d for d in os.listdir(models_dir) if d.startswith("stable_sac_")]
    if not model_dirs:
        print(f"❌ 没有找到生成的模型目录")
        return False
    
    print(f"✅ 找到 {len(model_dirs)} 个模型目录")
    
    # 检查最新的模型目录
    latest_dir = os.path.join(models_dir, sorted(model_dirs)[-1])
    print(f"📁 检查最新目录: {latest_dir}")
    
    expected_files = [
        "stable_sac_model.pth",
        "final_training_history.xlsx", 
        "training_plots_data.xlsx",
        "training_summary.xlsx",
        "training_history.png",
        "training_history.pdf"
    ]
    
    found_files = []
    missing_files = []
    
    for file_name in expected_files:
        file_path = os.path.join(latest_dir, file_name)
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            found_files.append(f"{file_name} ({file_size} bytes)")
        else:
            missing_files.append(file_name)
    
    print(f"\n📊 文件检查结果:")
    print(f"✅ 找到的文件 ({len(found_files)}):")
    for file_info in found_files:
        print(f"  - {file_info}")
    
    if missing_files:
        print(f"❌ 缺失的文件 ({len(missing_files)}):")
        for file_name in missing_files:
            print(f"  - {file_name}")
    
    # 验证Excel文件内容
    try:
        import pandas as pd
        
        history_file = os.path.join(latest_dir, "final_training_history.xlsx")
        if os.path.exists(history_file):
            df = pd.read_excel(history_file)
            print(f"\n📈 训练历史数据:")
            print(f"  Episodes: {len(df)}")
            print(f"  平均奖励: {df['Reward'].mean():.2f}")
            print(f"  平均完成率: {df['Completion_Ratio'].mean():.2%}")
            print(f"  数据列: {list(df.columns)}")
        
        summary_file = os.path.join(latest_dir, "training_summary.xlsx")
        if os.path.exists(summary_file):
            summary_df = pd.read_excel(summary_file)
            print(f"\n📋 训练摘要:")
            for col in summary_df.columns:
                value = summary_df[col].iloc[0]
                print(f"  {col}: {value}")
                
    except ImportError:
        print("⚠️ pandas未安装，跳过Excel文件验证")
    except Exception as e:
        print(f"⚠️ 验证Excel文件时出错: {e}")
    
    # 总结
    success_rate = len(found_files) / len(expected_files)
    print(f"\n🎯 测试结果:")
    print(f"  文件生成成功率: {success_rate:.1%} ({len(found_files)}/{len(expected_files)})")
    
    if success_rate >= 0.8:  # 80%以上的文件生成成功
        print("✅ 测试通过！训练脚本能够正确生成图像和数据文件")
        return True
    else:
        print("❌ 测试失败！部分文件未能正确生成")
        return False

if __name__ == "__main__":
    success = test_training_with_plots()
    if success:
        print("\n🎉 所有测试通过！")
        sys.exit(0)
    else:
        print("\n💥 测试失败！")
        sys.exit(1)
