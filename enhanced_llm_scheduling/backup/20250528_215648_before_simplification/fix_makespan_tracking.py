#!/usr/bin/env python3
"""
快速修复训练脚本以添加Makespan追踪
"""

import re

def fix_xlstm_script():
    """修复xLSTM训练脚本"""
    
    # 读取文件
    with open('train_xlstm.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 1. 添加makespan追踪变量
    content = content.replace(
        '    episode_rewards = []\n    episode_completion_rates = []',
        '    episode_rewards = []\n    episode_completion_rates = []\n    episode_makespans = []  # 添加makespan追踪'
    )
    
    # 2. 在记录episode结果时添加makespan
    old_pattern = r'            # 记录episode结果\n            completed_tasks = len\(info\.get\(\'completed_tasks\', set\(\)\)\)\n            completion_rate = completed_tasks / num_tasks\n\n            episode_rewards\.append\(episode_reward\)\n            episode_completion_rates\.append\(completion_rate\)'
    
    new_pattern = '''            # 记录episode结果
            completed_tasks = len(info.get('completed_tasks', set()))
            completion_rate = completed_tasks / num_tasks
            
            # 获取makespan（DAG完成时间）
            if data_loader._step_state is not None and 'total_completion_time' in data_loader._step_state:
                makespan = data_loader._step_state['total_completion_time'] / 1000.0  # 转换为秒
            else:
                makespan = info.get('total_time', 0) / 1000.0  # 备用方案

            episode_rewards.append(episode_reward)
            episode_completion_rates.append(completion_rate)
            episode_makespans.append(makespan)'''
    
    content = re.sub(old_pattern, new_pattern, content)
    
    # 3. 更新打印进度
    content = content.replace(
        '            avg_completion = np.mean(episode_completion_rates[-window_size:])',
        '            avg_completion = np.mean(episode_completion_rates[-window_size:])\n            avg_makespan = np.mean(episode_makespans[-window_size:])'
    )
    
    content = content.replace(
        '                print(f"Episode {episode:4d}: DAG {dag_idx}, Reward={episode_reward:8.2f}, "\n                      f"Completion={completion_rate:.2f}, Avg20_Reward={avg_reward:8.2f}, "\n                      f"Avg20_Completion={avg_completion:.2f}, NaN_Count={episode_nan_count}, "\n                      f"Time={time.time()-start_time:.2f}s")',
        '                print(f"Episode {episode:4d}: DAG {dag_idx}, Reward={episode_reward:8.2f}, "\n                      f"Completion={completion_rate:.2f}, Makespan={makespan:.2f}s, "\n                      f"Avg20_Reward={avg_reward:8.2f}, Avg20_Completion={avg_completion:.2f}, "\n                      f"Avg20_Makespan={avg_makespan:.2f}s, NaN_Count={episode_nan_count}, "\n                      f"Time={time.time()-start_time:.2f}s")'
    )
    
    # 4. 更新保存训练历史数据
    content = content.replace(
        "                history_df = pd.DataFrame({\n                    'Episode': range(1, len(episode_rewards) + 1),\n                    'Reward': episode_rewards,\n                    'Completion_Ratio': episode_completion_rates\n                })",
        "                history_df = pd.DataFrame({\n                    'Episode': range(1, len(episode_rewards) + 1),\n                    'Reward': episode_rewards,\n                    'Completion_Ratio': episode_completion_rates,\n                    'Makespan': episode_makespans\n                })"
    )
    
    # 5. 更新绘制训练曲线调用
    content = content.replace(
        'plot_training_history(episode_rewards, episode_completion_rates, model_dir)',
        'plot_training_history(episode_rewards, episode_completion_rates, episode_makespans, model_dir)'
    )
    
    # 6. 更新最终保存数据
    content = content.replace(
        "        final_history_df = pd.DataFrame({\n            'Episode': range(1, len(episode_rewards) + 1),\n            'Reward': episode_rewards,\n            'Completion_Ratio': episode_completion_rates\n        })",
        "        final_history_df = pd.DataFrame({\n            'Episode': range(1, len(episode_rewards) + 1),\n            'Reward': episode_rewards,\n            'Completion_Ratio': episode_completion_rates,\n            'Makespan': episode_makespans\n        })"
    )
    
    content = content.replace(
        'plot_training_history(episode_rewards, episode_completion_rates, final_model_dir)',
        'plot_training_history(episode_rewards, episode_completion_rates, episode_makespans, final_model_dir)'
    )
    
    # 7. 更新统计摘要
    content = content.replace(
        "            'Average_Completion_Rate': np.mean(episode_completion_rates),",
        "            'Average_Completion_Rate': np.mean(episode_completion_rates),\n            'Average_Makespan': np.mean(episode_makespans),"
    )
    
    content = content.replace(
        "            'Final_Completion_Rate': episode_completion_rates[-1] if episode_completion_rates else 0",
        "            'Final_Completion_Rate': episode_completion_rates[-1] if episode_completion_rates else 0,\n            'Final_Makespan': episode_makespans[-1] if episode_makespans else 0"
    )
    
    # 8. 更新返回值
    content = content.replace(
        'return agent, episode_rewards, episode_completion_rates',
        'return agent, episode_rewards, episode_completion_rates, episode_makespans'
    )
    
    # 9. 更新绘图函数
    content = content.replace(
        'def plot_training_history(rewards, completion_ratios, save_dir):',
        'def plot_training_history(rewards, completion_ratios, makespans, save_dir):'
    )
    
    content = content.replace(
        '    """绘制训练历史曲线 - 按照原版代码风格"""',
        '    """绘制训练历史曲线 - 按照原版代码风格，包含makespan"""'
    )
    
    # 10. 更新main函数
    content = content.replace(
        'agent, rewards, completion_rates = result',
        'agent, rewards, completion_rates, makespans = result'
    )
    
    content = content.replace(
        '        print(f"  最高完成率: {np.max(completion_rates):.2%}")',
        '        print(f"  最高完成率: {np.max(completion_rates):.2%}")\n        print(f"  平均Makespan: {np.mean(makespans):.2f}秒")\n        print(f"  最终Makespan: {makespans[-1]:.2f}秒")\n        print(f"  最佳Makespan: {np.min(makespans):.2f}秒")'
    )
    
    # 写回文件
    with open('train_xlstm.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ xLSTM训练脚本已修复，添加了Makespan追踪")

def fix_xlstm_plot_function():
    """修复xLSTM绘图函数"""
    
    with open('train_xlstm.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加makespan平滑计算
    content = content.replace(
        '    rewards_smooth = moving_average(rewards, window_size)\n    completion_smooth = moving_average(completion_ratios, window_size)',
        '    rewards_smooth = moving_average(rewards, window_size)\n    completion_smooth = moving_average(completion_ratios, window_size)\n    makespans_smooth = moving_average(makespans, window_size)'
    )
    
    # 添加makespan单独图表
    makespan_plot = '''
    # 3. 绘制Makespan曲线
    plt.figure(figsize=(10, 6), dpi=100)
    plt.plot(makespans, alpha=0.3, color='red', label='Raw Makespan')
    plt.plot(range(window_size-1, len(makespans)), makespans_smooth, color='red', linewidth=2, label=f'Smoothed Makespan (window={window_size})')
    plt.xlabel('Episode')
    plt.ylabel('Makespan (seconds)')
    plt.title('xLSTM SAC Makespan (DAG Completion Time)')
    plt.legend()
    plt.grid(True)
    plt.savefig(f"{save_dir}/xlstm_training_makespan.png", dpi=300, bbox_inches='tight')
    plt.close()'''
    
    content = content.replace(
        '    plt.close()\n\n    # 3. 绘制组合图表',
        f'    plt.close(){makespan_plot}\n\n    # 4. 绘制组合图表'
    )
    
    # 更新组合图表为3个子图
    content = content.replace(
        '    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))',
        '    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 15))'
    )
    
    # 添加makespan子图
    makespan_subplot = '''
    # Makespan曲线
    ax3.plot(makespans, alpha=0.3, color='red', label='Raw Makespan')
    ax3.plot(range(window_size-1, len(makespans)), makespans_smooth, color='red', linewidth=2, label=f'Smoothed Makespan (window={window_size})')
    ax3.set_xlabel('Episode')
    ax3.set_ylabel('Makespan (seconds)')
    ax3.set_title('xLSTM SAC Makespan (DAG Completion Time)')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
'''
    
    content = content.replace(
        '    ax2.set_yticklabels([f\'{int(x*100)}%\' for x in np.arange(0, 1.1, 0.1)])\n\n    # 调整布局并保存',
        f'    ax2.set_yticklabels([f\'{{int(x*100)}}%\' for x in np.arange(0, 1.1, 0.1)])\n{makespan_subplot}\n    # 调整布局并保存'
    )
    
    # 更新Excel数据保存
    content = content.replace(
        "        'Completion_Ratio_Smoothed': np.append(np.full(window_size-1, np.nan), completion_smooth)",
        "        'Completion_Ratio_Smoothed': np.append(np.full(window_size-1, np.nan), completion_smooth),\n        'Makespan': makespans,\n        'Makespan_Smoothed': np.append(np.full(window_size-1, np.nan), makespans_smooth)"
    )
    
    with open('train_xlstm.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ xLSTM绘图函数已修复，添加了Makespan图表")

if __name__ == "__main__":
    fix_xlstm_script()
    fix_xlstm_plot_function()
    print("🎉 所有修复完成！")
