"""
增强LLM调度系统的测试脚本
"""
import torch
import numpy as np
import argparse
import os
import sys
import time
from datetime import datetime

# 添加路径
sys.path.append('..')
sys.path.append('.')

from enhanced_config import EnhancedLLMConfig
from enhanced_data_loader import <PERSON>hancedData<PERSON>oa<PERSON>
from enhanced_sac_agent import <PERSON>hancedSACAgent

def create_ready_mask(data_loader, seq_len):
    """创建就绪任务掩码"""
    ready_tasks = data_loader.get_ready_tasks()
    ready_mask = np.zeros(seq_len)
    
    for task_idx in ready_tasks:
        if task_idx < seq_len:
            ready_mask[task_idx] = 1.0
    
    return ready_mask

def test_single_dag(agent, data_loader, dag_idx, config, deterministic=True, debug=False):
    """测试单个DAG的调度性能"""
    
    try:
        # 加载DAG数据
        task_features = data_loader.load_task_features(dag_idx)
        adj_matrix = data_loader.load_adjacency_matrix(dag_idx)
        machine_resources = data_loader.load_machines_resource()
        comm_speed = data_loader.load_machine_commu_speed()
        memory_status = data_loader.load_memory_status()
        
        num_tasks = task_features.shape[1]
        
        # 重置环境
        data_loader._step_state = None
        
        # 获取初始状态
        state = data_loader.process_state_as_enhanced_sequence(
            task_features, adj_matrix, machine_resources, comm_speed, memory_status
        )
        
        # 初始化环境状态
        data_loader._init_step_state(task_features, adj_matrix, machine_resources, 
                                   comm_speed, memory_status)
        
        ready_mask = create_ready_mask(data_loader, config.SEQ_LEN)
        
        total_reward = 0
        step_count = 0
        max_steps = num_tasks * 3  # 防止无限循环
        
        execution_log = []
        
        if debug:
            print(f"\n=== 测试DAG {dag_idx} (任务数: {num_tasks}) ===")
        
        while step_count < max_steps:
            # 选择动作
            action = agent.select_action(state, ready_mask, deterministic=deterministic)
            
            if debug:
                ready_tasks = data_loader.get_ready_tasks()
                print(f"Step {step_count}: 就绪任务={ready_tasks}, 动作={action}")
            
            # 执行动作
            next_state, reward, done, info = data_loader.step_enhanced_task(
                action, adj_matrix, task_features, num_tasks,
                machine_resources, comm_speed, memory_status, debug=debug
            )
            
            # 记录执行信息
            if 'executed_task' in info:
                execution_log.append({
                    'step': step_count,
                    'task': info['executed_task'],
                    'execution_time': info.get('execution_time', 0),
                    'reward': reward,
                    'newly_ready': info.get('newly_ready_tasks', [])
                })
            
            # 更新状态
            state = next_state
            ready_mask = create_ready_mask(data_loader, config.SEQ_LEN)
            total_reward += reward
            step_count += 1
            
            if done:
                break
        
        # 计算结果
        completed_tasks = len(info.get('completed_tasks', set()))
        completion_rate = completed_tasks / num_tasks
        total_time = info.get('total_time', 0) / 1000.0  # 转换为秒
        dag_failed = info.get('dag_failed', False)
        
        result = {
            'dag_idx': dag_idx,
            'num_tasks': num_tasks,
            'completed_tasks': completed_tasks,
            'completion_rate': completion_rate,
            'total_time': total_time,
            'total_reward': total_reward,
            'steps': step_count,
            'dag_failed': dag_failed,
            'execution_log': execution_log
        }
        
        if debug:
            print(f"结果: 完成率={completion_rate:.2f}, 总时间={total_time:.2f}s, "
                  f"总奖励={total_reward:.2f}, 步数={step_count}")
        
        return result
        
    except Exception as e:
        print(f"测试DAG {dag_idx} 时出错: {e}")
        return {
            'dag_idx': dag_idx,
            'num_tasks': 0,
            'completed_tasks': 0,
            'completion_rate': 0.0,
            'total_time': float('inf'),
            'total_reward': -1000.0,
            'steps': 0,
            'dag_failed': True,
            'error': str(e)
        }

def test_enhanced_sac(model_path, config, test_dags=None, deterministic=True, debug=False):
    """测试增强的SAC智能体"""
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() and config.CUDA else 'cpu')
    print(f"使用设备: {device}")
    
    # 初始化数据加载器和智能体
    data_loader = EnhancedDataLoader(config, debug=False)
    agent = EnhancedSACAgent(config, device)
    
    # 加载模型
    if os.path.exists(model_path):
        agent.load_models(model_path)
        print(f"已加载模型: {model_path}")
    else:
        print(f"模型文件不存在: {model_path}")
        return None
    
    # 确定测试的DAG列表
    if test_dags is None:
        test_dags = list(range(min(50, config.NUM_STEPS)))  # 默认测试前50个DAG
    
    print(f"开始测试 {len(test_dags)} 个DAG...")
    
    results = []
    start_time = time.time()
    
    for i, dag_idx in enumerate(test_dags):
        if i % 10 == 0:
            print(f"测试进度: {i}/{len(test_dags)}")
        
        result = test_single_dag(agent, data_loader, dag_idx, config, 
                               deterministic=deterministic, debug=debug)
        results.append(result)
    
    test_time = time.time() - start_time
    
    # 计算统计信息
    successful_results = [r for r in results if not r.get('dag_failed', True)]
    
    if successful_results:
        avg_completion_rate = np.mean([r['completion_rate'] for r in successful_results])
        avg_total_time = np.mean([r['total_time'] for r in successful_results])
        avg_reward = np.mean([r['total_reward'] for r in successful_results])
        avg_steps = np.mean([r['steps'] for r in successful_results])
        success_rate = len(successful_results) / len(results)
    else:
        avg_completion_rate = 0.0
        avg_total_time = float('inf')
        avg_reward = -1000.0
        avg_steps = 0.0
        success_rate = 0.0
    
    # 打印结果
    print(f"\n=== 测试结果 ===")
    print(f"测试DAG数量: {len(test_dags)}")
    print(f"成功调度数量: {len(successful_results)}")
    print(f"成功率: {success_rate:.2f}")
    print(f"平均完成率: {avg_completion_rate:.2f}")
    print(f"平均总时间: {avg_total_time:.2f}s")
    print(f"平均奖励: {avg_reward:.2f}")
    print(f"平均步数: {avg_steps:.2f}")
    print(f"测试用时: {test_time:.2f}s")
    
    # 保存详细结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_dir = f"test_results/enhanced_sac_{timestamp}"
    os.makedirs(results_dir, exist_ok=True)
    
    # 保存结果
    results_path = os.path.join(results_dir, "test_results.npy")
    np.save(results_path, results)
    
    # 保存统计摘要
    summary = {
        'test_dags': len(test_dags),
        'successful_dags': len(successful_results),
        'success_rate': success_rate,
        'avg_completion_rate': avg_completion_rate,
        'avg_total_time': avg_total_time,
        'avg_reward': avg_reward,
        'avg_steps': avg_steps,
        'test_time': test_time,
        'config': {
            'num_edge_servers': config.NUM_EDGE_SERVERS,
            'max_tasks': config.MAX_TASKS_NUM,
            'state_dim': config.ENHANCED_STATE_DIM,
            'action_dim': config.COMPOUND_ACTION_DIM
        }
    }
    
    summary_path = os.path.join(results_dir, "test_summary.npy")
    np.save(summary_path, summary)
    
    print(f"测试结果已保存到: {results_dir}")
    
    return results, summary

def main():
    parser = argparse.ArgumentParser(description='测试增强LLM调度系统')
    parser.add_argument('--model_path', type=str, required=True, help='模型文件路径')
    parser.add_argument('--edge_num', type=int, default=5, help='边缘设备数量')
    parser.add_argument('--task_num', type=int, default=20, help='最大任务数量')
    parser.add_argument('--test_num', type=int, default=50, help='测试DAG数量')
    parser.add_argument('--deterministic', type=str, default='True', help='是否使用确定性策略')
    parser.add_argument('--debug', type=str, default='False', help='是否打印调试信息')
    parser.add_argument('--cuda', type=str, default='True', help='是否使用GPU')
    
    args = parser.parse_args()
    
    # 创建配置
    config = EnhancedLLMConfig()
    config.update_edge_servers(args.edge_num, args.task_num)
    config.CUDA = args.cuda.lower() == 'true'
    
    deterministic = args.deterministic.lower() == 'true'
    debug = args.debug.lower() == 'true'
    
    print(f"测试配置:")
    print(f"  模型路径: {args.model_path}")
    print(f"  边缘设备数量: {config.NUM_EDGE_SERVERS}")
    print(f"  最大任务数量: {config.MAX_TASKS_NUM}")
    print(f"  测试DAG数量: {args.test_num}")
    print(f"  确定性策略: {deterministic}")
    print(f"  调试模式: {debug}")
    
    # 开始测试
    test_dags = list(range(args.test_num))
    results, summary = test_enhanced_sac(
        args.model_path, config, test_dags, deterministic, debug
    )
    
    return results, summary

if __name__ == "__main__":
    main()
