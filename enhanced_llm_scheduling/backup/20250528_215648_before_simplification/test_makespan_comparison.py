#!/usr/bin/env python3
"""
简化的Makespan对比测试脚本
测试xLSTM、LSTM和基线算法的Makespan性能
"""

import os
import sys
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')

# 添加路径
sys.path.append('..')
sys.path.append('.')

from enhanced_data_loader import EnhancedDataLoader
from lstm_sac_agent import LSTMSACAgent
from xlstm_sac_agent import XLSTMSACAgent
import enhanced_config as config

def simple_random_baseline(data_loader, dag_idx):
    """简单随机基线算法"""
    start_time = time.time()

    # 加载DAG数据
    task_features = data_loader.load_task_features(dag_idx)
    adj_matrix = data_loader.load_adjacency_matrix(dag_idx)
    machine_resources = data_loader.load_machines_resource()
    comm_speed = data_loader.load_machine_commu_speed()
    memory_status = data_loader.load_memory_status()
    num_tasks = task_features.shape[1]

    # 初始化环境
    data_loader._init_step_state(task_features, adj_matrix, machine_resources, comm_speed, memory_status)

    completed_tasks = 0
    max_steps = num_tasks * 3

    for step in range(max_steps):
        ready_tasks = data_loader.get_ready_tasks()
        if not ready_tasks:
            break

        # 随机选择任务和机器
        task_choice = np.random.uniform(-1, 1)
        machine_choice = np.random.uniform(-1, 1)
        action = np.array([task_choice, machine_choice])

        try:
            _, _, done, info = data_loader.step_enhanced_task(action, debug=False)
            if done:
                completed_tasks = len(info.get('completed_tasks', set()))
                break
        except Exception:
            break

    algorithm_time = time.time() - start_time
    completion_rate = completed_tasks / num_tasks

    # 获取makespan
    if data_loader._step_state is not None and 'total_completion_time' in data_loader._step_state:
        makespan = data_loader._step_state['total_completion_time'] / 1000.0
    else:
        makespan = 0.0

    return {
        'completion_rate': completion_rate,
        'makespan': makespan,
        'completed_tasks': completed_tasks,
        'algorithm_time': algorithm_time
    }

def simple_round_robin_baseline(data_loader, dag_idx):
    """简单轮询基线算法"""
    start_time = time.time()

    # 加载DAG数据
    task_features = data_loader.load_task_features(dag_idx)
    adj_matrix = data_loader.load_adjacency_matrix(dag_idx)
    machine_resources = data_loader.load_machines_resource()
    comm_speed = data_loader.load_machine_commu_speed()
    memory_status = data_loader.load_memory_status()
    num_tasks = task_features.shape[1]
    num_machines = len(machine_resources)

    # 初始化环境
    data_loader._init_step_state(task_features, adj_matrix, machine_resources, comm_speed, memory_status)

    completed_tasks = 0
    max_steps = num_tasks * 3
    machine_counter = 0

    for step in range(max_steps):
        ready_tasks = data_loader.get_ready_tasks()
        if not ready_tasks:
            break

        # 选择第一个就绪任务
        task_choice = -1.0

        # 轮询选择机器
        machine_idx = machine_counter % num_machines
        machine_choice = -1.0 + (2.0 / max(num_machines - 1, 1)) * machine_idx
        machine_counter += 1

        action = np.array([task_choice, machine_choice])

        try:
            _, _, done, info = data_loader.step_enhanced_task(action, debug=False)
            if done:
                completed_tasks = len(info.get('completed_tasks', set()))
                break
        except Exception:
            break

    algorithm_time = time.time() - start_time
    completion_rate = completed_tasks / num_tasks

    # 获取makespan
    if data_loader._step_state is not None and 'total_completion_time' in data_loader._step_state:
        makespan = data_loader._step_state['total_completion_time'] / 1000.0
    else:
        makespan = 0.0

    return {
        'completion_rate': completion_rate,
        'makespan': makespan,
        'completed_tasks': completed_tasks,
        'algorithm_time': algorithm_time
    }

def test_agent_makespan(agent, data_loader, dag_indices, agent_name):
    """测试智能体的makespan性能"""
    print(f"\n🧪 测试 {agent_name} 性能...")

    results = []

    for i, dag_idx in enumerate(dag_indices):
        try:
            start_time = time.time()

            # 加载DAG数据
            task_features = data_loader.load_task_features(dag_idx)
            adj_matrix = data_loader.load_adjacency_matrix(dag_idx)
            machine_resources = data_loader.load_machines_resource()
            comm_speed = data_loader.load_machine_commu_speed()
            memory_status = data_loader.load_memory_status()
            num_tasks = task_features.shape[1]

            # 初始化环境
            data_loader._init_step_state(task_features, adj_matrix, machine_resources, comm_speed, memory_status)

            # 获取初始状态
            state = data_loader.process_state_as_enhanced_sequence(
                task_features, adj_matrix, machine_resources, comm_speed, memory_status)

            completed_tasks = 0
            max_steps = num_tasks * 3

            for step in range(max_steps):
                ready_tasks = data_loader.get_ready_tasks()
                if not ready_tasks:
                    break

                # 获取就绪掩码
                ready_mask = np.zeros(config.SEQ_LEN)
                for j, _ in enumerate(ready_tasks[:config.SEQ_LEN]):
                    if j < config.SEQ_LEN:
                        ready_mask[j] = 1.0

                # 选择动作
                action = agent.select_action(state, ready_mask, deterministic=True)

                # 执行动作
                next_state, _, done, info = data_loader.step_enhanced_task(action, debug=False)

                if done:
                    completed_tasks = len(info.get('completed_tasks', set()))
                    break

                state = next_state

            algorithm_time = time.time() - start_time
            completion_rate = completed_tasks / num_tasks

            # 获取makespan
            if data_loader._step_state is not None and 'total_completion_time' in data_loader._step_state:
                makespan = data_loader._step_state['total_completion_time'] / 1000.0
            else:
                makespan = 0.0

            results.append({
                'dag_idx': dag_idx,
                'completion_rate': completion_rate,
                'makespan': makespan,
                'completed_tasks': completed_tasks,
                'algorithm_time': algorithm_time
            })

            if (i + 1) % 10 == 0:
                print(f"  已完成 {i + 1}/{len(dag_indices)} 个DAG")

        except Exception as e:
            print(f"  ❌ DAG {dag_idx} 测试失败: {e}")
            results.append({
                'dag_idx': dag_idx,
                'completion_rate': 0.0,
                'makespan': 0.0,
                'completed_tasks': 0,
                'algorithm_time': 0.0
            })

    return results

def test_baseline_makespan(baseline_func, data_loader, dag_indices, baseline_name):
    """测试基线算法的makespan性能"""
    print(f"\n🧪 测试 {baseline_name} 基线算法性能...")

    results = []

    for i, dag_idx in enumerate(dag_indices):
        try:
            result = baseline_func(data_loader, dag_idx)
            result['dag_idx'] = dag_idx
            results.append(result)

            if (i + 1) % 10 == 0:
                print(f"  已完成 {i + 1}/{len(dag_indices)} 个DAG")

        except Exception as e:
            print(f"  ❌ DAG {dag_idx} 测试失败: {e}")
            results.append({
                'dag_idx': dag_idx,
                'completion_rate': 0.0,
                'makespan': 0.0,
                'completed_tasks': 0,
                'algorithm_time': 0.0
            })

    return results

def load_models(edge_num, device='cuda'):
    """加载训练好的模型"""
    models = {}

    models_dir = "models"
    if not os.path.exists(models_dir):
        print("❌ models目录不存在")
        return models

    # 设置配置
    config.EDGE_NUM = edge_num
    config.ENHANCED_STATE_DIM = 5 + 1 + edge_num*4 + 6

    # 查找LSTM模型
    lstm_dirs = [d for d in os.listdir(models_dir) if d.startswith(f"lstm_sac_{edge_num}devices")]
    if lstm_dirs:
        latest_lstm_dir = sorted(lstm_dirs)[-1]
        lstm_model_path = os.path.join(models_dir, latest_lstm_dir, "lstm_sac_model.pth")
        if os.path.exists(lstm_model_path):
            try:
                lstm_agent = LSTMSACAgent(
                    state_dim=config.ENHANCED_STATE_DIM,
                    action_dim=2,
                    hidden_dim=config.HIDDEN_DIM,
                    lr=config.LEARNING_RATE,
                    device=device
                )
                lstm_agent.load_models(lstm_model_path)
                models['LSTM'] = lstm_agent
                print(f"✅ 加载LSTM模型: {lstm_model_path}")
            except Exception as e:
                print(f"❌ 加载LSTM模型失败: {e}")

    # 查找xLSTM模型
    xlstm_dirs = [d for d in os.listdir(models_dir) if d.startswith(f"xlstm_sac_{edge_num}devices")]
    if xlstm_dirs:
        latest_xlstm_dir = sorted(xlstm_dirs)[-1]
        xlstm_model_path = os.path.join(models_dir, latest_xlstm_dir, "xlstm_sac_model.pth")
        if os.path.exists(xlstm_model_path):
            try:
                xlstm_agent = XLSTMSACAgent(
                    state_dim=config.ENHANCED_STATE_DIM,
                    action_dim=2,
                    hidden_dim=config.HIDDEN_DIM,
                    lr=config.LEARNING_RATE,
                    device=device
                )
                xlstm_agent.load_models(xlstm_model_path)
                models['xLSTM'] = xlstm_agent
                print(f"✅ 加载xLSTM模型: {xlstm_model_path}")
            except Exception as e:
                print(f"❌ 加载xLSTM模型失败: {e}")

    return models

def analyze_and_plot_results(all_results, edge_num):
    """分析结果并绘制对比图"""

    # 计算统计指标
    summary = {}
    for algorithm, results in all_results.items():
        if not results:
            continue

        completion_rates = [r['completion_rate'] for r in results]
        makespans = [r['makespan'] for r in results if r['makespan'] > 0]  # 只考虑有效makespan

        # 计算成功完成的DAG
        successful_dags = [r for r in results if r['completion_rate'] >= 1.0]
        success_rate = len(successful_dags) / len(results) * 100

        # 条件平均makespan（只考虑成功完成的DAG）
        conditional_makespans = [r['makespan'] for r in successful_dags if r['makespan'] > 0]
        conditional_avg_makespan = np.mean(conditional_makespans) if conditional_makespans else 0

        summary[algorithm] = {
            'avg_completion_rate': np.mean(completion_rates),
            'avg_makespan': np.mean(makespans) if makespans else 0,
            'success_rate': success_rate,
            'conditional_avg_makespan': conditional_avg_makespan,
            'successful_dags': len(successful_dags)
        }

    # 打印结果
    print(f"\n📊 {edge_num}设备Makespan性能对比:")
    print("=" * 80)
    print(f"{'算法':<12} {'平均完成率':<10} {'成功率':<8} {'平均Makespan':<12} {'条件平均Makespan':<15}")
    print("-" * 80)

    for algorithm, stats in summary.items():
        print(f"{algorithm:<12} "
              f"{stats['avg_completion_rate']:.2%} "
              f"{stats['success_rate']:>6.1f}% "
              f"{stats['avg_makespan']:>10.2f}s "
              f"{stats['conditional_avg_makespan']:>13.2f}s")

    # 绘制对比图
    algorithms = list(summary.keys())

    # 设置绘图样式
    plt.style.use('ggplot')
    plt.rcParams['font.family'] = 'DejaVu Sans'
    plt.rcParams['font.size'] = 12

    # 1. Makespan对比
    plt.figure(figsize=(12, 8))
    makespans = [summary[alg]['conditional_avg_makespan'] for alg in algorithms]
    colors = plt.cm.Set3(np.linspace(0, 1, len(algorithms)))

    bars = plt.bar(algorithms, makespans, color=colors, alpha=0.8)
    plt.ylabel('Conditional Average Makespan (seconds)')
    plt.title(f'{edge_num}设备 - Algorithm Performance Comparison: Makespan')
    plt.xticks(rotation=45)

    # 添加数值标签
    for bar, makespan in zip(bars, makespans):
        if makespan > 0:
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(makespans)*0.01,
                    f'{makespan:.1f}s', ha='center', va='bottom')

    plt.tight_layout()
    plt.savefig(f'makespan_comparison_{edge_num}devices.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 2. 完成率对比
    plt.figure(figsize=(12, 8))
    completion_rates = [summary[alg]['avg_completion_rate'] for alg in algorithms]

    bars = plt.bar(algorithms, completion_rates, color=colors, alpha=0.8)
    plt.ylabel('Average Completion Rate')
    plt.title(f'{edge_num}设备 - Algorithm Performance Comparison: Completion Rate')
    plt.xticks(rotation=45)
    plt.ylim(0, 1.1)
    plt.gca().yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: f'{y:.0%}'))

    # 添加数值标签
    for bar, rate in zip(bars, completion_rates):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{rate:.1%}', ha='center', va='bottom')

    plt.tight_layout()
    plt.savefig(f'completion_rate_comparison_{edge_num}devices.png', dpi=300, bbox_inches='tight')
    plt.close()

    print(f"\n📈 对比图已生成:")
    print(f"  📊 makespan_comparison_{edge_num}devices.png")
    print(f"  📊 completion_rate_comparison_{edge_num}devices.png")

    return summary

def main():
    """主函数"""
    print("🧪 Makespan性能对比测试")
    print("=" * 60)

    # 配置参数
    edge_num = 2  # 从2设备开始测试
    test_dag_count = 50
    device = 'cuda' if 'torch' in sys.modules and hasattr(sys.modules['torch'], 'cuda') and sys.modules['torch'].cuda.is_available() else 'cpu'

    print(f"📊 测试配置:")
    print(f"  边缘设备数: {edge_num}")
    print(f"  测试DAG数量: {test_dag_count}")
    print(f"  计算设备: {device}")

    # 设置测试数据路径 - 使用实际存在的数据集
    test_data_path = f"../dataset/testing/DAG_10_edges_{edge_num}_mem_24GB_density_0.4_0.6"
    if not os.path.exists(test_data_path):
        print(f"❌ 测试数据集不存在: {test_data_path}")
        print("可用的测试数据集:")
        import glob
        available_datasets = glob.glob("../dataset/testing/DAG_*")
        for dataset in available_datasets:
            print(f"  {dataset}")
        return

    # 创建数据加载器
    # 首先设置配置
    config.EDGE_NUM = edge_num
    config.ENHANCED_STATE_DIM = 5 + 1 + edge_num*4 + 6

    # 创建增强配置对象
    from enhanced_config import EnhancedLLMConfig
    enhanced_config = EnhancedLLMConfig()
    enhanced_config.NUM_EDGE_SERVERS = edge_num
    enhanced_config.update_edge_servers(edge_num)

    # 创建数据加载器，但需要手动设置数据路径
    data_loader = EnhancedDataLoader(enhanced_config)
    data_loader.data_path = test_data_path

    # 生成测试DAG索引
    dag_indices = list(range(test_dag_count))

    # 加载训练好的模型
    print(f"\n🔧 加载训练好的模型...")
    trained_models = load_models(edge_num, device)

    # 开始测试
    all_results = {}

    # 测试训练好的模型
    for model_name, agent in trained_models.items():
        results = test_agent_makespan(agent, data_loader, dag_indices, model_name)
        all_results[model_name] = results

    # 测试基线算法
    baseline_algorithms = {
        'Random': simple_random_baseline,
        'Round_Robin': simple_round_robin_baseline
    }

    for baseline_name, baseline_func in baseline_algorithms.items():
        results = test_baseline_makespan(baseline_func, data_loader, dag_indices, baseline_name)
        all_results[baseline_name] = results

    # 分析结果并绘图
    summary = analyze_and_plot_results(all_results, edge_num)

    # 保存结果
    timestamp = time.strftime("%Y%m%d_%H%M%S")

    # 保存详细结果
    with pd.ExcelWriter(f"makespan_test_results_{edge_num}devices_{timestamp}.xlsx") as writer:
        for algorithm, results in all_results.items():
            if results:
                df = pd.DataFrame(results)
                df.to_excel(writer, sheet_name=algorithm, index=False)

    # 保存汇总统计
    summary_df = pd.DataFrame(summary).T
    summary_df.to_excel(f"makespan_summary_{edge_num}devices_{timestamp}.xlsx")

    print(f"\n🎉 测试完成！")
    print(f"📁 结果已保存到:")
    print(f"  📊 makespan_test_results_{edge_num}devices_{timestamp}.xlsx")
    print(f"  📊 makespan_summary_{edge_num}devices_{timestamp}.xlsx")

if __name__ == "__main__":
    import torch
    main()
