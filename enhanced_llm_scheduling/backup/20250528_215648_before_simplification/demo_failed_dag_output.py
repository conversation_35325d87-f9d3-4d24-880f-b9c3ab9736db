#!/usr/bin/env python3
"""
演示失败DAG输出功能
"""

def demo_failed_dag_output():
    """演示失败DAG输出的格式"""
    
    # 模拟一些失败的DAG数据
    failed_dags = [
        {
            'episode': 5,
            'dag_id': 104,
            'dag_difficulty': 0.8,
            'completion_rate': 0.0,
            'completed_tasks': 0,
            'total_tasks': 15,
            'failed_tasks': 15,
            'max_ready_tasks': 4,
            'total_memory_demand': 45.6,
            'llm_task_count': 8,
            'llm_ratio': 0.533,
            'avg_memory_per_task': 3.04,
            'deadlock_detected': False,
            'memory_overflow': True,
            'high_parallelism': False,
            'reason': 'memory_insufficient'
        },
        {
            'episode': 12,
            'dag_id': 18,
            'dag_difficulty': 0.9,
            'completion_rate': 0.267,
            'completed_tasks': 4,
            'total_tasks': 15,
            'failed_tasks': 11,
            'max_ready_tasks': 3,
            'total_memory_demand': 38.2,
            'llm_task_count': 6,
            'llm_ratio': 0.4,
            'avg_memory_per_task': 2.55,
            'deadlock_detected': True,
            'memory_overflow': False,
            'high_parallelism': False,
            'reason': 'deadlock'
        },
        {
            'episode': 18,
            'dag_id': 102,
            'dag_difficulty': 0.75,
            'completion_rate': 0.733,
            'completed_tasks': 11,
            'total_tasks': 15,
            'failed_tasks': 4,
            'max_ready_tasks': 5,
            'total_memory_demand': 42.1,
            'llm_task_count': 7,
            'llm_ratio': 0.467,
            'avg_memory_per_task': 2.81,
            'deadlock_detected': False,
            'memory_overflow': False,
            'high_parallelism': False,
            'reason': 'unknown'
        }
    ]
    
    device_num = 5  # 5个边缘设备 + 1个用户设备
    
    print("🔍 失败DAG分析摘要:")
    print(f"  失败episode数: {len(failed_dags)}")
    print(f"  唯一失败DAG数: {len(set(dag['dag_id'] for dag in failed_dags))}")
    print(f"  内存溢出次数: {sum(1 for dag in failed_dags if dag['memory_overflow'])}")
    print(f"  死锁次数: {sum(1 for dag in failed_dags if dag['deadlock_detected'])}")
    print(f"  高并行度失败次数: {sum(1 for dag in failed_dags if dag['high_parallelism'])}")
    print(f"  失败DAG ID列表: {list(set(dag['dag_id'] for dag in failed_dags))}")
    
    # 详细输出每个失败的DAG信息
    print(f"\n❌ 失败DAG详细信息:")
    print("=" * 80)
    for idx, dag_info in enumerate(failed_dags):
        print(f"\n失败DAG #{idx+1}:")
        print(f"  Episode: {dag_info['episode']}")
        print(f"  DAG ID: {dag_info['dag_id']}")
        print(f"  完成率: {dag_info['completion_rate']:.1%} ({dag_info['completed_tasks']}/{dag_info['total_tasks']})")
        print(f"  失败任务数: {dag_info['failed_tasks']}")
        print(f"  失败原因: {dag_info['reason']}")
        print(f"  最大并行度: {dag_info['max_ready_tasks']} (机器数: {device_num + 1})")
        print(f"  总内存需求: {dag_info['total_memory_demand']:.2f}GB")
        print(f"  LLM任务数: {dag_info['llm_task_count']} ({dag_info['llm_ratio']:.1%})")
        print(f"  平均任务内存: {dag_info['avg_memory_per_task']:.2f}GB")
        
        # 问题标识
        issues = []
        if dag_info['memory_overflow']:
            issues.append("内存溢出")
        if dag_info['deadlock_detected']:
            issues.append("死锁")
        if dag_info['high_parallelism']:
            issues.append("高并行度")
        
        if issues:
            print(f"  检测到的问题: {', '.join(issues)}")
        else:
            print(f"  检测到的问题: 无明确问题标识")
        
        # 可能的失败原因分析
        if dag_info['completion_rate'] == 0.0:
            print(f"  分析: 完全失败，可能是代码错误或极难调度的DAG")
        elif dag_info['completion_rate'] < 0.5:
            print(f"  分析: 严重失败，可能是资源不足或调度策略问题")
        else:
            print(f"  分析: 部分失败，可能是个别任务调度困难")
    
    print("=" * 80)
    
    print(f"\n💡 失败原因总结:")
    print(f"  - DAG 104: 完全失败，内存溢出导致无法调度")
    print(f"  - DAG 18: 部分失败，死锁导致后续任务无法执行")
    print(f"  - DAG 102: 部分失败，原因不明，可能是调度策略问题")
    
    print(f"\n🔧 建议改进措施:")
    print(f"  1. 对于内存溢出问题：优化内存分配策略，考虑任务分割")
    print(f"  2. 对于死锁问题：改进依赖关系检测和资源释放机制")
    print(f"  3. 对于未知失败：增加更详细的失败原因记录和分析")

if __name__ == "__main__":
    print("🎯 失败DAG输出功能演示")
    print("=" * 80)
    demo_failed_dag_output()
