#!/usr/bin/env python3
"""
检查训练状态脚本
"""

import os
import glob
import time
from datetime import datetime

def check_training_progress():
    """检查训练进度"""
    print("🔍 检查训练状态...")
    print("=" * 60)
    
    # 检查是否有训练进程在运行
    pid_file = "training_pid.txt"
    if os.path.exists(pid_file):
        with open(pid_file, 'r') as f:
            pid = f.read().strip()
        
        # 检查进程是否还在运行
        try:
            os.kill(int(pid), 0)  # 发送信号0检查进程是否存在
            print(f"✅ 训练进程正在运行 (PID: {pid})")
        except (OSError, ValueError):
            print(f"❌ 训练进程已停止 (PID: {pid})")
    else:
        print("ℹ️ 未找到训练进程信息")
    
    print()
    
    # 检查日志文件
    log_files = glob.glob("auto_train_log_*.txt") + glob.glob("background_training_*.log")
    if log_files:
        latest_log = max(log_files, key=os.path.getctime)
        print(f"📝 最新日志文件: {latest_log}")
        
        # 显示最后几行日志
        try:
            with open(latest_log, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                if lines:
                    print("📊 最新日志内容 (最后10行):")
                    print("-" * 40)
                    for line in lines[-10:]:
                        print(f"  {line.rstrip()}")
                    print("-" * 40)
        except Exception as e:
            print(f"❌ 读取日志文件失败: {e}")
    else:
        print("⚠️ 未找到日志文件")
    
    print()
    
    # 检查已生成的模型
    models_dir = "models"
    if os.path.exists(models_dir):
        print("📁 检查已生成的模型:")
        
        # 查找新训练的模型（2、3、4、6设备）
        target_devices = [2, 3, 4, 6]
        model_types = ['lstm', 'xlstm']
        
        found_models = {}
        for device_num in target_devices:
            found_models[device_num] = {}
            for model_type in model_types:
                pattern = f"{model_type}_sac_{device_num}devices_*"
                matches = glob.glob(os.path.join(models_dir, pattern))
                found_models[device_num][model_type] = len(matches)
        
        # 显示结果
        print(f"{'设备数':<6} {'LSTM':<6} {'xLSTM':<6} {'状态'}")
        print("-" * 30)
        for device_num in target_devices:
            lstm_count = found_models[device_num]['lstm']
            xlstm_count = found_models[device_num]['xlstm']
            
            if lstm_count > 0 and xlstm_count > 0:
                status = "✅ 完成"
            elif lstm_count > 0 or xlstm_count > 0:
                status = "🔄 进行中"
            else:
                status = "⏳ 等待"
            
            print(f"{device_num:<6} {lstm_count:<6} {xlstm_count:<6} {status}")
        
        # 显示所有相关模型
        all_models = []
        for device_num in target_devices:
            for model_type in model_types:
                pattern = f"{model_type}_sac_{device_num}devices_*"
                matches = glob.glob(os.path.join(models_dir, pattern))
                all_models.extend(matches)
        
        if all_models:
            print(f"\n📦 找到的模型目录:")
            for model in sorted(all_models):
                model_name = os.path.basename(model)
                # 检查模型文件是否存在
                model_file = os.path.join(model, f"{model_name.split('_')[0]}_sac_model.pth")
                if os.path.exists(model_file):
                    size = os.path.getsize(model_file) / (1024*1024)  # MB
                    print(f"  ✅ {model_name} ({size:.1f}MB)")
                else:
                    print(f"  🔄 {model_name} (训练中...)")
    else:
        print("❌ models目录不存在")
    
    print()
    print(f"🕐 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def main():
    """主函数"""
    check_training_progress()
    
    print("\n💡 有用的命令:")
    print("  实时查看日志: tail -f auto_train_log_*.txt")
    print("  再次检查状态: python check_training_status.py")
    print("  停止训练: kill $(cat training_pid.txt)")

if __name__ == "__main__":
    main()
