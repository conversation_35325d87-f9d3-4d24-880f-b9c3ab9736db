#!/usr/bin/env python3
"""
对比测试：简化版本 vs 复杂版本
验证简化设计的有效性
"""

import os
import sys
import time
import numpy as np
import matplotlib.pyplot as plt
import torch
import argparse

# 添加路径
sys.path.append('..')
sys.path.append('.')

from enhanced_data_loader import EnhancedDataLoader
from simplified_config import SimplifiedConfig
from simplified_event_system import SimplifiedSchedulingEnvironment
from simplified_sac_agent import SimplifiedSACAgent

# 复杂版本
from enhanced_config import EnhancedLLMConfig
from improved_xlstm_networks import ImprovedXLSTMSACAgent

def test_simplified_system(config, episodes=50, test_dag=0):
    """测试简化系统"""
    print("🔧 测试简化系统...")
    
    # 创建数据加载器
    data_loader = EnhancedDataLoader(config, debug=False)
    
    # 创建环境和智能体
    env = SimplifiedSchedulingEnvironment(config)
    agent = SimplifiedSACAgent(config, device="cuda")
    
    results = {
        'makespans': [],
        'completion_rates': [],
        'rewards': [],
        'convergence_episodes': []
    }
    
    for episode in range(episodes):
        # 加载固定DAG
        task_features = data_loader.load_task_features(test_dag)
        adj_matrix = data_loader.load_adjacency_matrix(test_dag)
        machine_resources = data_loader.load_memory_status()
        
        # 重置环境
        state = env.reset(task_features, adj_matrix, machine_resources)
        
        episode_reward = 0
        step_count = 0
        max_steps = task_features.shape[1] * 3
        
        while step_count < max_steps:
            step_count += 1
            
            # 选择动作
            action = agent.select_action(state, deterministic=episode > 30)
            
            # 执行动作
            next_state, reward, done, info = env.step(action)
            
            # 存储经验
            agent.store_transition(state, action, reward, next_state, done)
            
            # 更新
            state = next_state
            episode_reward += reward
            
            # 训练
            if len(agent.replay_buffer) > agent.batch_size:
                agent.update()
            
            if done:
                break
        
        # 记录结果
        completion_rate = info.get('completion_rate', 0.0)
        makespan = info.get('makespan', 1000.0)
        
        results['completion_rates'].append(completion_rate)
        results['makespans'].append(makespan if completion_rate >= 0.8 else 1000.0)
        results['rewards'].append(episode_reward)
        
        if episode % 10 == 0:
            print(f"  Episode {episode:2d}: 完成率={completion_rate:.1%}, "
                  f"Makespan={makespan:6.1f}s, 奖励={episode_reward:6.2f}")
    
    return results

def test_complex_system(config, episodes=50, test_dag=0):
    """测试复杂系统"""
    print("🔧 测试复杂系统...")
    
    # 创建数据加载器
    data_loader = EnhancedDataLoader(config, debug=False)
    
    # 创建智能体
    agent = ImprovedXLSTMSACAgent(
        state_dim=config.ENHANCED_STATE_DIM,
        action_dim=2,
        hidden_dim=256,
        seq_len=config.SEQ_LEN,
        num_machines=config.NUM_EDGE_SERVERS + 1,
        xlstm_layers=2,
        lr=3e-4,
        device="cuda"
    )
    
    results = {
        'makespans': [],
        'completion_rates': [],
        'rewards': [],
        'convergence_episodes': []
    }
    
    for episode in range(episodes):
        # 加载固定DAG
        task_features = data_loader.load_task_features(test_dag)
        adj_matrix = data_loader.load_adjacency_matrix(test_dag)
        machine_resources = data_loader.load_machines_resource()
        comm_speed = data_loader.load_machine_commu_speed()
        memory_status = data_loader.load_memory_status()
        
        # 重置内存状态
        original_memory = data_loader.load_memory_status()
        for i in range(len(memory_status)):
            memory_status[i] = original_memory[i]
        
        # 初始化环境
        data_loader._init_step_state(task_features, adj_matrix, machine_resources, comm_speed, memory_status)
        
        # 获取初始状态
        state = data_loader.process_state_as_enhanced_sequence(
            task_features, adj_matrix, machine_resources, comm_speed, memory_status
        )
        
        episode_reward = 0
        step_count = 0
        max_steps = task_features.shape[1] * 2
        done = False
        
        while not done and step_count < max_steps:
            step_count += 1
            
            # 获取就绪任务掩码
            ready_tasks = data_loader.get_ready_tasks()
            if not ready_tasks:
                break
            
            ready_mask = np.zeros(config.SEQ_LEN)
            for i, _ in enumerate(ready_tasks[:config.SEQ_LEN]):
                if i < config.SEQ_LEN:
                    ready_mask[i] = 1.0
            
            # 转换为tensor
            state_tensor = torch.FloatTensor(state).unsqueeze(0).to("cuda")
            ready_mask_tensor = torch.FloatTensor(ready_mask).unsqueeze(0).to("cuda")
            
            # 选择动作
            compound_action = agent.select_action(state_tensor, ready_mask_tensor, deterministic=episode > 30)
            
            # 执行动作
            next_state, reward, done, info = data_loader.step_enhanced_task(compound_action, debug=False)
            
            # 存储经验
            agent.store_transition(state_tensor, compound_action, reward, 
                                 torch.FloatTensor(next_state).unsqueeze(0), done)
            
            # 更新
            state = next_state
            episode_reward += reward
            
            # 训练
            if len(agent.replay_buffer) > 64:
                agent.update()
        
        # 记录结果
        completed_tasks = info.get("completed_tasks", set())
        total_tasks = task_features.shape[1]
        completion_rate = len(completed_tasks) / total_tasks
        makespan = info.get("total_time", 1000000) / 1000.0
        
        results['completion_rates'].append(completion_rate)
        results['makespans'].append(makespan if completion_rate >= 0.8 else 1000.0)
        results['rewards'].append(episode_reward)
        
        if episode % 10 == 0:
            print(f"  Episode {episode:2d}: 完成率={completion_rate:.1%}, "
                  f"Makespan={makespan:6.1f}s, 奖励={episode_reward:6.2f}")
    
    return results

def compare_systems():
    """对比两个系统"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--episodes", default=50, type=int, help="测试回合数")
    parser.add_argument("--device_num", default=5, type=int, help="边缘设备数量")
    parser.add_argument("--test_dag", default=0, type=int, help="测试DAG索引")
    args = parser.parse_args()
    
    print("🎯 系统对比测试")
    print("=" * 80)
    print(f"测试参数: {args}")
    
    # 创建配置
    simplified_config = SimplifiedConfig()
    simplified_config.update_edge_servers(args.device_num)
    
    complex_config = EnhancedLLMConfig()
    complex_config.update_edge_servers(args.device_num)
    
    print(f"\n📊 配置对比:")
    print(f"  简化版本: 状态维度={simplified_config.STATE_DIM}, 动作维度={simplified_config.ACTION_DIM} (离散)")
    print(f"  复杂版本: 状态维度={complex_config.ENHANCED_STATE_DIM}, 动作维度=2 (复合连续)")
    
    # 测试简化系统
    print(f"\n🔧 测试简化系统 (离散动作 + 稀疏奖励 + 事件驱动)...")
    start_time = time.time()
    simplified_results = test_simplified_system(simplified_config, args.episodes, args.test_dag)
    simplified_time = time.time() - start_time
    
    # 测试复杂系统
    print(f"\n🔧 测试复杂系统 (复合动作 + 密集奖励 + 步进式)...")
    start_time = time.time()
    complex_results = test_complex_system(complex_config, args.episodes, args.test_dag)
    complex_time = time.time() - start_time
    
    # 分析结果
    print(f"\n📈 结果对比:")
    print("=" * 80)
    
    # 简化系统统计
    simplified_success = [c for c in simplified_results['completion_rates'] if c >= 0.8]
    simplified_makespans = [m for m in simplified_results['makespans'] if m < 1000]
    
    print(f"🔧 简化系统:")
    print(f"  成功率: {len(simplified_success)}/{args.episodes} ({len(simplified_success)/args.episodes:.1%})")
    if simplified_makespans:
        print(f"  平均Makespan: {np.mean(simplified_makespans):.2f}s")
        print(f"  最佳Makespan: {min(simplified_makespans):.2f}s")
        print(f"  Makespan标准差: {np.std(simplified_makespans):.2f}s")
    print(f"  平均奖励: {np.mean(simplified_results['rewards']):.2f}")
    print(f"  训练时间: {simplified_time:.1f}s")
    
    # 复杂系统统计
    complex_success = [c for c in complex_results['completion_rates'] if c >= 0.8]
    complex_makespans = [m for m in complex_results['makespans'] if m < 1000]
    
    print(f"\n🔧 复杂系统:")
    print(f"  成功率: {len(complex_success)}/{args.episodes} ({len(complex_success)/args.episodes:.1%})")
    if complex_makespans:
        print(f"  平均Makespan: {np.mean(complex_makespans):.2f}s")
        print(f"  最佳Makespan: {min(complex_makespans):.2f}s")
        print(f"  Makespan标准差: {np.std(complex_makespans):.2f}s")
    print(f"  平均奖励: {np.mean(complex_results['rewards']):.2f}")
    print(f"  训练时间: {complex_time:.1f}s")
    
    # 绘制对比图
    plt.figure(figsize=(15, 10))
    
    # 完成率对比
    plt.subplot(2, 3, 1)
    plt.plot(simplified_results['completion_rates'], label='简化系统', alpha=0.7)
    plt.plot(complex_results['completion_rates'], label='复杂系统', alpha=0.7)
    plt.title('完成率对比')
    plt.xlabel('Episode')
    plt.ylabel('Completion Rate')
    plt.legend()
    plt.ylim(0, 1)
    
    # Makespan对比
    plt.subplot(2, 3, 2)
    simplified_plot = [m if m < 1000 else np.nan for m in simplified_results['makespans']]
    complex_plot = [m if m < 1000 else np.nan for m in complex_results['makespans']]
    plt.plot(simplified_plot, label='简化系统', alpha=0.7)
    plt.plot(complex_plot, label='复杂系统', alpha=0.7)
    plt.title('Makespan对比')
    plt.xlabel('Episode')
    plt.ylabel('Makespan (seconds)')
    plt.legend()
    
    # 奖励对比
    plt.subplot(2, 3, 3)
    plt.plot(simplified_results['rewards'], label='简化系统', alpha=0.7)
    plt.plot(complex_results['rewards'], label='复杂系统', alpha=0.7)
    plt.title('奖励对比')
    plt.xlabel('Episode')
    plt.ylabel('Episode Reward')
    plt.legend()
    
    # 成功率滑动平均
    plt.subplot(2, 3, 4)
    window = 10
    simplified_success_rate = []
    complex_success_rate = []
    for i in range(len(simplified_results['completion_rates'])):
        start = max(0, i - window + 1)
        simplified_window = simplified_results['completion_rates'][start:i+1]
        complex_window = complex_results['completion_rates'][start:i+1]
        simplified_success_rate.append(sum(1 for c in simplified_window if c >= 0.8) / len(simplified_window))
        complex_success_rate.append(sum(1 for c in complex_window if c >= 0.8) / len(complex_window))
    
    plt.plot(simplified_success_rate, label='简化系统', alpha=0.7)
    plt.plot(complex_success_rate, label='复杂系统', alpha=0.7)
    plt.title(f'成功率滑动平均 (窗口={window})')
    plt.xlabel('Episode')
    plt.ylabel('Success Rate')
    plt.legend()
    plt.ylim(0, 1)
    
    # Makespan分布对比
    plt.subplot(2, 3, 5)
    if simplified_makespans:
        plt.hist(simplified_makespans, bins=15, alpha=0.5, label='简化系统', density=True)
    if complex_makespans:
        plt.hist(complex_makespans, bins=15, alpha=0.5, label='复杂系统', density=True)
    plt.title('Makespan分布对比')
    plt.xlabel('Makespan (seconds)')
    plt.ylabel('Density')
    plt.legend()
    
    # 系统特点对比
    plt.subplot(2, 3, 6)
    categories = ['成功率', '平均Makespan\n(归一化)', '训练速度\n(归一化)']
    simplified_scores = [
        len(simplified_success) / args.episodes,
        1 - (np.mean(simplified_makespans) / 1000 if simplified_makespans else 0),
        1 - (simplified_time / max(simplified_time, complex_time))
    ]
    complex_scores = [
        len(complex_success) / args.episodes,
        1 - (np.mean(complex_makespans) / 1000 if complex_makespans else 0),
        1 - (complex_time / max(simplified_time, complex_time))
    ]
    
    x = np.arange(len(categories))
    width = 0.35
    plt.bar(x - width/2, simplified_scores, width, label='简化系统', alpha=0.7)
    plt.bar(x + width/2, complex_scores, width, label='复杂系统', alpha=0.7)
    plt.title('综合性能对比')
    plt.ylabel('归一化分数')
    plt.xticks(x, categories)
    plt.legend()
    plt.ylim(0, 1)
    
    plt.tight_layout()
    plt.savefig('simplified_vs_complex_comparison.png', dpi=300, bbox_inches='tight')
    print(f"\n📊 对比图已保存到: simplified_vs_complex_comparison.png")
    
    # 结论
    print(f"\n🎯 结论:")
    if len(simplified_success) > len(complex_success):
        print("  ✅ 简化系统在成功率方面表现更好")
    elif len(simplified_success) < len(complex_success):
        print("  ✅ 复杂系统在成功率方面表现更好")
    else:
        print("  ⚖️ 两个系统成功率相当")
    
    if simplified_makespans and complex_makespans:
        if np.mean(simplified_makespans) < np.mean(complex_makespans):
            print("  ✅ 简化系统在Makespan方面表现更好")
        else:
            print("  ✅ 复杂系统在Makespan方面表现更好")
    
    if simplified_time < complex_time:
        print("  ✅ 简化系统训练速度更快")
    else:
        print("  ✅ 复杂系统训练速度更快")

if __name__ == "__main__":
    compare_systems()
