#!/usr/bin/env python3
"""
训练改进的xLSTM调度系统
实现"先选任务，再分配机器"的清晰逻辑，专注于makespan优化
"""

import os
import sys
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
import argparse
from datetime import datetime

# 添加路径
sys.path.append('..')
sys.path.append('.')

from enhanced_data_loader import EnhancedDataLoader
from improved_xlstm_networks import ImprovedXLSTMSACAgent
from improved_reward_function import ImprovedRewardFunction
from enhanced_config import EnhancedLLMConfig

def calculate_memory_utilization(memory_status, machine_total_memory=None):
    """计算内存利用率"""
    # 安全处理memory_status的布尔判断
    if memory_status is None or (hasattr(memory_status, '__len__') and len(memory_status) == 0):
        return 0.0

    # 如果没有提供机器总内存信息，使用默认值
    if machine_total_memory is None:
        # 默认：用户设备16GB，边缘设备32GB
        machine_total_memory = [16.0] + [32.0] * (len(memory_status) - 1)

    total_available = sum(memory_status)
    total_capacity = sum(machine_total_memory[:len(memory_status)])

    if total_capacity <= 0:
        return 0.0

    utilization = 1.0 - (total_available / total_capacity)
    return max(0.0, min(1.0, utilization))

def calculate_load_balance_score(machine_finish_times):
    """计算负载均衡分数"""
    # 安全处理machine_finish_times的布尔判断
    if machine_finish_times is None or (hasattr(machine_finish_times, '__len__') and len(machine_finish_times) <= 1):
        return 1.0

    # 计算机器完成时间的标准差
    times = np.array(machine_finish_times)
    if np.max(times) == 0:
        return 1.0

    # 归一化标准差
    normalized_std = np.std(times) / (np.mean(times) + 1e-8)

    # 标准差越小，负载均衡越好
    balance_score = 1.0 / (1.0 + normalized_std)
    return max(0.0, min(1.0, balance_score))

def analyze_sequence_details(state, ready_mask, task_features, data_loader, episode, step_count):
    """分析序列的详细信息"""
    print(f"\n  🔬 [详细序列分析] Episode {episode}, Step {step_count}:")

    # 1. 基本信息
    print(f"    序列维度: {state.shape}")
    print(f"    就绪掩码: {ready_mask.sum()}/{len(ready_mask)} 个任务就绪")

    # 2. 就绪任务详情
    ready_indices = [i for i, mask in enumerate(ready_mask) if mask == 1]
    print(f"    就绪任务索引: {ready_indices}")

    # 3. 任务特征分析
    if task_features is not None:
        print(f"    任务特征矩阵形状: {task_features.shape}")
        for i in ready_indices[:3]:  # 只显示前3个就绪任务
            is_llm = task_features[2, i] == 1
            predicted_memory = data_loader._task_predicted_memory.get(i, 0)
            predicted_tokens = data_loader._task_predicted_output_tokens.get(i, 0)
            print(f"      任务{i}: {'LLM' if is_llm else '普通'}, "
                  f"内存{predicted_memory:.2f}GB, 输出{predicted_tokens:.0f}tokens")

    # 4. 状态特征统计
    print(f"    状态特征统计:")
    print(f"      全局: min={state.min():.3f}, max={state.max():.3f}, "
          f"mean={state.mean():.3f}, std={state.std():.3f}")

    # 5. 就绪任务的状态特征
    if ready_indices:
        ready_states = state[:, ready_indices]
        print(f"      就绪任务: min={ready_states.min():.3f}, max={ready_states.max():.3f}, "
              f"mean={ready_states.mean():.3f}, std={ready_states.std():.3f}")

    # 6. 机器资源状态
    if hasattr(data_loader, '_step_state') and data_loader._step_state:
        memory_status = data_loader._step_state.get('memory_status', [])
        if memory_status:
            print(f"    机器内存状态: {[f'{mem:.1f}GB' for mem in memory_status]}")

    # 7. DAG进度
    if hasattr(data_loader, '_task_status'):
        completed_count = sum(1 for status in data_loader._task_status.values() if status == 3)
        total_tasks = len(data_loader._task_status)
        print(f"    DAG进度: {completed_count}/{total_tasks} ({completed_count/total_tasks:.1%}) 已完成")

def train_improved_xlstm(data_path, num_episodes=3000, save_interval=2000,
                        device_num=2, device='cuda'):
    """
    训练改进的xLSTM调度系统
    Args:
        data_path: 训练数据路径
        num_episodes: 训练回合数
        save_interval: 保存间隔
        device_num: 边缘设备数量
        device: 计算设备
    """
    print("🚀 开始训练改进的xLSTM调度系统")
    print("=" * 80)

    # 创建配置实例
    config = EnhancedLLMConfig()
    config.NUM_EDGE_SERVERS = device_num
    config.update_edge_servers(device_num)
    config.set_custom_data_path(data_path)

    print(f"📊 训练配置:")
    print(f"  数据路径: {data_path}")
    print(f"  训练回合数: {num_episodes}")
    print(f"  边缘设备数: {device_num}")
    print(f"  状态维度: {config.ENHANCED_STATE_DIM}")
    print(f"  序列长度: {config.SEQ_LEN}")
    print(f"  计算设备: {device}")

    # 创建数据加载器
    data_loader = EnhancedDataLoader(config, debug=True)

    # 创建改进的智能体
    agent = ImprovedXLSTMSACAgent(
        state_dim=config.ENHANCED_STATE_DIM,
        action_dim=2,  # [task_selection, machine_assignment]
        hidden_dim=256,
        seq_len=config.SEQ_LEN,
        num_machines=device_num + 1,  # 包括用户设备
        xlstm_layers=2,
        lr=3e-4,
        device=device
    )

    # 创建改进的奖励函数 - 启用步骤级奖励
    reward_function = ImprovedRewardFunction(
        max_makespan=200.0,
        completion_weight=50.0,     # 启用完成率奖励
        makespan_weight=20.0,       # 启用makespan奖励
        efficiency_weight=5.0,      # 启用效率奖励
        penalty_weight=30.0,        # 启用惩罚
        step_reward_scale=1.0,      # 启用步骤奖励
        task_completion_reward=5.0, # 任务完成奖励
        llm_edge_bonus=2.0,        # LLM边缘执行奖励
        memory_fail_penalty=10.0,   # 内存失败惩罚
        no_ready_penalty=2.0,       # 无就绪任务惩罚
        time_penalty_factor=0.02    # 时间惩罚因子 - 降低时间惩罚
    )

    print(f"\n🎯 完成率专注奖励函数:")
    print(f"   奖励 = 完成率 × 100")
    print(f"   完美完成额外奖励: +50")
    print(f"   步骤奖励: 关闭")
    print(f"   不考虑makespan: 专注完成率")

    # 创建保存目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_dir = f"models/improved_xlstm_sac_{device_num}devices_{timestamp}"
    os.makedirs(model_dir, exist_ok=True)

    # 训练历史记录
    episode_rewards = []
    episode_completion_rates = []
    episode_makespans = []
    episode_efficiency_scores = []
    successful_episodes = 0
    nan_count = 0
    start_time = time.time()

    # 记录失败DAG的信息
    failed_dags = []
    dag_analysis = {
        'total_episodes': 0,
        'successful_episodes': 0,
        'failed_episodes': 0,
        'completion_rates': [],
        'makespans': [],
        'failed_dag_ids': [],
        'memory_overflow_episodes': [],
        'deadlock_episodes': [],
        'high_parallelism_episodes': []  # 记录高并行度导致失败的episode
    }

    print(f"\n🎯 开始训练...")

    for episode in range(num_episodes):
        try:
            # 🎯 平衡采样策略：确保各难度DAG均匀分布
            if episode % 3 == 0:  # 1/3概率选择简单DAG
                dag_idx = np.random.randint(0, 67)  # 前1/3为简单
            elif episode % 3 == 1:  # 1/3概率选择中等DAG
                dag_idx = np.random.randint(67, 134)  # 中1/3为中等
            else:  # 1/3概率选择困难DAG
                dag_idx = np.random.randint(134, 200)  # 后1/3为困难

            # 🔍 记录DAG选择信息
            dag_difficulty = "简单" if dag_idx < 67 else ("中等" if dag_idx < 134 else "困难")

            # 加载DAG数据
            task_features = data_loader.load_task_features(dag_idx)
            adj_matrix = data_loader.load_adjacency_matrix(dag_idx)
            machine_resources = data_loader.load_machines_resource()
            comm_speed = data_loader.load_machine_commu_speed()
            memory_status = data_loader.load_memory_status()
            num_tasks = task_features.shape[1]

            # 初始化环境
            data_loader._init_step_state(task_features, adj_matrix, machine_resources, comm_speed, memory_status)

            # 获取初始状态
            state = data_loader.process_state_as_enhanced_sequence(
                task_features, adj_matrix, machine_resources, comm_speed, memory_status)

            episode_reward = 0
            episode_nan_count = 0
            step_count = 0
            max_steps = num_tasks * 2

            # 执行回合
            detailed_debug = (episode % 1 == 0)  # 每个episode都详细打印

            while step_count < max_steps:
                step_count += 1

                # 获取就绪任务掩码
                ready_tasks = data_loader.get_ready_tasks()
                if not ready_tasks:
                    if detailed_debug:
                        print(f"    步骤{step_count}: 没有就绪任务，回合结束")
                    break

                ready_mask = np.zeros(config.SEQ_LEN)
                for i, _ in enumerate(ready_tasks[:config.SEQ_LEN]):
                    if i < config.SEQ_LEN:
                        ready_mask[i] = 1.0

                # 打印详细的调度信息
                if detailed_debug:
                    print(f"\n  📋 步骤{step_count}调度信息:")
                    print(f"    就绪任务: {ready_tasks}")
                    print(f"    已完成任务: {len(data_loader._step_state.get('completed_tasks', set()))}/{num_tasks}")

                    # 打印内存状态
                    memory_status = data_loader._step_state.get('memory_status', [])
                    if memory_status is not None:
                        print(f"    内存状态:")
                        for i, mem in enumerate(memory_status):
                            machine_type = "用户设备" if i == 0 else f"边缘设备{i}"
                            print(f"      {machine_type}: {mem:.2f}GB 可用")

                    # 打印任务状态详情
                    print(f"    任务状态详情:")
                    for task_idx in range(num_tasks):  # 显示所有任务
                        status = data_loader._task_status.get(task_idx, 0)
                        status_names = ["未就绪", "就绪", "运行中", "已完成"]
                        predicted_memory = data_loader._task_predicted_memory.get(task_idx, 0)
                        predicted_tokens = data_loader._task_predicted_output_tokens.get(task_idx, 0)
                        is_llm = task_features[2, task_idx] == 1

                        print(f"      任务{task_idx}: {status_names[status]}, "
                              f"{'LLM' if is_llm else '普通'}, "
                              f"内存{predicted_memory:.2f}GB, "
                              f"输出{predicted_tokens:.0f}tokens")

                # 🔍 打印序列信息（每5个episode打印一次详细序列）
                if detailed_debug and episode % 5 == 0:
                    print(f"\n  📊 [序列信息] Episode {episode}, Step {step_count}:")
                    print(f"    状态序列形状: {state.shape}")
                    print(f"    就绪掩码: {ready_mask}")
                    print(f"    就绪任务索引: {[i for i, mask in enumerate(ready_mask) if mask == 1]}")

                    # 打印前5个任务的状态特征
                    print(f"    前5个任务的状态特征:")
                    for i in range(min(5, state.shape[1])):
                        task_state = state[:, i]
                        is_ready = ready_mask[i] == 1
                        print(f"      任务{i}: ready={is_ready}, 特征前10维={task_state[:10]}")

                    # 打印状态统计
                    print(f"    状态统计: min={state.min():.3f}, max={state.max():.3f}, "
                          f"mean={state.mean():.3f}, std={state.std():.3f}")

                # 🔬 打印更详细的序列分析（每20个episode打印一次）
                if detailed_debug and episode % 20 == 0:
                    analyze_sequence_details(state, ready_mask, task_features, data_loader, episode, step_count)

                # 转换为tensor
                state_tensor = torch.FloatTensor(state).unsqueeze(0).to(device)
                ready_mask_tensor = torch.FloatTensor(ready_mask).unsqueeze(0).to(device)

                # 🔍 打印网络输入信息
                if detailed_debug and episode % 5 == 0:
                    print(f"    网络输入张量形状: state={state_tensor.shape}, mask={ready_mask_tensor.shape}")

                # 选择动作
                action = agent.select_action(state_tensor, ready_mask_tensor, deterministic=False)

                # 检查NaN
                if np.isnan(action).any():
                    episode_nan_count += 1
                    action = np.array([0.0, 0.0])  # 使用默认动作
                    if detailed_debug:
                        print(f"    ⚠️  检测到NaN动作，使用默认动作")

                if detailed_debug:
                    print(f"    🎯 选择动作: 任务选择={action[0]:.3f}, 机器选择={action[1]:.3f}")

                # 执行动作
                try:
                    next_state, _, done, info = data_loader.step_enhanced_task(action, debug=detailed_debug)

                    # 打印执行结果
                    if detailed_debug:
                        executed_task = info.get('executed_task', -1)
                        if executed_task >= 0:
                            execution_time = info.get('execution_time', 0) / 1000.0
                            print(f"    ✅ 执行任务{executed_task}, 用时{execution_time:.3f}秒")

                            # 打印新增就绪任务
                            newly_ready = info.get('newly_ready_tasks', [])
                            # 安全处理newly_ready的布尔判断
                            if hasattr(newly_ready, '__len__') and len(newly_ready) > 0:
                                print(f"    🆕 新增就绪任务: {newly_ready}")

                        failed_task = info.get('failed_task', -1)
                        if failed_task >= 0:
                            reason = info.get('reason', 'unknown')
                            print(f"    ❌ 任务{failed_task}失败: {reason}")

                    # 构建步骤级奖励信息
                    executed_task = info.get('executed_task', -1)
                    failed_task = info.get('failed_task', -1)

                    step_info = {
                        'task_completed_successfully': executed_task >= 0,
                        'task_failed_memory': failed_task >= 0 and info.get('reason') == 'memory_insufficient',
                        'no_ready_tasks_but_not_done': len(ready_tasks) == 0 and not done,
                        'is_llm': False,
                        'assigned_machine_idx': 0,
                        'task_execution_time_sec': 0,
                        'predicted_memory': 0
                    }

                    # 如果任务成功执行，获取详细信息
                    if executed_task >= 0:
                        is_llm = task_features[2, executed_task] == 1
                        assigned_machine = info.get('assigned_machine', 0)
                        execution_time = info.get('execution_time', 0) / 1000.0  # 转换为秒
                        predicted_memory = data_loader._task_predicted_memory.get(executed_task, 0)

                        step_info.update({
                            'is_llm': is_llm,
                            'assigned_machine_idx': assigned_machine,
                            'task_execution_time_sec': execution_time,
                            'predicted_memory': predicted_memory
                        })

                        # 调试信息
                        if detailed_debug and is_llm:
                            machine_name = "用户设备" if assigned_machine == 0 else f"边缘设备{assigned_machine}"
                            print(f"    🔍 LLM任务{executed_task}分配信息: {machine_name} (机器{assigned_machine})")

                    improved_reward, reward_components = reward_function.calculate_reward(step_info)
                    episode_reward += improved_reward

                    # 安全处理improved_reward的比较
                    if detailed_debug and (np.isscalar(improved_reward) and improved_reward != 0):
                        print(f"    💰 步骤奖励: {improved_reward:.2f}")
                        for comp_name, comp_value in reward_components.items():
                            # 安全处理comp_value的比较
                            if np.isscalar(comp_value) and comp_value != 0:
                                print(f"      {comp_name}: {comp_value:.2f}")

                    if done:
                        # 回合结束，计算最终奖励
                        completed_tasks_data = info.get('completed_tasks', set())
                        # 安全处理completed_tasks数据
                        if hasattr(completed_tasks_data, '__len__'):
                            completed_tasks = len(completed_tasks_data)
                        else:
                            completed_tasks = 0

                        # 🔥 关键修复：获取正确的makespan（包含失败惩罚）
                        raw_makespan = data_loader._step_state.get('total_completion_time', 0) / 1000.0
                        # 使用info中的total_time，它已经包含了失败DAG的1000秒惩罚
                        makespan = info.get('total_time', raw_makespan * 1000) / 1000.0

                        episode_info = {
                            'total_tasks': num_tasks,
                            'completed_tasks': completed_tasks,
                            'makespan': makespan,
                            'failed_tasks': num_tasks - completed_tasks,
                            'deadlock_occurred': info.get('deadlock_detected', False)
                        }

                        final_reward, final_components = reward_function.calculate_reward({}, episode_info)
                        episode_reward += final_reward

                        if detailed_debug:
                            print(f"\n  🏁 回合结束:")
                            print(f"    完成任务: {completed_tasks}/{num_tasks} ({completed_tasks/num_tasks:.1%})")
                            print(f"    Makespan: {makespan:.2f}秒")
                            print(f"    最终奖励: {final_reward:.2f}")
                            print(f"    总奖励: {episode_reward:.2f}")
                            for comp_name, comp_value in final_components.items():
                                # 安全处理comp_value的比较
                                if np.isscalar(comp_value) and comp_value != 0:
                                    print(f"      {comp_name}: {comp_value:.2f}")
                        break

                    state = next_state

                except Exception as e:
                    # 处理执行错误
                    if detailed_debug:
                        print(f"    ❌ 执行错误: {e}")
                    episode_reward -= 1.0
                    break

            # 记录episode结果
            if 'info' in locals() and info is not None:
                completed_tasks_data = info.get('completed_tasks', set())
                # 处理不同类型的completed_tasks数据
                if hasattr(completed_tasks_data, '__len__'):
                    completed_tasks = len(completed_tasks_data)
                else:
                    completed_tasks = 0
            else:
                completed_tasks = 0
            completion_rate = completed_tasks / num_tasks

            # 🔍 分析失败原因和DAG特征
            dag_analysis['total_episodes'] += 1
            dag_analysis['completion_rates'].append(completion_rate)

            # 分析DAG并行度和资源需求
            max_ready_tasks = 0
            total_memory_demand = 0
            llm_task_count = 0

            # 分析任务特征
            for task_idx in range(num_tasks):
                is_llm = task_features[2, task_idx] == 1
                predicted_memory = data_loader._task_predicted_memory.get(task_idx, 0)
                total_memory_demand += predicted_memory
                if is_llm:
                    llm_task_count += 1

            # 估算最大并行度
            try:
                initial_ready_count = len(data_loader.get_ready_tasks()) if hasattr(data_loader, 'get_ready_tasks') else 1
                max_ready_tasks = max(max_ready_tasks, initial_ready_count)
            except:
                max_ready_tasks = 1

            # 记录DAG分析信息
            dag_analysis['makespans'].append(makespan if completion_rate >= 1.0 else 0.0)

            # 分析失败原因
            if completion_rate < 1.0:
                dag_analysis['failed_episodes'] += 1
                dag_analysis['failed_dag_ids'].append(dag_idx)

                # 记录详细失败信息
                failure_info = {
                    'episode': episode,
                    'dag_id': dag_idx,
                    'dag_difficulty': dag_difficulty,
                    'completion_rate': completion_rate,
                    'completed_tasks': completed_tasks,
                    'total_tasks': num_tasks,
                    'failed_tasks': num_tasks - completed_tasks,
                    'max_ready_tasks': max_ready_tasks,
                    'total_memory_demand': total_memory_demand,
                    'llm_task_count': llm_task_count,
                    'llm_ratio': llm_task_count / num_tasks,
                    'avg_memory_per_task': total_memory_demand / num_tasks,
                    'deadlock_detected': info.get('deadlock_detected', False) if 'info' in locals() else False,
                    'memory_overflow': False,  # 将在下面检测
                    'high_parallelism': max_ready_tasks > (device_num + 1),  # 就绪任务数超过机器数
                    'reason': 'unknown'
                }

                # 检测内存溢出
                if 'info' in locals() and info is not None:
                    failed_task = info.get('failed_task', -1)
                    if failed_task >= 0 and info.get('reason') == 'memory_insufficient':
                        failure_info['memory_overflow'] = True
                        failure_info['reason'] = 'memory_insufficient'
                        dag_analysis['memory_overflow_episodes'].append(episode)

                # 检测死锁
                if failure_info['deadlock_detected']:
                    failure_info['reason'] = 'deadlock'
                    dag_analysis['deadlock_episodes'].append(episode)

                # 检测高并行度问题
                if failure_info['high_parallelism']:
                    failure_info['reason'] = 'high_parallelism'
                    dag_analysis['high_parallelism_episodes'].append(episode)

                failed_dags.append(failure_info)

                # 打印失败分析
                if detailed_debug or episode % 50 == 0:
                    print(f"\n❌ [失败分析] Episode {episode}, DAG {dag_idx}:")
                    print(f"    完成率: {completion_rate:.1%} ({completed_tasks}/{num_tasks})")
                    print(f"    失败原因: {failure_info['reason']}")
                    print(f"    最大并行度: {max_ready_tasks} (机器数: {device_num + 1})")
                    print(f"    总内存需求: {total_memory_demand:.2f}GB")
                    print(f"    LLM任务比例: {failure_info['llm_ratio']:.1%}")
                    print(f"    平均任务内存: {failure_info['avg_memory_per_task']:.2f}GB")
            else:
                dag_analysis['successful_episodes'] += 1

            # �🔥 关键修复：获取正确的makespan（包含失败惩罚）
            if 'info' in locals() and info is not None:
                # 使用info中的total_time，它已经包含了失败DAG的1000秒惩罚
                makespan = info.get('total_time', 0) / 1000.0
            elif data_loader._step_state is not None and 'total_completion_time' in data_loader._step_state:
                makespan = data_loader._step_state['total_completion_time'] / 1000.0
            else:
                makespan = 0.0

            # 🔥 关键修复：只有100%完成的DAG才参与最佳makespan统计
            # 但所有DAG的makespan都要记录（包括失败DAG的1000秒惩罚）
            makespan_for_stats = makespan if completion_rate >= 1.0 else 0.0

            # 计算效率分数（只有完成率100%时才有意义）
            if completion_rate >= 1.0 and makespan > 0:
                efficiency_score = completed_tasks / makespan
            else:
                efficiency_score = 0.0

            episode_rewards.append(episode_reward)
            episode_completion_rates.append(completion_rate)
            episode_makespans.append(makespan_for_stats)  # 使用修正后的makespan
            episode_efficiency_scores.append(efficiency_score)

            if completion_rate >= 0.8:
                successful_episodes += 1

            nan_count += episode_nan_count

            # 计算移动平均
            window_size = min(20, len(episode_rewards))
            avg_reward = np.mean(episode_rewards[-window_size:])
            avg_completion = np.mean(episode_completion_rates[-window_size:])
            avg_makespan = np.mean(episode_makespans[-window_size:])
            avg_efficiency = np.mean(episode_efficiency_scores[-window_size:])

            # 打印进度 - 修改为每10个episode打印，确保能看到序列信息
            if episode % 10 == 0:
                print(f"Episode {episode:4d}: DAG {dag_idx}({dag_difficulty}), Reward={episode_reward:8.2f}, "
                      f"Completion={completion_rate:.2f}, Makespan={makespan:.2f}s, "
                      f"Efficiency={efficiency_score:.3f}, "
                      f"Avg20_Reward={avg_reward:8.2f}, Avg20_Completion={avg_completion:.2f}, "
                      f"Avg20_Makespan={avg_makespan:.2f}s, Avg20_Efficiency={avg_efficiency:.3f}, "
                      f"NaN_Count={episode_nan_count}, Time={time.time()-start_time:.2f}s")

            # 定期保存模型和结果
            if (episode + 1) % save_interval == 0:
                print(f"\n💾 保存中间结果 (Episode {episode + 1})...")

                # 保存模型
                intermediate_model_dir = f"{model_dir}/checkpoint_{episode + 1}"
                os.makedirs(intermediate_model_dir, exist_ok=True)
                agent.save_models(f"{intermediate_model_dir}/improved_xlstm_sac_model.pth")

                # 保存训练历史数据
                history_df = pd.DataFrame({
                    'Episode': range(1, len(episode_rewards) + 1),
                    'Reward': episode_rewards,
                    'Completion_Ratio': episode_completion_rates,
                    'Makespan': episode_makespans,
                    'Efficiency_Score': episode_efficiency_scores
                })
                history_df.to_excel(f"{intermediate_model_dir}/training_history.xlsx", index=False)

                # 保存失败DAG分析
                if failed_dags:
                    failed_df = pd.DataFrame(failed_dags)
                    failed_df.to_excel(f"{intermediate_model_dir}/failed_dags_analysis.xlsx", index=False)

                    # 保存DAG分析摘要
                    analysis_summary = {
                        'total_episodes': dag_analysis['total_episodes'],
                        'successful_episodes': dag_analysis['successful_episodes'],
                        'failed_episodes': dag_analysis['failed_episodes'],
                        'success_rate': dag_analysis['successful_episodes'] / dag_analysis['total_episodes'] * 100,
                        'memory_overflow_count': len(dag_analysis['memory_overflow_episodes']),
                        'deadlock_count': len(dag_analysis['deadlock_episodes']),
                        'high_parallelism_count': len(dag_analysis['high_parallelism_episodes']),
                        'avg_completion_rate': np.mean(dag_analysis['completion_rates']),
                        'failed_dag_ids': dag_analysis['failed_dag_ids']
                    }

                    analysis_df = pd.DataFrame([analysis_summary])
                    analysis_df.to_excel(f"{intermediate_model_dir}/dag_analysis_summary.xlsx", index=False)

                    print(f"📊 失败DAG分析:")
                    print(f"    总episode数: {analysis_summary['total_episodes']}")
                    print(f"    成功率: {analysis_summary['success_rate']:.1f}%")
                    print(f"    内存溢出: {analysis_summary['memory_overflow_count']}次")
                    print(f"    死锁: {analysis_summary['deadlock_count']}次")
                    print(f"    高并行度: {analysis_summary['high_parallelism_count']}次")

                # 绘制训练曲线
                if len(episode_rewards) >= 50:
                    plot_training_history(episode_rewards, episode_completion_rates,
                                        episode_makespans, episode_efficiency_scores,
                                        intermediate_model_dir)

                print(f"✅ 中间结果已保存到: {intermediate_model_dir}")

        except Exception as e:
            print(f"❌ Episode {episode} 训练失败: {e}")
            episode_rewards.append(-10.0)
            episode_completion_rates.append(0.0)
            episode_makespans.append(0.0)
            episode_efficiency_scores.append(0.0)
            continue

    # 保存最终模型和结果
    final_model_dir = f"{model_dir}/final"
    os.makedirs(final_model_dir, exist_ok=True)

    print(f"\n💾 保存最终模型...")
    agent.save_models(f"{final_model_dir}/improved_xlstm_sac_model.pth")

    # 保存完整训练历史数据
    if episode_rewards:
        final_history_df = pd.DataFrame({
            'Episode': range(1, len(episode_rewards) + 1),
            'Reward': episode_rewards,
            'Completion_Ratio': episode_completion_rates,
            'Makespan': episode_makespans,
            'Efficiency_Score': episode_efficiency_scores
        })
        final_history_df.to_excel(f"{final_model_dir}/final_training_history.xlsx", index=False)

        # 绘制最终训练曲线（只有足够数据时才绘制）
        if len(episode_rewards) >= 10:
            plot_training_history(episode_rewards, episode_completion_rates,
                                episode_makespans, episode_efficiency_scores,
                                final_model_dir)

        # 保存训练统计摘要
        summary_stats = {
            'Total_Episodes': len(episode_rewards),
            'Successful_Episodes': successful_episodes,
            'Average_Reward': np.mean(episode_rewards),
            'Average_Completion_Rate': np.mean(episode_completion_rates),
            'Average_Makespan': np.mean(episode_makespans),
            'Average_Efficiency': np.mean(episode_efficiency_scores),
            'Total_NaN_Count': nan_count,
            'Success_Rate': successful_episodes/len(episode_rewards)*100,
            'Final_Reward': episode_rewards[-1] if episode_rewards else 0,
            'Final_Completion_Rate': episode_completion_rates[-1] if episode_completion_rates else 0,
            'Final_Makespan': episode_makespans[-1] if episode_makespans else 0,
            'Final_Efficiency': episode_efficiency_scores[-1] if episode_efficiency_scores else 0
        }

        summary_df = pd.DataFrame([summary_stats])
        summary_df.to_excel(f"{final_model_dir}/training_summary.xlsx", index=False)

        # 保存最终失败DAG分析
        if failed_dags:
            final_failed_df = pd.DataFrame(failed_dags)
            final_failed_df.to_excel(f"{final_model_dir}/final_failed_dags_analysis.xlsx", index=False)

            # 保存最终DAG分析摘要
            final_analysis_summary = {
                'total_episodes': dag_analysis['total_episodes'],
                'successful_episodes': dag_analysis['successful_episodes'],
                'failed_episodes': dag_analysis['failed_episodes'],
                'success_rate': dag_analysis['successful_episodes'] / dag_analysis['total_episodes'] * 100 if dag_analysis['total_episodes'] > 0 else 0,
                'memory_overflow_count': len(dag_analysis['memory_overflow_episodes']),
                'deadlock_count': len(dag_analysis['deadlock_episodes']),
                'high_parallelism_count': len(dag_analysis['high_parallelism_episodes']),
                'avg_completion_rate': np.mean(dag_analysis['completion_rates']) if dag_analysis['completion_rates'] else 0,
                'unique_failed_dag_ids': list(set(dag_analysis['failed_dag_ids'])),
                'failed_dag_count': len(set(dag_analysis['failed_dag_ids']))
            }

            final_analysis_df = pd.DataFrame([final_analysis_summary])
            final_analysis_df.to_excel(f"{final_model_dir}/final_dag_analysis_summary.xlsx", index=False)

        print(f"\n📊 训练完成统计:")
        print(f"  总回合数: {len(episode_rewards)}")
        print(f"  成功回合数: {successful_episodes}")
        print(f"  平均奖励: {np.mean(episode_rewards):.2f}")
        print(f"  平均完成率: {np.mean(episode_completion_rates):.2%}")
        print(f"  平均Makespan: {np.mean(episode_makespans):.2f}秒")
        print(f"  平均效率: {np.mean(episode_efficiency_scores):.3f}")
        print(f"  成功率: {successful_episodes/len(episode_rewards)*100:.1f}%")

        # 打印失败DAG分析摘要
        if failed_dags:
            print(f"\n🔍 失败DAG分析摘要:")
            print(f"  失败episode数: {final_analysis_summary['failed_episodes']}")
            print(f"  唯一失败DAG数: {final_analysis_summary['failed_dag_count']}")
            print(f"  内存溢出次数: {final_analysis_summary['memory_overflow_count']}")
            print(f"  死锁次数: {final_analysis_summary['deadlock_count']}")
            print(f"  高并行度失败次数: {final_analysis_summary['high_parallelism_count']}")
            print(f"  失败DAG ID列表: {final_analysis_summary['unique_failed_dag_ids'][:10]}{'...' if len(final_analysis_summary['unique_failed_dag_ids']) > 10 else ''}")

            # 详细输出每个失败的DAG信息
            print(f"\n❌ 失败DAG详细信息:")
            print("=" * 80)
            for idx, dag_info in enumerate(failed_dags):
                print(f"\n失败DAG #{idx+1}:")
                print(f"  Episode: {dag_info['episode']}")
                print(f"  DAG ID: {dag_info['dag_id']}")
                print(f"  完成率: {dag_info['completion_rate']:.1%} ({dag_info['completed_tasks']}/{dag_info['total_tasks']})")
                print(f"  失败任务数: {dag_info['failed_tasks']}")
                print(f"  失败原因: {dag_info['reason']}")
                print(f"  最大并行度: {dag_info['max_ready_tasks']} (机器数: {device_num + 1})")
                print(f"  总内存需求: {dag_info['total_memory_demand']:.2f}GB")
                print(f"  LLM任务数: {dag_info['llm_task_count']} ({dag_info['llm_ratio']:.1%})")
                print(f"  平均任务内存: {dag_info['avg_memory_per_task']:.2f}GB")

                # 问题标识
                issues = []
                if dag_info['memory_overflow']:
                    issues.append("内存溢出")
                if dag_info['deadlock_detected']:
                    issues.append("死锁")
                if dag_info['high_parallelism']:
                    issues.append("高并行度")

                if issues:
                    print(f"  检测到的问题: {', '.join(issues)}")
                else:
                    print(f"  检测到的问题: 无明确问题标识")

                # 可能的失败原因分析
                if dag_info['completion_rate'] == 0.0:
                    print(f"  分析: 完全失败，可能是代码错误或极难调度的DAG")
                elif dag_info['completion_rate'] < 0.5:
                    print(f"  分析: 严重失败，可能是资源不足或调度策略问题")
                else:
                    print(f"  分析: 部分失败，可能是个别任务调度困难")

            print("=" * 80)

    print(f"🏆 最终模型和训练数据已保存到: {final_model_dir}")

    return agent, episode_rewards, episode_completion_rates, episode_makespans, episode_efficiency_scores

def plot_training_history(rewards, completion_ratios, makespans, efficiency_scores, save_dir):
    """绘制训练历史曲线"""

    def moving_average(data, window_size):
        return np.convolve(data, np.ones(window_size)/window_size, mode='valid')

    window_size = min(50, len(rewards) // 10)
    if window_size < 2:
        window_size = 2

    # 计算平滑曲线
    rewards_smooth = moving_average(rewards, window_size)
    completion_smooth = moving_average(completion_ratios, window_size)
    makespans_smooth = moving_average(makespans, window_size)
    efficiency_smooth = moving_average(efficiency_scores, window_size)

    # 设置绘图样式
    plt.style.use('ggplot')
    plt.rcParams['font.family'] = 'DejaVu Sans'
    plt.rcParams['font.size'] = 10

    # 创建2x2子图
    _, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # 奖励曲线
    ax1.plot(rewards, alpha=0.3, color='blue', label='Raw Rewards')
    ax1.plot(range(window_size-1, len(rewards)), rewards_smooth, color='blue', linewidth=2,
             label=f'Smoothed Rewards (window={window_size})')
    ax1.set_xlabel('Episode')
    ax1.set_ylabel('Reward')
    ax1.set_title('Improved xLSTM SAC Training Rewards')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 完成率曲线
    ax2.plot(completion_ratios, alpha=0.3, color='green', label='Raw Completion Rate')
    ax2.plot(range(window_size-1, len(completion_ratios)), completion_smooth, color='green', linewidth=2,
             label=f'Smoothed Completion Rate (window={window_size})')
    ax2.set_xlabel('Episode')
    ax2.set_ylabel('Completion Rate')
    ax2.set_title('Improved xLSTM SAC Task Completion Rate')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(0, 1.1)
    ax2.set_yticks(np.arange(0, 1.1, 0.1))
    ax2.set_yticklabels([f'{int(x*100)}%' for x in np.arange(0, 1.1, 0.1)])

    # Makespan曲线
    ax3.plot(makespans, alpha=0.3, color='red', label='Raw Makespan')
    ax3.plot(range(window_size-1, len(makespans)), makespans_smooth, color='red', linewidth=2,
             label=f'Smoothed Makespan (window={window_size})')
    ax3.set_xlabel('Episode')
    ax3.set_ylabel('Makespan (seconds)')
    ax3.set_title('Improved xLSTM SAC Makespan (DAG Completion Time)')
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # 效率曲线
    ax4.plot(efficiency_scores, alpha=0.3, color='orange', label='Raw Efficiency')
    ax4.plot(range(window_size-1, len(efficiency_scores)), efficiency_smooth, color='orange', linewidth=2,
             label=f'Smoothed Efficiency (window={window_size})')
    ax4.set_xlabel('Episode')
    ax4.set_ylabel('Efficiency (tasks/second)')
    ax4.set_title('Improved xLSTM SAC Efficiency Score')
    ax4.legend()
    ax4.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(f"{save_dir}/improved_xlstm_training_history.png", dpi=300, bbox_inches='tight')
    plt.close()

    # 保存数据为Excel格式
    plot_data = pd.DataFrame({
        'Episode': range(1, len(rewards) + 1),
        'Reward': rewards,
        'Reward_Smoothed': np.append(np.full(window_size-1, np.nan), rewards_smooth),
        'Completion_Ratio': completion_ratios,
        'Completion_Ratio_Smoothed': np.append(np.full(window_size-1, np.nan), completion_smooth),
        'Makespan': makespans,
        'Makespan_Smoothed': np.append(np.full(window_size-1, np.nan), makespans_smooth),
        'Efficiency_Score': efficiency_scores,
        'Efficiency_Score_Smoothed': np.append(np.full(window_size-1, np.nan), efficiency_smooth)
    })
    plot_data.to_excel(f"{save_dir}/training_plot_data.xlsx", index=False)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='训练改进的xLSTM调度系统')
    parser.add_argument('--data_path', type=str,
                       default='../dataset/training/DAG_200_edges_2_mem_32GB_density_0.4_0.6',
                       help='训练数据路径')
    parser.add_argument('--episodes', type=int, default=3000, help='训练回合数')
    parser.add_argument('--save_interval', type=int, default=1000, help='保存间隔')
    parser.add_argument('--device_num', type=int, default=2, help='边缘设备数量')
    parser.add_argument('--device', type=str, default='cuda', help='计算设备')

    args = parser.parse_args()

    print("🚀 启动改进的xLSTM调度系统训练")
    print(f"参数: {args}")

    try:
        result = train_improved_xlstm(
            data_path=args.data_path,
            num_episodes=args.episodes,
            save_interval=args.save_interval,
            device_num=args.device_num,
            device=args.device
        )

        _, rewards, completion_rates, makespans, efficiency_scores = result
        print("🎉 改进的xLSTM版训练成功完成!")

        # 显示最终统计
        if rewards:
            print(f"\n📊 最终训练统计:")
            print(f"  总回合数: {len(rewards)}")
            print(f"  平均奖励: {np.mean(rewards):.2f}")
            print(f"  最终奖励: {rewards[-1]:.2f}")
            print(f"  平均完成率: {np.mean(completion_rates):.2%}")
            print(f"  最终完成率: {completion_rates[-1]:.2%}")
            print(f"  最高完成率: {np.max(completion_rates):.2%}")
            print(f"  平均Makespan: {np.mean(makespans):.2f}秒")
            print(f"  最终Makespan: {makespans[-1]:.2f}秒")
            # 只统计100%完成DAG的makespan
            valid_makespans = [m for m in makespans if m > 0]
            if valid_makespans:
                print(f"  最佳Makespan: {np.min(valid_makespans):.2f}秒 (仅统计100%完成的DAG)")
            else:
                print(f"  最佳Makespan: 无 (没有100%完成的DAG)")
            print(f"  平均效率: {np.mean(efficiency_scores):.3f}")
            print(f"  最终效率: {efficiency_scores[-1]:.3f}")

    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
