#!/usr/bin/env python3
"""
改进的稳定性训练脚本
解决Makespan波动问题的多维度策略
"""

import os
import sys
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
import argparse
import random
from datetime import datetime
from collections import defaultdict, deque

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 添加路径
sys.path.append('..')
sys.path.append('.')

from simplified_data_loader import SimplifiedDataLoader
from simplified_config import SimplifiedConfig
from simplified_event_system import SimplifiedSchedulingEnvironment
from simplified_sac_agent import SimplifiedSACAgent

class ImprovedStabilityTrainer:
    def __init__(self, config):
        self.config = config
        self.data_loader = SimplifiedDataLoader(config)
        self.env = SimplifiedSchedulingEnvironment(config)
        self.agent = SimplifiedSACAgent(config, device='cuda')

        # 稳定性改进参数
        self.exploration_strategies = {
            'forced_diversity': True,    # 强制探索不同机器
            'epsilon_greedy': True,      # epsilon-greedy探索
            'machine_rotation': True,    # 机器轮换策略
            'adaptive_sampling': True    # 自适应DAG采样
        }

        # 性能跟踪
        self.makespan_history = deque(maxlen=50)  # 最近50个episode的makespan
        self.dag_performance = defaultdict(list)   # 每个DAG的性能记录
        self.machine_usage = defaultdict(int)      # 机器使用统计

        # 奖励函数改进
        self.reward_weights = {
            'completion': 10.0,      # 完成奖励权重
            'makespan': 5.0,         # makespan奖励权重
            'stability': 2.0,        # 稳定性奖励权重
            'exploration': 1.0       # 探索奖励权重
        }

    def enhanced_select_action(self, state, episode, deterministic=False):
        """
        增强的动作选择策略
        """
        # 基础动作选择
        if not isinstance(state, torch.Tensor):
            state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.agent.device)
        else:
            state_tensor = state

        with torch.no_grad():
            if deterministic:
                logits = self.agent.actor(state_tensor)
                action = torch.argmax(logits, dim=-1)
            else:
                # 获取原始logits
                logits = self.agent.actor(state_tensor)

                # 应用探索策略
                modified_logits = self._apply_exploration_strategies(logits, episode)

                # 采样动作
                probs = torch.softmax(modified_logits, dim=-1)
                action_dist = torch.distributions.Categorical(probs)
                action = action_dist.sample()

        return action.cpu().numpy()[0]

    def _apply_exploration_strategies(self, logits, episode):
        """应用多种探索策略"""
        modified_logits = logits.clone()

        # 1. 强制探索不同机器 (前50个episode)
        if self.exploration_strategies['forced_diversity'] and episode < 50:
            exploration_noise = torch.randn_like(modified_logits) * 0.3
            modified_logits = modified_logits + exploration_noise

        # 2. Epsilon-greedy探索 (前100个episode)
        if self.exploration_strategies['epsilon_greedy'] and episode < 100:
            epsilon = max(0.1, 1.0 - episode / 100.0)
            if np.random.random() < epsilon:
                # 随机选择，但偏向使用较少的机器
                machine_counts = np.array([self.machine_usage[i] for i in range(logits.shape[1])])
                # 反向权重：使用少的机器权重高
                inverse_weights = 1.0 / (machine_counts + 1)
                inverse_weights = inverse_weights / np.sum(inverse_weights)

                # 应用权重
                weight_tensor = torch.FloatTensor(inverse_weights).to(logits.device)
                modified_logits = modified_logits + torch.log(weight_tensor + 1e-8)

        # 3. 机器轮换策略 (前80个episode)
        if self.exploration_strategies['machine_rotation'] and episode < 80:
            preferred_machine = episode % logits.shape[1]
            modified_logits[0, preferred_machine] += 1.0

        return modified_logits

    def adaptive_dag_sampling(self, episode, total_episodes):
        """
        自适应DAG采样策略
        根据DAG的学习难度调整采样频率
        """
        if not self.exploration_strategies['adaptive_sampling']:
            return np.random.randint(0, 200)  # 随机采样

        # 计算每个DAG的平均性能
        dag_difficulties = {}
        for dag_id, performances in self.dag_performance.items():
            if len(performances) > 0:
                avg_makespan = np.mean([p['makespan'] for p in performances if p['makespan'] < 1500])
                completion_rate = np.mean([p['completion_rate'] for p in performances])
                # 难度分数：makespan高且完成率低的DAG更难
                difficulty = avg_makespan * (2.0 - completion_rate)
                dag_difficulties[dag_id] = difficulty

        if len(dag_difficulties) < 10:
            return np.random.randint(0, 200)  # 数据不足时随机采样

        # 根据训练阶段调整采样策略
        if episode < total_episodes * 0.3:
            # 前30%：主要训练简单DAG
            easy_dags = sorted(dag_difficulties.items(), key=lambda x: x[1])[:50]
            return np.random.choice([dag_id for dag_id, _ in easy_dags])
        elif episode < total_episodes * 0.7:
            # 中间40%：混合训练
            return np.random.randint(0, 200)
        else:
            # 后30%：重点训练困难DAG
            hard_dags = sorted(dag_difficulties.items(), key=lambda x: x[1], reverse=True)[:50]
            return np.random.choice([dag_id for dag_id, _ in hard_dags])

    def compute_enhanced_reward(self, info, episode):
        """
        计算增强的奖励函数
        """
        completion_rate = info.get('completion_rate', 0.0)
        makespan = info.get('makespan', 1000.0)

        # 基础奖励
        completion_reward = completion_rate * self.reward_weights['completion']

        # Makespan奖励 (归一化)
        if completion_rate >= 0.8:
            # 只有成功完成时才考虑makespan
            normalized_makespan = min(makespan / 1000.0, 2.0)  # 归一化到[0, 2]
            makespan_reward = (2.0 - normalized_makespan) * self.reward_weights['makespan']
        else:
            makespan_reward = 0.0

        # 稳定性奖励
        stability_reward = 0.0
        if len(self.makespan_history) >= 10:
            recent_makespans = [m for m in self.makespan_history if m < 1500]
            if len(recent_makespans) >= 5:
                stability = 1.0 / (1.0 + np.std(recent_makespans) / 100.0)
                stability_reward = stability * self.reward_weights['stability']

        # 探索奖励 (鼓励使用不同机器)
        exploration_reward = 0.0
        if episode < 100:
            machine_usage_variance = np.var(list(self.machine_usage.values()))
            exploration_reward = (1.0 / (1.0 + machine_usage_variance)) * self.reward_weights['exploration']

        total_reward = completion_reward + makespan_reward + stability_reward + exploration_reward

        return total_reward, {
            'completion_reward': completion_reward,
            'makespan_reward': makespan_reward,
            'stability_reward': stability_reward,
            'exploration_reward': exploration_reward
        }

    def train(self, num_episodes=1000, save_interval=250, device_num=5):
        """主训练循环"""
        print("🚀 开始改进稳定性训练")
        print("=" * 80)
        print("📋 改进策略:")
        print("  ✅ 多种探索策略 (强制探索、epsilon-greedy、机器轮换)")
        print("  ✅ 自适应DAG采样 (根据难度调整)")
        print("  ✅ 增强奖励函数 (完成率+makespan+稳定性+探索)")
        print("  ✅ 性能跟踪和分析")
        print("=" * 80)

        # 创建保存目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_dir = f"models/improved_stability_{device_num}devices_{timestamp}"
        os.makedirs(model_dir, exist_ok=True)

        # 训练记录
        episode_rewards = []
        episode_completion_rates = []
        episode_makespans = []
        episode_dag_ids = []
        reward_components = []

        successful_episodes = 0
        start_time = time.time()

        for episode in range(num_episodes):
            try:
                # 自适应DAG采样
                dag_idx = self.adaptive_dag_sampling(episode, num_episodes)

                # 加载数据
                task_features = self.data_loader.load_task_features(dag_idx)
                adj_matrix = self.data_loader.load_adjacency_matrix(dag_idx)
                machine_resources = self.data_loader.load_memory_status()
                num_tasks = task_features.shape[1]

                # 重置环境
                state = self.env.reset(task_features, adj_matrix, machine_resources)

                episode_reward = 0
                step_count = 0
                max_steps = num_tasks * 3

                # 执行episode
                while step_count < max_steps:
                    step_count += 1

                    # 增强动作选择
                    action = self.enhanced_select_action(state, episode, deterministic=False)

                    # 记录机器使用
                    self.machine_usage[action] += 1

                    # 执行动作
                    next_state, base_reward, done, info = self.env.step(action)

                    # 计算增强奖励
                    enhanced_reward, reward_breakdown = self.compute_enhanced_reward(info, episode)

                    # 存储经验
                    self.agent.store_transition(state, action, enhanced_reward, next_state, done)

                    # 更新状态和奖励
                    state = next_state
                    episode_reward += enhanced_reward

                    # 训练智能体
                    if len(self.agent.replay_buffer) > self.agent.batch_size:
                        self.agent.update()

                    if done:
                        break

                # 记录结果
                completion_rate = info.get('completion_rate', 0.0)
                makespan = info.get('makespan', 1000.0)

                episode_rewards.append(episode_reward)
                episode_completion_rates.append(completion_rate)
                episode_makespans.append(makespan if completion_rate >= 0.8 else 1000.0)
                episode_dag_ids.append(dag_idx)
                reward_components.append(reward_breakdown)

                # 更新性能跟踪
                if completion_rate >= 0.8:
                    self.makespan_history.append(makespan)
                    successful_episodes += 1

                self.dag_performance[dag_idx].append({
                    'completion_rate': completion_rate,
                    'makespan': makespan,
                    'episode': episode
                })

                # 打印进度
                if (episode + 1) % 20 == 0 or episode < 10:
                    recent_completion = np.mean(episode_completion_rates[-20:])
                    recent_makespan = np.mean([m for m in episode_makespans[-20:] if m < 1000])
                    success_rate = successful_episodes / (episode + 1)

                    # 计算稳定性指标
                    stability_score = 0.0
                    if len(self.makespan_history) >= 10:
                        recent_makespans = [m for m in self.makespan_history if m < 1500]
                        if len(recent_makespans) >= 5:
                            stability_score = 1.0 / (1.0 + np.std(recent_makespans) / 100.0)

                    print(f"Episode {episode+1:4d} | "
                          f"DAG {dag_idx:2d} | "
                          f"完成率: {completion_rate:.1%} (平均: {recent_completion:.1%}) | "
                          f"Makespan: {makespan:6.1f}s (平均: {recent_makespan:6.1f}s) | "
                          f"稳定性: {stability_score:.3f} | "
                          f"成功率: {success_rate:.1%}")

                # 保存模型
                if (episode + 1) % save_interval == 0:
                    checkpoint_dir = f"{model_dir}/checkpoint_{episode+1}"
                    os.makedirs(checkpoint_dir, exist_ok=True)
                    self.agent.save_models(f"{checkpoint_dir}/agent.pth")
                    print(f"💾 已保存检查点: {checkpoint_dir}")

            except Exception as e:
                print(f"❌ Episode {episode} 出错: {e}")
                continue

        # 保存最终模型
        final_dir = f"{model_dir}/final"
        os.makedirs(final_dir, exist_ok=True)
        self.agent.save_models(f"{final_dir}/agent.pth")

        # 分析和可视化结果
        self._analyze_and_visualize_results(
            episode_rewards, episode_completion_rates, episode_makespans,
            episode_dag_ids, reward_components, model_dir
        )

        return {
            'rewards': episode_rewards,
            'completion_rates': episode_completion_rates,
            'makespans': episode_makespans,
            'dag_ids': episode_dag_ids,
            'success_rate': successful_episodes / num_episodes,
            'model_dir': model_dir
        }

    def _analyze_and_visualize_results(self, episode_rewards, episode_completion_rates,
                                     episode_makespans, episode_dag_ids, reward_components, model_dir):
        """分析和可视化训练结果"""
        print(f"\n📈 训练完成分析:")

        successful_episodes = sum(1 for cr in episode_completion_rates if cr >= 0.8)
        print(f"  成功率: {successful_episodes/len(episode_completion_rates):.1%}")

        # 成功episode的makespan统计
        successful_makespans = [m for m, cr in zip(episode_makespans, episode_completion_rates)
                               if cr >= 0.8 and m < 1000]
        if successful_makespans:
            print(f"  最佳Makespan: {min(successful_makespans):.2f}秒")
            print(f"  平均Makespan: {np.mean(successful_makespans):.2f}秒")
            print(f"  Makespan标准差: {np.std(successful_makespans):.2f}秒")

        # 机器使用分析
        print(f"\n📊 机器使用统计:")
        total_usage = sum(self.machine_usage.values())
        for machine_id, usage in self.machine_usage.items():
            percentage = usage / total_usage * 100 if total_usage > 0 else 0
            print(f"  机器 {machine_id}: {usage} 次 ({percentage:.1f}%)")

        # 保存详细统计
        stats_df = pd.DataFrame({
            'Episode': range(len(episode_rewards)),
            'Reward': episode_rewards,
            'Completion_Rate': episode_completion_rates,
            'Makespan': episode_makespans,
            'DAG_ID': episode_dag_ids
        })
        stats_df.to_csv(f'{model_dir}/training_stats.csv', index=False)

        print(f"\n💾 训练统计已保存到: {model_dir}/training_stats.csv")
        print(f"🎉 改进稳定性训练完成!")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='改进稳定性训练')
    parser.add_argument('--episodes', type=int, default=500, help='训练回合数')
    parser.add_argument('--device_num', type=int, default=5, help='边缘设备数量')
    parser.add_argument('--data_path', type=str,
                       default='../dataset/training/DAG_30_edges_5_mem_57-64GB_density_0.2_0.6',
                       help='训练数据路径')
    parser.add_argument('--save_interval', type=int, default=250, help='保存间隔')

    args = parser.parse_args()

    # 创建配置
    config = SimplifiedConfig()
    config.update_edge_servers(args.device_num)
    config.set_data_path(args.data_path)

    print(f"🎯 改进稳定性训练参数:")
    print(f"  回合数: {args.episodes}")
    print(f"  设备数: {args.device_num}")
    print(f"  数据路径: {args.data_path}")

    # 开始训练
    trainer = ImprovedStabilityTrainer(config)
    results = trainer.train(
        num_episodes=args.episodes,
        save_interval=args.save_interval,
        device_num=args.device_num
    )

    print(f"\n🎉 训练完成!")
    print(f"  最终成功率: {results['success_rate']:.1%}")
    print(f"  模型保存路径: {results['model_dir']}")

if __name__ == "__main__":
    main()
