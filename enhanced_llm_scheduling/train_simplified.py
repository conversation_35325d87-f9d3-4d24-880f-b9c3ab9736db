#!/usr/bin/env python3
"""
简化的调度系统训练脚本
参考别人的系统设计：离散动作空间 + 稀疏奖励 + 事件驱动
"""

import os
import sys
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
import argparse
from datetime import datetime

# 添加路径
sys.path.append('..')
sys.path.append('.')

from simplified_data_loader import SimplifiedDataLoader
from simplified_config import SimplifiedConfig
from simplified_event_system import SimplifiedSchedulingEnvironment
from simplified_sac_agent import SimplifiedSACAgent

def train_simplified_system(data_path, num_episodes=1000, save_interval=500,
                           device_num=5, device='cuda'):
    """
    训练简化的调度系统
    Args:
        data_path: 训练数据路径
        num_episodes: 训练回合数
        save_interval: 保存间隔
        device_num: 边缘设备数量
        device: 计算设备
    """
    print("🚀 开始训练简化的调度系统")
    print("=" * 80)
    print("📋 系统特点:")
    print("  ✅ 离散动作空间 (直接选择机器)")
    print("  ✅ 稀疏奖励 (只在episode结束时给奖励)")
    print("  ✅ 事件驱动 (真实时间推进)")
    print("  ✅ 归一化奖励 (自适应边界)")
    print("=" * 80)

    # 创建配置
    config = SimplifiedConfig()
    config.update_edge_servers(device_num)
    config.set_data_path(data_path)

    print(f"📊 训练配置:")
    print(f"  数据路径: {data_path}")
    print(f"  训练回合数: {num_episodes}")
    print(f"  边缘设备数: {device_num}")
    print(f"  状态维度: {config.STATE_DIM}")
    print(f"  动作维度: {config.ACTION_DIM} (离散)")
    print(f"  计算设备: {device}")

    # 创建数据加载器（用于加载DAG数据）
    data_loader = SimplifiedDataLoader(config)

    # 创建简化的调度环境
    env = SimplifiedSchedulingEnvironment(config)

    # 创建简化的智能体
    agent = SimplifiedSACAgent(config, device=device)

    # 创建保存目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_dir = f"models/simplified_sac_{device_num}devices_{timestamp}"
    os.makedirs(model_dir, exist_ok=True)

    # 训练历史记录
    episode_rewards = []
    episode_completion_rates = []
    episode_makespans = []
    episode_memory_utilizations = []
    successful_episodes = 0
    start_time = time.time()

    print(f"\n🎯 开始训练...")

    for episode in range(num_episodes):
        try:
            # 随机选择DAG
            dag_idx = np.random.randint(0, 200)

            # 加载DAG数据
            task_features = data_loader.load_task_features(dag_idx)
            adj_matrix = data_loader.load_adjacency_matrix(dag_idx)
            machine_resources = data_loader.load_memory_status()
            num_tasks = task_features.shape[1]

            # 重置环境
            state = env.reset(task_features, adj_matrix, machine_resources)

            episode_reward = 0
            step_count = 0
            max_steps = num_tasks * 3  # 增加最大步数

            # 执行回合
            while step_count < max_steps:
                step_count += 1

                # 选择动作
                action = agent.select_action(state, deterministic=False)

                # 执行动作
                next_state, reward, done, info = env.step(action)

                # 存储经验
                agent.store_transition(state, action, reward, next_state, done)

                # 更新状态和奖励
                state = next_state
                episode_reward += reward

                # 训练智能体
                if len(agent.replay_buffer) > agent.batch_size:
                    agent.update()

                if done:
                    break

            # 记录episode结果
            completion_rate = info.get('completion_rate', 0.0)
            makespan = info.get('makespan', 1000.0)
            memory_utilization = info.get('memory_utilization', 0.0)

            episode_rewards.append(episode_reward)
            episode_completion_rates.append(completion_rate)
            episode_makespans.append(makespan if completion_rate >= 0.8 else 1000.0)
            episode_memory_utilizations.append(memory_utilization)

            if completion_rate >= 0.8:
                successful_episodes += 1

            # 打印进度
            if (episode + 1) % 20 == 0 or episode < 10:
                recent_completion = np.mean(episode_completion_rates[-20:])
                recent_makespan = np.mean([m for m in episode_makespans[-20:] if m < 1000])
                recent_reward = np.mean(episode_rewards[-20:])
                success_rate = successful_episodes / (episode + 1)

                elapsed_time = time.time() - start_time
                print(f"Episode {episode+1:4d} | "
                      f"完成率: {completion_rate:.1%} (平均: {recent_completion:.1%}) | "
                      f"Makespan: {makespan:6.1f}s (平均: {recent_makespan:6.1f}s) | "
                      f"奖励: {episode_reward:6.2f} (平均: {recent_reward:6.2f}) | "
                      f"成功率: {success_rate:.1%} | "
                      f"用时: {elapsed_time:.1f}s")

            # 保存模型
            if (episode + 1) % save_interval == 0:
                checkpoint_dir = f"{model_dir}/checkpoint_{episode+1}"
                os.makedirs(checkpoint_dir, exist_ok=True)
                agent.save_models(f"{checkpoint_dir}/agent.pth")
                print(f"💾 已保存检查点: {checkpoint_dir}")

        except Exception as e:
            print(f"❌ Episode {episode} 出错: {e}")
            continue

    # 训练完成分析
    print(f"\n📈 训练完成分析:")
    print(f"  总回合数: {num_episodes}")
    print(f"  成功回合数: {successful_episodes}")
    print(f"  成功率: {successful_episodes/num_episodes:.1%}")

    # 成功完成的episode统计
    successful_makespans = [m for m in episode_makespans if m < 1000]
    if successful_makespans:
        print(f"  最佳Makespan: {min(successful_makespans):.2f}秒")
        print(f"  平均Makespan: {np.mean(successful_makespans):.2f}秒")
        print(f"  Makespan标准差: {np.std(successful_makespans):.2f}秒")

        # 分析学习趋势
        first_half = successful_makespans[:len(successful_makespans)//2] if len(successful_makespans) > 10 else successful_makespans[:5]
        second_half = successful_makespans[len(successful_makespans)//2:] if len(successful_makespans) > 10 else successful_makespans[5:]

        if first_half and second_half:
            improvement = np.mean(first_half) - np.mean(second_half)
            print(f"  学习改进: {improvement:.2f}秒 ({'改进' if improvement > 0 else '退化'})")

    # 保存最终模型
    final_dir = f"{model_dir}/final"
    os.makedirs(final_dir, exist_ok=True)
    agent.save_models(f"{final_dir}/agent.pth")

    # 绘制学习曲线 - 改进版本
    if len(episode_rewards) > 10:
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('简化调度系统训练结果', fontsize=16, fontweight='bold')

        episodes = list(range(1, len(episode_completion_rates) + 1))

        # 1. 完成率学习曲线 - 添加移动平均
        axes[0, 0].plot(episodes, episode_completion_rates, 'b-', alpha=0.3, label='原始数据')
        # 计算移动平均
        window = 10
        if len(episode_completion_rates) >= window:
            moving_avg = []
            for i in range(len(episode_completion_rates)):
                start_idx = max(0, i - window + 1)
                avg = np.mean(episode_completion_rates[start_idx:i+1])
                moving_avg.append(avg)
            axes[0, 0].plot(episodes, moving_avg, 'b-', linewidth=2, label=f'移动平均({window})')
        axes[0, 0].set_title('完成率学习曲线')
        axes[0, 0].set_xlabel('Episode')
        axes[0, 0].set_ylabel('Completion Rate')
        axes[0, 0].grid(True, alpha=0.3)
        axes[0, 0].set_ylim(0, 1.1)
        axes[0, 0].legend()

        # 2. Makespan学习曲线 - 添加移动平均和趋势
        valid_makespans = [m if m < 2000 else np.nan for m in episode_makespans]
        axes[0, 1].plot(episodes, valid_makespans, 'g-', alpha=0.3, label='原始数据')
        # 移动平均（忽略NaN值）
        if len([m for m in valid_makespans if not np.isnan(m)]) >= window:
            moving_avg_makespan = []
            for i in range(len(valid_makespans)):
                start_idx = max(0, i - window + 1)
                window_data = [m for m in valid_makespans[start_idx:i+1] if not np.isnan(m)]
                if window_data:
                    moving_avg_makespan.append(np.mean(window_data))
                else:
                    moving_avg_makespan.append(np.nan)
            axes[0, 1].plot(episodes, moving_avg_makespan, 'g-', linewidth=2, label=f'移动平均({window})')
        axes[0, 1].set_title('Makespan学习曲线')
        axes[0, 1].set_xlabel('Episode')
        axes[0, 1].set_ylabel('Makespan (seconds)')
        axes[0, 1].grid(True, alpha=0.3)
        axes[0, 1].legend()

        # 3. 奖励学习曲线 - 添加移动平均
        axes[0, 2].plot(episodes, episode_rewards, 'r-', alpha=0.3, label='原始数据')
        if len(episode_rewards) >= window:
            moving_avg_reward = []
            for i in range(len(episode_rewards)):
                start_idx = max(0, i - window + 1)
                avg = np.mean(episode_rewards[start_idx:i+1])
                moving_avg_reward.append(avg)
            axes[0, 2].plot(episodes, moving_avg_reward, 'r-', linewidth=2, label=f'移动平均({window})')
        axes[0, 2].set_title('奖励学习曲线')
        axes[0, 2].set_xlabel('Episode')
        axes[0, 2].set_ylabel('Episode Reward')
        axes[0, 2].grid(True, alpha=0.3)
        axes[0, 2].legend()

        # 4. DAG复杂度多样性分析
        # 显示不同DAG的makespan差异，体现数据集多样性
        valid_episodes = [i for i, m in enumerate(valid_makespans, 1) if not np.isnan(m)]
        valid_makespan_values = [m for m in valid_makespans if not np.isnan(m)]
        axes[1, 0].scatter(valid_episodes, valid_makespan_values, alpha=0.6, s=20, c='orange')
        axes[1, 0].set_title('DAG复杂度多样性\n(不同DAG的Makespan差异)')
        axes[1, 0].set_xlabel('Episode (DAG Index)')
        axes[1, 0].set_ylabel('Makespan (seconds)')
        axes[1, 0].grid(True, alpha=0.3)

        # 5. 学习稳定性分析
        # 计算滑动窗口内的标准差，显示学习稳定性
        window_size = 20
        if len(valid_makespan_values) >= window_size:
            stability = []
            stability_episodes = []
            for i in range(window_size-1, len(valid_makespan_values)):
                window_data = valid_makespan_values[i-window_size+1:i+1]
                std = np.std(window_data)
                stability.append(std)
                stability_episodes.append(valid_episodes[i])
            axes[1, 1].plot(stability_episodes, stability, 'purple', linewidth=2)
            axes[1, 1].set_title(f'学习稳定性分析\n(滑动标准差, 窗口={window_size})')
            axes[1, 1].set_xlabel('Episode')
            axes[1, 1].set_ylabel('Makespan Std Dev')
            axes[1, 1].grid(True, alpha=0.3)

        # 6. Makespan分布 - 更详细的统计分析
        if successful_makespans:
            n, bins, patches = axes[1, 2].hist(successful_makespans, bins=15, alpha=0.7,
                                              color='skyblue', edgecolor='black')
            mean_makespan = np.mean(successful_makespans)
            median_makespan = np.median(successful_makespans)
            axes[1, 2].axvline(mean_makespan, color='red', linestyle='--',
                              linewidth=2, label=f'均值: {mean_makespan:.1f}s')
            axes[1, 2].axvline(median_makespan, color='green', linestyle='--',
                              linewidth=2, label=f'中位数: {median_makespan:.1f}s')
            axes[1, 2].set_title('Makespan分布统计\n(成功完成的Episode)')
            axes[1, 2].set_xlabel('Makespan (seconds)')
            axes[1, 2].set_ylabel('Frequency')
            axes[1, 2].grid(True, alpha=0.3)
            axes[1, 2].legend()

        plt.tight_layout()
        plt.savefig(f'{model_dir}/training_results.png', dpi=300, bbox_inches='tight')
        print(f"\n📊 学习曲线已保存到: {model_dir}/training_results.png")

    # 保存训练统计
    stats_df = pd.DataFrame({
        'episode': range(len(episode_rewards)),
        'reward': episode_rewards,
        'completion_rate': episode_completion_rates,
        'makespan': episode_makespans,
        'memory_utilization': episode_memory_utilizations
    })
    stats_df.to_csv(f'{model_dir}/training_stats.csv', index=False)
    print(f"📊 训练统计已保存到: {model_dir}/training_stats.csv")

    return {
        'rewards': episode_rewards,
        'completion_rates': episode_completion_rates,
        'makespans': episode_makespans,
        'memory_utilizations': episode_memory_utilizations,
        'success_rate': successful_episodes / num_episodes,
        'model_dir': model_dir
    }

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='训练简化的调度系统')
    parser.add_argument('--episodes', type=int, default=1000, help='训练回合数')
    parser.add_argument('--device_num', type=int, default=5, help='边缘设备数量')
    parser.add_argument('--data_path', type=str,
                       default='../dataset/training/DAG_30_edges_5_mem_57-64GB_density_0.2_0.6',
                       help='训练数据路径')
    parser.add_argument('--device', type=str, default='cuda', help='计算设备')
    parser.add_argument('--save_interval', type=int, default=500, help='保存间隔')

    args = parser.parse_args()

    print(f"🎯 训练参数:")
    print(f"  回合数: {args.episodes}")
    print(f"  设备数: {args.device_num}")
    print(f"  数据路径: {args.data_path}")
    print(f"  计算设备: {args.device}")

    # 开始训练
    results = train_simplified_system(
        data_path=args.data_path,
        num_episodes=args.episodes,
        save_interval=args.save_interval,
        device_num=args.device_num,
        device=args.device
    )

    print(f"\n🎉 训练完成!")
    print(f"  最终成功率: {results['success_rate']:.1%}")
    print(f"  模型保存路径: {results['model_dir']}")

if __name__ == "__main__":
    main()
