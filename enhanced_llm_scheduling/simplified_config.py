"""
简化的调度系统配置
参考别人的系统设计，使用离散动作空间和稀疏奖励
"""

class SimplifiedConfig:
    """简化的调度系统配置"""
    
    def __init__(self):
        # 基础配置
        self.NUM_EDGE_SERVERS = 5
        self.MAX_TASKS_NUM = 32
        self.STATE_DIM = 36  # 简化的状态维度
        
        # 动作空间配置 - 离散动作
        # 0: 用户设备
        # 1 to NUM_EDGE_SERVERS: 边缘设备
        self.ACTION_DIM = self.NUM_EDGE_SERVERS + 1
        
        # 训练配置
        self.LEARNING_RATE = 3e-4
        self.BATCH_SIZE = 64
        self.REPLAY_BUFFER_SIZE = 100000
        self.GAMMA = 0.99
        self.TAU = 0.005
        
        # 奖励配置 - 稀疏奖励
        self.REWARD_ONLY_AT_END = True
        self.TIME_WEIGHT = 0.5
        self.EFFICIENCY_WEIGHT = 0.5
        
        # 数据路径
        self.TRAINING_DATA_PATH = "../dataset/training/DAG_30_edges_5_mem_57-64GB_density_0.2_0.6"
        self.TESTING_DATA_PATH = "../dataset/testing"
        
        # 机器资源文件
        self.MACHINES_RESOURCE_FILE = "../dataset/machines_resource1.xlsx"
        
        # 时间管理
        self.USE_EVENT_DRIVEN = True  # 是否使用事件驱动
        self.CURRENT_TIME = 0.0
        
        # 奖励归一化
        self.USE_REWARD_NORMALIZATION = True
        self.TIME_MIN = float('inf')
        self.TIME_MAX = float('-inf')
        self.EFFICIENCY_MIN = float('inf')
        self.EFFICIENCY_MAX = float('-inf')
        
        print(f"[SIMPLIFIED_CONFIG] 初始化完成")
        print(f"  边缘设备数: {self.NUM_EDGE_SERVERS}")
        print(f"  动作维度: {self.ACTION_DIM} (离散)")
        print(f"  状态维度: {self.STATE_DIM}")
        print(f"  稀疏奖励: {self.REWARD_ONLY_AT_END}")
        print(f"  事件驱动: {self.USE_EVENT_DRIVEN}")
    
    def update_edge_servers(self, num_servers):
        """更新边缘设备数量"""
        self.NUM_EDGE_SERVERS = num_servers
        self.ACTION_DIM = num_servers + 1
        print(f"[SIMPLIFIED_CONFIG] 更新边缘设备数: {num_servers}, 动作维度: {self.ACTION_DIM}")
    
    def set_data_path(self, path):
        """设置数据路径"""
        self.TRAINING_DATA_PATH = path
        print(f"[SIMPLIFIED_CONFIG] 设置数据路径: {path}")
    
    def update_reward_bounds(self, time_value, efficiency_value):
        """更新奖励归一化边界"""
        if self.USE_REWARD_NORMALIZATION:
            self.TIME_MIN = min(self.TIME_MIN, time_value)
            self.TIME_MAX = max(self.TIME_MAX, time_value)
            self.EFFICIENCY_MIN = min(self.EFFICIENCY_MIN, efficiency_value)
            self.EFFICIENCY_MAX = max(self.EFFICIENCY_MAX, efficiency_value)
    
    def get_normalized_reward(self, time_value, efficiency_value):
        """计算归一化奖励"""
        if not self.USE_REWARD_NORMALIZATION:
            return -self.TIME_WEIGHT * time_value - self.EFFICIENCY_WEIGHT * efficiency_value
        
        # 确保最大最小值不同
        time_min = self.TIME_MIN if self.TIME_MIN != float('inf') else time_value
        time_max = self.TIME_MAX if self.TIME_MAX != float('-inf') else time_value + 1
        efficiency_min = self.EFFICIENCY_MIN if self.EFFICIENCY_MIN != float('inf') else efficiency_value
        efficiency_max = self.EFFICIENCY_MAX if self.EFFICIENCY_MAX != float('-inf') else efficiency_value + 1
        
        if time_max <= time_min:
            time_max = time_min + 1
        if efficiency_max <= efficiency_min:
            efficiency_max = efficiency_min + 1
        
        # 归一化到 [0,1] 区间
        normalized_time = (time_value - time_min) / (time_max - time_min)
        normalized_efficiency = (efficiency_value - efficiency_min) / (efficiency_max - efficiency_min)
        
        # 确保在 [0,1] 区间内
        normalized_time = max(0.0, min(1.0, normalized_time))
        normalized_efficiency = max(0.0, min(1.0, normalized_efficiency))
        
        # 计算归一化后的加权奖励（负值，因为我们要最小化）
        reward = -self.TIME_WEIGHT * normalized_time - self.EFFICIENCY_WEIGHT * normalized_efficiency
        
        return reward
