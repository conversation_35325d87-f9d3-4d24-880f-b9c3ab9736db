"""
改进的奖励函数
专注于最小化makespan和最大化任务完成率
"""
import numpy as np

class ImprovedRewardFunction:
    """改进的奖励函数 - 专注于makespan和完成率优化"""

    def __init__(self, max_makespan=200.0, completion_weight=50.0, makespan_weight=20.0,
                 efficiency_weight=5.0, penalty_weight=30.0, step_reward_scale=1.0,
                 task_completion_reward=10.0, llm_edge_bonus=2.0, memory_fail_penalty=10.0,
                 no_ready_penalty=2.0, time_penalty_factor=0.1):
        """
        初始化奖励函数
        Args:
            max_makespan: 最大makespan，用于归一化
            completion_weight: 完成率权重
            makespan_weight: makespan权重
            efficiency_weight: 效率权重
            penalty_weight: 惩罚权重
            step_reward_scale: 步骤奖励缩放因子
            task_completion_reward: 任务完成奖励
            llm_edge_bonus: LLM边缘执行奖励
            memory_fail_penalty: 内存失败惩罚
            no_ready_penalty: 无就绪任务惩罚
            time_penalty_factor: 时间惩罚因子
        """
        self.max_makespan = max_makespan
        self.completion_weight = completion_weight
        self.makespan_weight = makespan_weight
        self.efficiency_weight = efficiency_weight
        self.penalty_weight = penalty_weight
        self.step_reward_scale = step_reward_scale

        # 步骤级奖励参数
        self.task_completion_reward = task_completion_reward
        self.llm_edge_bonus = llm_edge_bonus
        self.memory_fail_penalty = memory_fail_penalty
        self.no_ready_penalty = no_ready_penalty
        self.time_penalty_factor = time_penalty_factor
        self.max_task_time = 50.0  # 最大单任务执行时间（秒）- 调整为更合理的值

        # LLM本地执行惩罚
        self.llm_local_penalty = 5.0

        print(f"[IMPROVED_REWARD] 初始化改进奖励函数")
        print(f"  最大Makespan: {max_makespan}秒")
        print(f"  完成率权重: {completion_weight}")
        print(f"  Makespan权重: {makespan_weight}")
        print(f"  效率权重: {efficiency_weight}")
        print(f"  惩罚权重: {penalty_weight}")
        print(f"  步骤奖励缩放: {step_reward_scale}")
        print(f"  任务完成奖励: {task_completion_reward}")
        print(f"  LLM边缘奖励: {llm_edge_bonus}")
        print(f"  内存失败惩罚: {memory_fail_penalty}")

    def calculate_reward(self, step_info, episode_info=None):
        """
        改进的奖励计算 - 支持步骤级和回合级奖励
        Args:
            step_info: 单步信息
            episode_info: 回合信息（可选，用于回合结束时的额外奖励）
        Returns:
            reward: 计算得到的奖励值
            reward_components: 奖励组件字典
        """
        step_reward = 0.0
        reward_components = {}

        # --- 步骤级奖励 ---
        if step_info.get('task_completed_successfully', False):
            # 基础任务完成奖励
            step_reward += self.task_completion_reward
            reward_components['step_task_completion'] = self.task_completion_reward

            # LLM任务的机器分配奖励/惩罚
            if step_info.get('is_llm', False):
                assigned_machine = step_info.get('assigned_machine_idx', 0)
                if assigned_machine > 0:
                    # LLM任务在边缘设备执行 - 奖励
                    step_reward += self.llm_edge_bonus
                    reward_components['step_llm_on_edge'] = self.llm_edge_bonus
                else:
                    # LLM任务在本地设备执行 - 惩罚
                    step_reward -= self.llm_local_penalty
                    reward_components['step_llm_on_local'] = -self.llm_local_penalty

            # 时间惩罚（基于当前步骤执行的任务）
            task_exec_time = step_info.get('task_execution_time_sec', 0)
            time_penalty = min(task_exec_time, self.max_task_time) * self.time_penalty_factor
            step_reward -= time_penalty
            reward_components['step_time_penalty'] = -time_penalty

            # 内存效率奖励
            predicted_memory = step_info.get('predicted_memory', 0)
            if predicted_memory > 0:
                memory_efficiency = 1.0 / (predicted_memory + 1.0)
                step_reward += memory_efficiency
                reward_components['step_memory_efficiency'] = memory_efficiency

        # 任务因内存不足失败的惩罚
        if step_info.get('task_failed_memory', False):
            step_reward -= self.memory_fail_penalty
            reward_components['step_mem_fail_penalty'] = -self.memory_fail_penalty

        # 无就绪任务但DAG未完成的惩罚
        if step_info.get('no_ready_tasks_but_not_done', False):
            step_reward -= self.no_ready_penalty
            reward_components['step_no_ready_penalty'] = -self.no_ready_penalty

        # 应用步骤奖励缩放
        step_reward *= self.step_reward_scale
        total_reward = step_reward

        # --- 回合结束奖励 ---
        if episode_info is not None:
            total_tasks = episode_info.get('total_tasks', 1)
            completed_tasks = episode_info.get('completed_tasks', 0)
            completion_rate = completed_tasks / total_tasks

            # 完成率奖励
            completion_reward = completion_rate * self.completion_weight
            total_reward += completion_reward
            reward_components['episode_completion_reward'] = completion_reward

            # 完美完成奖励
            if completion_rate == 1.0:
                perfect_bonus = 50.0
                total_reward += perfect_bonus
                reward_components['episode_perfect_bonus'] = perfect_bonus

                # Makespan奖励（仅在完成率高时给予）
                makespan = episode_info.get('makespan', self.max_makespan)
                makespan_normalized = min(makespan, self.max_makespan) / self.max_makespan
                makespan_reward = self.makespan_weight * (1.0 - makespan_normalized)
                total_reward += makespan_reward
                reward_components['episode_makespan_reward'] = makespan_reward

            # 死锁惩罚
            if episode_info.get('deadlock_occurred', False):
                total_reward -= self.penalty_weight
                reward_components['episode_deadlock_penalty'] = -self.penalty_weight

            # 失败率惩罚
            failed_tasks = episode_info.get('failed_tasks', 0)
            if failed_tasks > 0:
                failure_rate = failed_tasks / total_tasks
                failure_penalty = failure_rate * self.penalty_weight
                total_reward -= failure_penalty
                reward_components['episode_failure_penalty'] = -failure_penalty

        return total_reward, reward_components

    def _calculate_simple_makespan_reward(self, episode_info):
        """
        简化的makespan专注奖励计算
        Args:
            episode_info: 回合信息
        Returns:
            dict: 奖励组件字典
        """
        reward_components = {}

        # 获取关键指标
        total_tasks = episode_info.get('total_tasks', 1)
        completed_tasks = episode_info.get('completed_tasks', 0)
        makespan = episode_info.get('makespan', self.max_makespan)  # 秒
        failed_tasks = episode_info.get('failed_tasks', 0)
        deadlock_occurred = episode_info.get('deadlock_occurred', False)

        completion_rate = completed_tasks / total_tasks

        # 1. 基础完成率奖励（简化版）
        if completion_rate >= 0.8:
            completion_reward = 50.0 * completion_rate  # 高完成率给高奖励
        elif completion_rate >= 0.5:
            completion_reward = 30.0 * completion_rate  # 中等完成率给中等奖励
        else:
            completion_reward = 10.0 * completion_rate - 5.0  # 低完成率给惩罚

        reward_components['completion'] = completion_reward

        # 2. Makespan奖励（核心）- 只有在完成率>50%时才考虑
        if completion_rate > 0.5:
            if makespan <= self.max_makespan:
                # 简单的线性makespan奖励
                makespan_ratio = makespan / self.max_makespan
                makespan_reward = 30.0 * (1.0 - makespan_ratio) * completion_rate
                reward_components['makespan'] = makespan_reward
            else:
                # makespan过大惩罚
                reward_components['makespan'] = -10.0
        else:
            reward_components['makespan'] = 0.0

        # 3. 失败和死锁惩罚（简化版）
        if failed_tasks > 0:
            reward_components['failure'] = -20.0 * (failed_tasks / total_tasks)
        else:
            reward_components['failure'] = 0.0

        if deadlock_occurred:
            reward_components['deadlock'] = -15.0
        else:
            reward_components['deadlock'] = 0.0

        # 4. 完美执行奖励
        if completion_rate == 1.0 and not deadlock_occurred:
            reward_components['perfect'] = 20.0
        else:
            reward_components['perfect'] = 0.0

        return reward_components

    def _calculate_episode_reward(self, episode_info):
        """
        计算回合结束时的综合奖励
        Args:
            episode_info: 回合信息
        Returns:
            dict: 各组件的奖励值
        """
        reward_components = {}

        # 获取关键指标
        total_tasks = episode_info.get('total_tasks', 1)
        completed_tasks = episode_info.get('completed_tasks', 0)
        makespan = episode_info.get('makespan', self.max_makespan)  # 秒
        failed_tasks = episode_info.get('failed_tasks', 0)
        deadlock_occurred = episode_info.get('deadlock_occurred', False)

        # 1. 完成率奖励（最重要的指标）- 使用指数奖励鼓励高完成率
        completion_rate = completed_tasks / total_tasks
        if completion_rate == 1.0:
            # 100%完成给予最高奖励
            completion_reward = self.completion_weight * 3.0
        elif completion_rate >= 0.9:
            # 90%以上完成给予很高奖励
            completion_reward = self.completion_weight * (2.0 + completion_rate)
        elif completion_rate >= 0.7:
            # 70%以上完成给予高奖励
            completion_reward = self.completion_weight * (1.5 * completion_rate)
        elif completion_rate >= 0.5:
            # 50%以上完成给予中等奖励
            completion_reward = self.completion_weight * completion_rate
        else:
            # 50%以下完成给予惩罚
            completion_reward = self.completion_weight * (completion_rate - 0.5)

        reward_components['completion_rate'] = completion_reward

        # 2. Makespan奖励（只有在高完成率时才给予makespan奖励）
        if completion_rate >= 0.7:  # 只有完成率达到70%以上才考虑makespan
            # 使用更严格的makespan归一化
            if makespan <= self.max_makespan:
                # 非线性makespan奖励：makespan越小奖励越高
                makespan_ratio = makespan / self.max_makespan
                makespan_reward = self.makespan_weight * (1.0 - makespan_ratio) ** 2 * completion_rate

                # 特别优秀的makespan给予额外奖励
                if makespan < self.max_makespan * 0.5:
                    makespan_reward += self.makespan_weight * 1.0
                elif makespan < self.max_makespan * 0.3:
                    makespan_reward += self.makespan_weight * 2.0

                reward_components['makespan'] = makespan_reward
            else:
                # makespan超过最大值时给予惩罚
                makespan_penalty = -self.makespan_weight * 0.5
                reward_components['makespan'] = makespan_penalty
        else:
            reward_components['makespan'] = 0.0

        # 3. 效率奖励（完成率/时间比）
        if makespan > 0 and completed_tasks > 0:
            efficiency = completed_tasks / makespan  # 任务/秒
            # 归一化效率（假设最高效率为1任务/秒）
            normalized_efficiency = min(efficiency, 1.0)
            efficiency_reward = self.efficiency_weight * normalized_efficiency
            reward_components['efficiency'] = efficiency_reward
        else:
            reward_components['efficiency'] = 0.0

        # 4. 失败惩罚
        if failed_tasks > 0:
            failure_penalty = -self.penalty_weight * (failed_tasks / total_tasks)
            reward_components['failure_penalty'] = failure_penalty
        else:
            reward_components['failure_penalty'] = 0.0

        # 5. 死锁惩罚
        if deadlock_occurred:
            deadlock_penalty = -self.penalty_weight * 0.5
            reward_components['deadlock_penalty'] = deadlock_penalty
        else:
            reward_components['deadlock_penalty'] = 0.0

        # 6. 完美执行奖励
        if completion_rate == 1.0 and not deadlock_occurred and failed_tasks == 0:
            perfect_bonus = self.completion_weight * 0.5
            reward_components['perfect_bonus'] = perfect_bonus
        else:
            reward_components['perfect_bonus'] = 0.0

        return reward_components

    def get_reward_info(self, step_info, episode_info=None):
        """
        获取详细的奖励信息（用于调试和分析）
        """
        reward, components = self.calculate_reward(step_info, episode_info)

        info = {
            'total_reward': reward,
            'components': components,
            'completion_weight': self.completion_weight,
            'makespan_weight': self.makespan_weight,
            'efficiency_weight': self.efficiency_weight,
            'penalty_weight': self.penalty_weight
        }

        if episode_info is not None:
            info.update({
                'completion_rate': episode_info.get('completed_tasks', 0) / episode_info.get('total_tasks', 1),
                'makespan': episode_info.get('makespan', 0),
                'failed_tasks': episode_info.get('failed_tasks', 0),
                'deadlock_occurred': episode_info.get('deadlock_occurred', False)
            })

        return info

class MakespanFocusedReward:
    """专注于Makespan优化的奖励函数"""

    def __init__(self, target_makespan=100.0, completion_threshold=0.8):
        """
        初始化专注于makespan的奖励函数
        Args:
            target_makespan: 目标makespan（秒）
            completion_threshold: 完成率阈值
        """
        self.target_makespan = target_makespan
        self.completion_threshold = completion_threshold

        print(f"[MAKESPAN_REWARD] 初始化Makespan专注奖励函数")
        print(f"  目标Makespan: {target_makespan}秒")
        print(f"  完成率阈值: {completion_threshold}")

    def calculate_reward(self, episode_info):
        """
        计算专注于makespan的奖励
        Args:
            episode_info: 回合信息
        Returns:
            reward: 奖励值
        """
        total_tasks = episode_info.get('total_tasks', 1)
        completed_tasks = episode_info.get('completed_tasks', 0)
        makespan = episode_info.get('makespan', float('inf'))

        completion_rate = completed_tasks / total_tasks

        # 只有当完成率达到阈值时才考虑makespan奖励
        if completion_rate >= self.completion_threshold:
            # 完成率奖励
            completion_reward = 10.0 * completion_rate

            # Makespan奖励（makespan越小奖励越高）
            if makespan < float('inf'):
                makespan_ratio = self.target_makespan / makespan
                makespan_reward = 20.0 * min(makespan_ratio, 2.0)  # 最多2倍奖励
            else:
                makespan_reward = 0.0

            total_reward = completion_reward + makespan_reward
        else:
            # 完成率不足时只给基础奖励
            total_reward = 5.0 * completion_rate

        return total_reward

def create_reward_function(reward_type='improved', **kwargs):
    """
    创建奖励函数的工厂函数
    Args:
        reward_type: 奖励函数类型 ('improved', 'makespan_focused')
        **kwargs: 奖励函数参数
    Returns:
        奖励函数实例
    """
    if reward_type == 'improved':
        return ImprovedRewardFunction(**kwargs)
    elif reward_type == 'makespan_focused':
        return MakespanFocusedReward(**kwargs)
    else:
        raise ValueError(f"未知的奖励函数类型: {reward_type}")

# 使用示例
if __name__ == "__main__":
    # 创建改进的奖励函数
    reward_func = ImprovedRewardFunction()

    # 模拟步骤信息
    step_info = {
        'valid_action': True,
        'task_completed': True,
        'memory_utilization': 0.6,
        'load_balance_score': 0.8
    }

    # 模拟回合信息
    episode_info = {
        'total_tasks': 10,
        'completed_tasks': 9,
        'makespan': 150.0,
        'failed_tasks': 1,
        'deadlock_occurred': False
    }

    # 计算奖励
    reward, components = reward_func.calculate_reward(step_info, episode_info)

    print(f"总奖励: {reward:.3f}")
    print("奖励组件:")
    for component, value in components.items():
        print(f"  {component}: {value:.3f}")
