#!/usr/bin/env python3
"""
分析数据集结构：任务特征和依赖矩阵的对应关系
"""

import numpy as np
import pandas as pd
import os
import sys

def analyze_dag_structure(data_path, dag_id=0):
    """分析单个DAG的结构"""
    print(f"\n🔍 分析DAG {dag_id} 的结构:")
    print(f"数据路径: {data_path}")
    
    # 1. 加载任务特征
    task_features_file = f"{data_path}/task_{dag_id}_features.xlsx"
    if not os.path.exists(task_features_file):
        print(f"❌ 任务特征文件不存在: {task_features_file}")
        return None
    
    task_features_df = pd.read_excel(task_features_file)
    print(f"\n📋 任务特征文件结构:")
    print(f"  文件: {task_features_file}")
    print(f"  形状: {task_features_df.shape}")
    print(f"  列名: {list(task_features_df.columns)}")
    print(f"  前5行:")
    print(task_features_df.head())
    
    # 2. 加载依赖矩阵
    adj_matrix_file = f"{data_path}/adjacency_{dag_id}_matrix.npy"
    if not os.path.exists(adj_matrix_file):
        print(f"❌ 依赖矩阵文件不存在: {adj_matrix_file}")
        return None
    
    adj_matrix = np.load(adj_matrix_file)
    print(f"\n🔗 依赖矩阵结构:")
    print(f"  文件: {adj_matrix_file}")
    print(f"  形状: {adj_matrix.shape}")
    print(f"  矩阵:")
    print(adj_matrix)
    
    # 3. 分析对应关系
    num_tasks_features = task_features_df.shape[0]
    num_tasks_matrix = adj_matrix.shape[0]
    
    print(f"\n📊 对应关系分析:")
    print(f"  任务特征文件中的任务数: {num_tasks_features}")
    print(f"  依赖矩阵中的任务数: {num_tasks_matrix}")
    print(f"  是否匹配: {'✅' if num_tasks_features == num_tasks_matrix else '❌'}")
    
    # 4. 分析任务类型
    if 'task_type' in task_features_df.columns:
        task_types = task_features_df['task_type'].value_counts()
        print(f"\n🏷️ 任务类型分布:")
        for task_type, count in task_types.items():
            print(f"  {task_type}: {count}个")
    
    # 5. 分析依赖关系
    print(f"\n🔗 依赖关系分析:")
    
    # 找到入度为0的任务（初始任务）
    in_degrees = np.sum(adj_matrix, axis=0)
    initial_tasks = np.where(in_degrees == 0)[0]
    print(f"  初始任务（入度=0）: {initial_tasks.tolist()}")
    
    # 找到出度为0的任务（终端任务）
    out_degrees = np.sum(adj_matrix, axis=1)
    terminal_tasks = np.where(out_degrees == 0)[0]
    print(f"  终端任务（出度=0）: {terminal_tasks.tolist()}")
    
    # 分析每个任务的前置和后续任务
    print(f"\n📋 任务依赖详情:")
    for i in range(min(10, num_tasks_matrix)):  # 只显示前10个任务
        predecessors = np.where(adj_matrix[:, i] == 1)[0]
        successors = np.where(adj_matrix[i, :] == 1)[0]
        print(f"  任务{i}: 前置{predecessors.tolist()} -> 后续{successors.tolist()}")
    
    # 6. 分析并行度
    print(f"\n⚡ 并行度分析:")
    max_parallelism = len(initial_tasks)
    print(f"  初始并行度: {max_parallelism}")
    
    # 简单估算最大可能并行度（所有无依赖冲突的任务）
    total_edges = np.sum(adj_matrix)
    density = total_edges / (num_tasks_matrix * (num_tasks_matrix - 1))
    print(f"  DAG密度: {density:.3f}")
    print(f"  总边数: {total_edges}")
    
    return {
        'dag_id': dag_id,
        'num_tasks': num_tasks_matrix,
        'initial_tasks': initial_tasks.tolist(),
        'terminal_tasks': terminal_tasks.tolist(),
        'total_edges': int(total_edges),
        'density': density,
        'max_initial_parallelism': max_parallelism,
        'task_features_shape': task_features_df.shape,
        'adj_matrix_shape': adj_matrix.shape
    }

def analyze_multiple_dags(data_path, dag_ids=[0, 1, 2, 50, 100, 150]):
    """分析多个DAG的结构"""
    print(f"🔍 分析多个DAG的结构特征")
    
    results = []
    for dag_id in dag_ids:
        try:
            result = analyze_dag_structure(data_path, dag_id)
            if result:
                results.append(result)
        except Exception as e:
            print(f"❌ 分析DAG {dag_id} 失败: {e}")
    
    if results:
        # 创建汇总表
        summary_df = pd.DataFrame(results)
        print(f"\n📊 DAG结构汇总:")
        print(summary_df)
        
        # 保存结果
        output_file = f"{data_path}/dag_structure_analysis.xlsx"
        summary_df.to_excel(output_file, index=False)
        print(f"\n💾 分析结果已保存到: {output_file}")
        
        # 统计信息
        print(f"\n📈 统计信息:")
        print(f"  任务数范围: {summary_df['num_tasks'].min()} - {summary_df['num_tasks'].max()}")
        print(f"  平均任务数: {summary_df['num_tasks'].mean():.1f}")
        print(f"  密度范围: {summary_df['density'].min():.3f} - {summary_df['density'].max():.3f}")
        print(f"  平均密度: {summary_df['density'].mean():.3f}")
        print(f"  最大初始并行度: {summary_df['max_initial_parallelism'].max()}")
        print(f"  平均初始并行度: {summary_df['max_initial_parallelism'].mean():.1f}")
    
    return results

def check_dataset_consistency(data_path):
    """检查数据集的一致性"""
    print(f"\n🔍 检查数据集一致性:")
    
    # 检查文件数量
    task_files = [f for f in os.listdir(data_path) if f.startswith('task_') and f.endswith('_features.xlsx')]
    adj_files = [f for f in os.listdir(data_path) if f.startswith('adjacency_') and f.endswith('_matrix.npy')]
    
    print(f"  任务特征文件数: {len(task_files)}")
    print(f"  依赖矩阵文件数: {len(adj_files)}")
    
    # 提取DAG ID
    task_ids = set()
    for f in task_files:
        try:
            dag_id = int(f.split('_')[1])
            task_ids.add(dag_id)
        except:
            pass
    
    adj_ids = set()
    for f in adj_files:
        try:
            dag_id = int(f.split('_')[1])
            adj_ids.add(dag_id)
        except:
            pass
    
    print(f"  任务特征DAG ID范围: {min(task_ids) if task_ids else 'N/A'} - {max(task_ids) if task_ids else 'N/A'}")
    print(f"  依赖矩阵DAG ID范围: {min(adj_ids) if adj_ids else 'N/A'} - {max(adj_ids) if adj_ids else 'N/A'}")
    
    # 检查匹配性
    missing_task_files = adj_ids - task_ids
    missing_adj_files = task_ids - adj_ids
    
    if missing_task_files:
        print(f"  ❌ 缺少任务特征文件的DAG: {sorted(list(missing_task_files))[:10]}...")
    
    if missing_adj_files:
        print(f"  ❌ 缺少依赖矩阵文件的DAG: {sorted(list(missing_adj_files))[:10]}...")
    
    if not missing_task_files and not missing_adj_files:
        print(f"  ✅ 所有DAG的文件都匹配")
    
    return {
        'task_file_count': len(task_files),
        'adj_file_count': len(adj_files),
        'task_ids': sorted(list(task_ids)),
        'adj_ids': sorted(list(adj_ids)),
        'missing_task_files': sorted(list(missing_task_files)),
        'missing_adj_files': sorted(list(missing_adj_files))
    }

def main():
    """主函数"""
    if len(sys.argv) > 1:
        data_path = sys.argv[1]
    else:
        data_path = "../dataset/training/DAG_200_edges_5_mem_32GB_density_0.4_0.6"
    
    print(f"🚀 数据集结构分析工具")
    print(f"数据路径: {data_path}")
    
    if not os.path.exists(data_path):
        print(f"❌ 数据路径不存在: {data_path}")
        return
    
    # 1. 检查数据集一致性
    consistency_result = check_dataset_consistency(data_path)
    
    # 2. 分析单个DAG结构
    print(f"\n" + "="*80)
    analyze_dag_structure(data_path, dag_id=0)
    
    # 3. 分析多个DAG
    print(f"\n" + "="*80)
    analyze_multiple_dags(data_path, dag_ids=[0, 1, 2, 10, 50, 100, 150, 199])

if __name__ == "__main__":
    main()
