"""
增强的数据加载器
支持动态状态管理和LLM任务的预测功能
"""
import numpy as np
import pandas as pd
import os
import sys
sys.path.append('..')
from data_loader import DataLoader
from enhanced_config import EnhancedLLMConfig

class EnhancedDataLoader(DataLoader):
    """增强的数据加载器，支持动态LLM调度"""

    def __init__(self, config=None, debug=False):
        if config is None:
            config = EnhancedLLMConfig()
        super().__init__(config)
        self.debug = debug

        # 任务状态跟踪
        self._task_status = {}  # task_idx -> status_index
        self._task_input_tokens = {}  # task_idx -> input_token_count
        self._task_predicted_output_tokens = {}  # task_idx -> predicted_output_tokens
        self._task_predicted_memory = {}  # task_idx -> predicted_runtime_memory

        # 存储原始任务特征用于LLM预测
        self._original_task_features = None

        # 存储每台机器的总内存容量
        self._machine_total_memory = {}

        # 确保机器异构参数被正确加载
        self._ensure_machine_heterogeneity_loaded()

        print(f"[ENHANCED_LOADER] 初始化完成，状态维度: {self.config.ENHANCED_STATE_DIM}")
        if self.debug:
            self._print_machine_heterogeneity_info()

    def _ensure_machine_heterogeneity_loaded(self):
        """确保机器异构参数被正确加载"""
        # 调用父类的load_machines_resource方法，确保异构参数被加载
        try:
            self.load_machines_resource()

            # 验证异构参数是否正确加载
            if not hasattr(self, 'token_per_second') or self.token_per_second is None:
                if self.debug:
                    print("[HETEROGENEITY] token_per_second未加载，使用默认值")
                num_machines = getattr(self.config, 'NUM_EDGE_SERVERS', 5) + 1
                self.token_per_second = np.full(num_machines, self.config.TOKEN_PER_SECOND)

            if not hasattr(self, 'base_execution_time') or self.base_execution_time is None:
                if self.debug:
                    print("[HETEROGENEITY] base_execution_time未加载，使用默认值")
                num_machines = getattr(self.config, 'NUM_EDGE_SERVERS', 5) + 1
                self.base_execution_time = np.full(num_machines, self.config.BASE_EXECUTION_TIME)

        except Exception as e:
            if self.debug:
                print(f"[HETEROGENEITY] 加载机器异构参数失败: {e}")
            # 使用默认值
            num_machines = getattr(self.config, 'NUM_EDGE_SERVERS', 5) + 1
            self.token_per_second = np.full(num_machines, self.config.TOKEN_PER_SECOND)
            self.base_execution_time = np.full(num_machines, self.config.BASE_EXECUTION_TIME)

    def _print_machine_heterogeneity_info(self):
        """打印机器异构性信息"""
        print(f"[HETEROGENEITY] 机器异构参数:")
        if hasattr(self, 'token_per_second') and self.token_per_second is not None:
            print(f"  Token处理速度: {self.token_per_second}")
        if hasattr(self, 'base_execution_time') and self.base_execution_time is not None:
            print(f"  基础执行时间: {self.base_execution_time}")

    def _load_machine_total_memory(self):
        """读取并存储每台机器的总内存容量"""
        try:
            # 直接读取机器资源Excel文件获取完整的DataFrame
            file_path = self._get_machine_resource_file_path()

            if file_path and os.path.exists(file_path):
                # 读取Excel文件
                df = pd.read_excel(file_path)

                if self.debug:
                    print(f"[MACHINE_MEMORY] 读取文件: {file_path}")
                    print(f"[MACHINE_MEMORY] 文件列名: {df.columns.tolist()}")

                # 查找内存列
                memory_col = None
                if 'Memory_Available' in df.columns:
                    memory_col = 'Memory_Available'
                elif 'memory' in df.columns:
                    memory_col = 'memory'
                elif 'Memory' in df.columns:
                    memory_col = 'Memory'

                if memory_col:
                    # 从DataFrame中读取内存数据
                    for i, memory in enumerate(df[memory_col]):
                        self._machine_total_memory[i] = float(memory)

                    if self.debug:
                        print(f"[MACHINE_MEMORY] 成功读取机器总内存配置:")
                        for i, total_mem in self._machine_total_memory.items():
                            machine_type = "用户设备" if i == 0 else f"边缘设备{i}"
                            print(f"    {machine_type}: {total_mem:.2f}GB")
                else:
                    raise ValueError(f"在文件中找不到内存列，可用列: {df.columns.tolist()}")
            else:
                raise FileNotFoundError(f"机器资源文件不存在: {file_path}")

        except Exception as e:
            if self.debug:
                print(f"[MACHINE_MEMORY] 读取机器内存配置失败: {e}")
            self._use_default_memory_config()

    def _get_machine_resource_file_path(self):
        """获取机器资源文件路径"""
        # 首先尝试配置中的路径
        if hasattr(self.config, 'MACHINES_RESOURCE_PATH'):
            file_path = self.config.MACHINES_RESOURCE_PATH
            if os.path.exists(file_path):
                return file_path

        # 尝试自定义数据路径
        if hasattr(self.config, 'TRAINING_DATA_PATH'):
            file_path = os.path.join(self.config.TRAINING_DATA_PATH, 'machines_resource1.xlsx')
            if os.path.exists(file_path):
                return file_path

        # 尝试默认路径
        default_paths = [
            'dataset/machines_resource1.xlsx',
            '../dataset/machines_resource1.xlsx',
            f'dataset/training/DAG_{getattr(self.config, "MAX_TASKS_NUM", 30)}_edges_{getattr(self.config, "NUM_EDGE_SERVERS", 5)}_mem_{getattr(self.config, "MEMORY_LIMIT", 32)}GB_density_0.4_0.6/machines_resource1.xlsx'
        ]

        for path in default_paths:
            if os.path.exists(path):
                return path

        return None

    def _use_default_memory_config(self):
        """使用默认的内存配置"""
        # 假设用户设备16GB，边缘设备32GB
        self._machine_total_memory[0] = 16.0
        for i in range(1, 7):  # 最多6个边缘设备
            self._machine_total_memory[i] = 32.0

    def _init_step_state(self, task_features, adj_matrix, machine_resources, comm_speed, memory_status):
        """初始化步骤状态，包含增强的任务状态管理"""
        super()._init_step_state(task_features, adj_matrix, machine_resources, comm_speed, memory_status)

        num_tasks = task_features.shape[1]

        # 存储原始任务特征用于LLM预测
        self._original_task_features = task_features

        # 读取并存储每台机器的总内存容量
        self._load_machine_total_memory()

        # 初始化任务状态
        self._task_status = {}
        self._task_input_tokens = {}
        self._task_predicted_output_tokens = {}
        self._task_predicted_memory = {}

        # 为所有任务初始化状态
        for task_idx in range(num_tasks):
            # 初始状态：未就绪/等待输入
            self._task_status[task_idx] = 0

            # 初始化token信息
            if task_features[2, task_idx] == 1:  # LLM任务
                self._task_input_tokens[task_idx] = 0  # 等待前序任务确定
                self._task_predicted_output_tokens[task_idx] = 0
                self._task_predicted_memory[task_idx] = 0
            else:  # 非LLM任务
                self._task_input_tokens[task_idx] = task_features[3, task_idx]  # 使用原始token数
                self._task_predicted_output_tokens[task_idx] = task_features[3, task_idx]
                self._task_predicted_memory[task_idx] = task_features[4, task_idx]  # 使用静态内存

        # 更新初始就绪任务的状态
        self._update_initial_ready_tasks(adj_matrix, num_tasks)

        if self.debug:
            print(f"[ENHANCED_INIT] 初始化{num_tasks}个任务的状态")

    def _update_initial_ready_tasks(self, adj_matrix, num_tasks):
        """初始化时更新就绪任务状态"""
        for task_idx in range(num_tasks):
            # 检查是否有前驱任务
            has_predecessors = False
            for pred_idx in range(num_tasks):
                if adj_matrix[pred_idx, task_idx] > 0:
                    has_predecessors = True
                    break

            # 如果没有前驱任务，设为就绪状态
            if not has_predecessors:
                self._task_status[task_idx] = 1  # 就绪可调度

                # 如果是LLM任务，使用文件中的初始token数
                if self._original_task_features[2, task_idx] == 1:
                    input_tokens = self._original_task_features[3, task_idx]
                    self._task_input_tokens[task_idx] = input_tokens

                    # 预测输出和内存
                    predicted_output = self._get_llm_output_tokens_from_file(task_idx, input_tokens)
                    predicted_memory = self._get_llm_memory_from_file(task_idx, input_tokens, predicted_output)

                    self._task_predicted_output_tokens[task_idx] = predicted_output
                    self._task_predicted_memory[task_idx] = predicted_memory

                    if self.debug:
                        print(f"[INIT_LLM] 任务{task_idx}: 输入{input_tokens}tokens -> "
                              f"输出{predicted_output}tokens, 内存{predicted_memory:.2f}GB")

                if self.debug:
                    print(f"[INIT_READY] 任务{task_idx}设为初始就绪状态")

    def _update_task_readiness(self, adj_matrix, completed_tasks, num_tasks):
        """更新任务就绪状态，包括LLM任务的输入token计算"""
        # 首先释放已完成任务的内存
        self._release_completed_tasks_memory()

        newly_ready_tasks = []

        for task_idx in range(num_tasks):
            # 跳过已完成或失败的任务
            if (task_idx in completed_tasks or
                task_idx in self._step_state.get('failed_tasks', set())):
                if self._task_status[task_idx] != 3:  # 如果还没标记为完成
                    self._task_status[task_idx] = 3  # 已完成/失败
                continue

            # 跳过已在就绪队列或运行中的任务
            if (task_idx in self._step_state.get('ready_tasks', []) or
                task_idx in self._step_state.get('pending_tasks', set()) or
                self._task_status[task_idx] == 2):  # 运行中
                continue

            # 检查依赖是否满足
            all_deps_completed = True
            total_input_tokens = 0

            for pred_idx in range(num_tasks):
                if adj_matrix[pred_idx, task_idx] > 0:  # pred_idx是task_idx的前序任务
                    if pred_idx not in completed_tasks:
                        all_deps_completed = False
                        break
                    else:
                        # 累计前序任务的输出token作为当前任务的输入
                        total_input_tokens += self._task_predicted_output_tokens.get(pred_idx, 0)

            if all_deps_completed:
                # 更新任务的输入token
                self._task_input_tokens[task_idx] = total_input_tokens

                # 如果是LLM任务，从文件中读取真实的输出token和内存需求
                if self._step_state['task_features'][2, task_idx] == 1:  # LLM任务
                    # 从原始任务特征文件中读取真实的输出token和内存需求
                    predicted_output = self._get_llm_output_tokens_from_file(task_idx, total_input_tokens)
                    predicted_memory = self._get_llm_memory_from_file(task_idx, total_input_tokens, predicted_output)

                    self._task_predicted_output_tokens[task_idx] = predicted_output
                    self._task_predicted_memory[task_idx] = predicted_memory

                    if self.debug:
                        print(f"[LLM_PREDICT] 任务{task_idx}: 输入{total_input_tokens}tokens -> "
                              f"输出{predicted_output}tokens, 内存{predicted_memory:.2f}GB")

                # 更新状态为"信息完整/就绪可调度"
                if self._task_status[task_idx] == 0:  # 从未就绪状态转换
                    self._task_status[task_idx] = 1
                    newly_ready_tasks.append(task_idx)

                    if self.debug:
                        print(f"[TASK_READY] 任务{task_idx}状态更新为就绪可调度")

        return newly_ready_tasks

    def get_ready_tasks(self):
        """获取当前就绪可调度的任务列表"""
        ready_tasks = []
        for task_idx, status in self._task_status.items():
            if status == 1:  # 信息完整/就绪可调度
                ready_tasks.append(task_idx)
        return ready_tasks

    def _get_llm_output_tokens_from_file(self, task_idx, input_tokens):
        """从任务特征文件中获取LLM任务的预测输出token数"""
        if self._original_task_features is not None:
            # 🔥 关键修复：直接使用文件中的token数，不进行计算
            # 文件中的Token_Count列包含的是预设的输出token数
            file_tokens = self._original_task_features[3, task_idx]

            # 确保token数在合理范围内（不超过2000，如用户所说）
            predicted_output_tokens = min(file_tokens, 2000.0)

            # 确保最少有一些token（但不要太高）
            predicted_output_tokens = max(predicted_output_tokens, 10.0)

            if self.debug and input_tokens > 10000:
                print(f"[TOKEN_DEBUG] 任务{task_idx}: 输入{input_tokens}tokens过大，使用文件预设{predicted_output_tokens}tokens")

            if self.debug:
                print(f"[TOKEN_FROM_FILE] 任务{task_idx}: 直接使用文件token数{predicted_output_tokens}")

            return max(predicted_output_tokens, 0)  # 确保非负

        # 如果没有原始特征数据，使用配置中的预测方法作为后备
        return self.config.predict_llm_output_tokens(input_tokens)

    def _get_llm_memory_from_file(self, task_idx, input_tokens, output_tokens):
        """从任务特征文件中获取LLM任务的预测运行时内存需求"""
        if self._original_task_features is not None:
            # 直接使用文件中的内存需求作为预测器的输出
            # 假设xlsx文件中的Memory_Req列包含的是预测器预测的运行时内存需求
            predicted_memory_gb = self._original_task_features[4, task_idx]
            return max(predicted_memory_gb, 0)  # 确保非负

        # 如果没有原始特征数据，使用配置中的预测方法作为后备
        return self.config.predict_llm_runtime_memory(input_tokens, output_tokens)

    def process_state_as_enhanced_sequence(self, task_features, adj_matrix, machine_resources,
                                         comm_speed, memory_status=None):
        """
        处理增强的序列化状态，包含动态特征

        返回:
            增强的序列化状态 [序列长度, 增强特征维度]
        """
        num_tasks = adj_matrix.shape[0]
        num_machines = len(machine_resources)

        if memory_status is None:
            memory_status = self.load_memory_status()

        # 归一化处理
        max_cpu = np.max(task_features[0]) if np.max(task_features[0]) > 0 else 1.0
        normalized_cpu = task_features[0] / (max_cpu + 1e-8)

        max_transfer = np.max(task_features[1]) if np.max(task_features[1]) > 0 else 1.0
        normalized_transfer = task_features[1] / (max_transfer + 1e-8)

        max_comm = np.max(comm_speed) if np.max(comm_speed) > 0 else 1.0
        normalized_comm = comm_speed / (max_comm + 1e-8)

        normalized_memory = memory_status / self.config.MEMORY_LIMIT

        max_compute = np.max(machine_resources) if np.max(machine_resources) > 0 else 1.0
        normalized_compute = machine_resources / (max_compute + 1e-8)

        # 获取DAG结构信息
        predecessors, successors = self._get_dag_structure(adj_matrix, num_tasks)

        # 构建增强序列状态
        sequence_length = self.config.SEQ_LEN
        feature_dim = self.config.ENHANCED_STATE_DIM
        sequence_state = np.zeros((sequence_length, feature_dim))

        for i in range(min(num_tasks, sequence_length)):
            idx = 0

            # 1. 静态任务特征 (3维)
            sequence_state[i, idx] = normalized_cpu[i]
            sequence_state[i, idx+1] = normalized_transfer[i]
            sequence_state[i, idx+2] = task_features[2, i]  # 是否LLM
            idx += 3

            # 2. 动态特征 (6维)
            # 任务状态one-hot编码 (4维)
            task_status = self._task_status.get(i, 0)
            status_onehot = self.config.create_task_status_onehot(task_status)
            sequence_state[i, idx:idx+4] = status_onehot
            idx += 4

            # 预估输出token数量 (1维)
            predicted_tokens = self._task_predicted_output_tokens.get(i, 0)
            sequence_state[i, idx] = self.config.normalize_tokens(predicted_tokens)
            idx += 1

            # 预估运行时内存 (1维)
            predicted_memory = self._task_predicted_memory.get(i, 0)
            sequence_state[i, idx] = self.config.normalize_memory(predicted_memory)
            idx += 1

            # 3. 全局系统特征（与原实现保持一致）
            # 通信特征
            sequence_state[i, idx] = np.mean(normalized_comm)
            idx += 1

            # 内存状态
            mem_end = min(idx + num_machines, feature_dim - 1)
            sequence_state[i, idx:mem_end] = normalized_memory[:mem_end-idx]
            idx = mem_end

            # 计算能力
            compute_end = min(idx + num_machines, feature_dim - 1)
            sequence_state[i, idx:compute_end] = normalized_compute[:compute_end-idx]
            idx = compute_end

            # TTFT和TPOT（使用机器特定的真实值）
            # 获取机器特定的TTFT (base_execution_time) 和 TPOT (token_per_second)
            machine_ttft = self._get_machine_ttft_values()
            machine_tpot = self._get_machine_tpot_values()

            # 归一化TTFT值
            if machine_ttft is not None:
                max_ttft = np.max(machine_ttft) if np.max(machine_ttft) > 0 else 1.0
                normalized_ttft = machine_ttft / (max_ttft + 1e-8)
            else:
                normalized_ttft = np.full(num_machines, 0.5)  # 后备默认值

            ttft_end = min(idx + num_machines, feature_dim - 1)
            sequence_state[i, idx:ttft_end] = normalized_ttft[:ttft_end-idx]
            idx = ttft_end

            # 归一化TPOT值（注意：token_per_second越大越好，所以可能需要倒数）
            if machine_tpot is not None:
                # 使用倒数，因为token_per_second越大表示处理越快
                machine_tpot_inv = 1.0 / (machine_tpot + 1e-8)
                max_tpot_inv = np.max(machine_tpot_inv) if np.max(machine_tpot_inv) > 0 else 1.0
                normalized_tpot = machine_tpot_inv / (max_tpot_inv + 1e-8)
            else:
                normalized_tpot = np.full(num_machines, 0.5)  # 后备默认值

            tpot_end = min(idx + num_machines, feature_dim - 1)
            sequence_state[i, idx:tpot_end] = normalized_tpot[:tpot_end-idx]
            idx = tpot_end

            # DAG结构信息（简化处理）
            max_preds = min(3, len(predecessors[i]))
            for j in range(3):
                if idx < feature_dim - 1:
                    if j < max_preds:
                        sequence_state[i, idx] = predecessors[i][j] / num_tasks
                    idx += 1

            max_succs = min(3, len(successors[i]))
            for j in range(3):
                if idx < feature_dim - 1:
                    if j < max_succs:
                        sequence_state[i, idx] = successors[i][j] / num_tasks
                    idx += 1

            # 有效任务掩码
            if idx < feature_dim:
                sequence_state[i, -1] = 1.0

        if self.debug:
            print(f"[ENHANCED_STATE] 生成状态形状: {sequence_state.shape}")
            ready_count = sum(1 for status in self._task_status.values() if status == 1)
            print(f"[ENHANCED_STATE] 当前就绪任务数: {ready_count}")

        return sequence_state

    def _get_dag_structure(self, adj_matrix, num_tasks):
        """获取DAG结构信息"""
        predecessors = [[] for _ in range(num_tasks)]
        successors = [[] for _ in range(num_tasks)]

        for i in range(num_tasks):
            for j in range(num_tasks):
                if adj_matrix[j, i] == 1:  # j是i的前继
                    predecessors[i].append(j)
                if adj_matrix[i, j] == 1:  # j是i的后继
                    successors[i].append(j)

        return predecessors, successors

    def _get_machine_ttft_values(self):
        """获取机器特定的TTFT (base_execution_time) 值"""
        # 尝试从父类获取已加载的base_execution_time（不同版本可能有不同的属性名）
        if hasattr(self, 'base_execution_time') and self.base_execution_time is not None:
            return self.base_execution_time
        elif hasattr(self, 'machine_base_execution_time') and self.machine_base_execution_time is not None:
            return self.machine_base_execution_time

        # 如果没有，尝试从token_per_second属性推断（某些版本可能只有这个）
        if hasattr(self, 'token_per_second') and self.token_per_second is not None:
            # 使用配置中的默认base_execution_time
            num_machines = len(self.token_per_second)
            return np.full(num_machines, self.config.BASE_EXECUTION_TIME)

        # 最后的后备方案：使用配置默认值
        if hasattr(self, '_step_state') and self._step_state is not None:
            machine_resources = self._step_state.get('machine_resources')
            if machine_resources is not None:
                num_machines = len(machine_resources)
                return np.full(num_machines, self.config.BASE_EXECUTION_TIME)

        return None

    def _get_machine_tpot_values(self):
        """获取机器特定的TPOT (token_per_second) 值"""
        # 尝试从父类获取已加载的token_per_second
        if hasattr(self, 'token_per_second') and self.token_per_second is not None:
            return self.token_per_second

        # 后备方案：使用配置默认值
        if hasattr(self, '_step_state') and self._step_state is not None:
            machine_resources = self._step_state.get('machine_resources')
            if machine_resources is not None:
                num_machines = len(machine_resources)
                return np.full(num_machines, self.config.TOKEN_PER_SECOND)

        return None

    def _release_completed_tasks_memory(self):
        """
        释放已完成任务的内存

        这个方法会遍历所有已完成的任务，释放它们占用的内存，
        防止内存耗尽导致的死锁问题。
        """
        if self._step_state is None:
            return

        completed_tasks = self._step_state.get('completed_tasks', set())
        failed_tasks = self._step_state.get('failed_tasks', set())
        memory_status = self._step_state.get('memory_status')

        if memory_status is None:
            return

        # 获取已释放内存的任务集合，如果不存在则创建
        if 'memory_released_tasks' not in self._step_state:
            self._step_state['memory_released_tasks'] = set()

        memory_released_tasks = self._step_state['memory_released_tasks']

        # 遍历所有已完成和失败的任务
        all_finished_tasks = completed_tasks.union(failed_tasks)

        for task_idx in all_finished_tasks:
            # 如果任务内存已经释放，跳过
            if task_idx in memory_released_tasks:
                continue

            # 获取任务的预测内存需求
            predicted_memory = self._task_predicted_memory.get(task_idx, 0)

            if predicted_memory <= 0:
                continue

            # 获取任务执行的机器（需要从任务分配记录中获取）
            machine_idx = self._get_task_machine(task_idx)

            if machine_idx is None or machine_idx < 0 or machine_idx >= len(memory_status):
                if self.debug:
                    print(f"[MEMORY_RELEASE] 任务{task_idx}没有有效的机器分配，跳过内存释放")
                continue

            # 释放内存
            memory_status[machine_idx] += predicted_memory
            memory_released_tasks.add(task_idx)

            if self.debug:
                # 获取该机器的总内存
                total_memory = self._machine_total_memory.get(machine_idx, 32.0)
                used_memory = total_memory - memory_status[machine_idx]
                utilization = (used_memory / total_memory) * 100

                print(f"🔄 [内存释放] 任务{task_idx}完成，释放内存:")
                print(f"    释放内存: +{predicted_memory:.2f}GB")
                print(f"    机器{machine_idx}可用内存: {memory_status[machine_idx]:.2f}GB")
                print(f"    内存利用率: {utilization:.1f}%")

    def _get_task_machine(self, task_idx):
        """
        获取任务执行的机器索引

        优先使用task_machines映射，如果不存在则通过完成时间推断
        """
        # 方法1: 直接从task_machines映射获取（推荐方法）
        task_machines = self._step_state.get('task_machines', {})
        if task_idx in task_machines:
            return task_machines[task_idx]

        # 方法2: 通过完成时间推断（后备方法）
        if 'task_finish_time' not in self._step_state:
            return None

        task_finish_times = self._step_state['task_finish_time']

        # 处理不同的数据类型
        if isinstance(task_finish_times, dict):
            task_finish_time = task_finish_times.get(task_idx, 0)
        elif hasattr(task_finish_times, '__getitem__') and task_idx < len(task_finish_times):
            task_finish_time = task_finish_times[task_idx]
        else:
            return None

        if task_finish_time <= 0:
            return None

        # 检查机器完成时间，找到匹配的机器
        machine_finish_time = self._step_state.get('machine_finish_time')
        if machine_finish_time is None:
            return None

        # 寻找完成时间最接近的机器（允许一定误差）
        tolerance = 1.0  # 1毫秒的容差
        for machine_idx, machine_time in enumerate(machine_finish_time):
            if abs(machine_time - task_finish_time) <= tolerance:
                return machine_idx

        # 如果没找到精确匹配，返回None
        if self.debug:
            print(f"[MEMORY_RELEASE] 警告：无法确定任务{task_idx}的执行机器")
        return None

    def step_enhanced_task(self, compound_action, adj_matrix=None, task_features=None,
                          num_tasks=None, machine_resources=None, comm_speed=None,
                          memory_status=None, debug=True):
        """
        增强的逐任务调度方法，支持复合动作（任务选择+机器分配）

        参数:
            compound_action: 复合动作 [task_choice, machine_choice]
            其他参数与原step_task方法相同

        返回:
            next_state: 下一个增强状态
            reward: 奖励值
            done: 是否完成
            info: 信息字典
        """
        # 1. 初始化阶段
        if self._step_state is None:
            if adj_matrix is None or task_features is None:
                raise ValueError("首次调用时，adj_matrix和task_features不能为None")

            if num_tasks is None:
                num_tasks = adj_matrix.shape[0]

            if debug:
                print(f"[ENHANCED_STEP] 开始新DAG: 总任务数 = {num_tasks}")
                # 统计LLM任务数量
                llm_tasks = sum(1 for i in range(num_tasks) if task_features[2, i] == 1)
                print(f"[ENHANCED_STEP] LLM任务数量: {llm_tasks}/{num_tasks}")

            self._init_step_state(task_features, adj_matrix, machine_resources, comm_speed, memory_status)

            if not self.get_ready_tasks():
                raise ValueError("初始化后没有就绪任务")

        # 使用传入的调试标志
        debug = debug or self.debug

        # 获取当前状态
        adj_matrix = self._step_state['adj_matrix'] if adj_matrix is None else adj_matrix
        task_features = self._step_state['task_features'] if task_features is None else task_features
        num_tasks = adj_matrix.shape[0] if num_tasks is None else num_tasks
        memory_status = self._step_state['memory_status'] if memory_status is None else memory_status
        machine_resources = self._step_state['machine_resources'] if machine_resources is None else machine_resources
        comm_speed = self._step_state['comm_speed'] if comm_speed is None else comm_speed

        completed_tasks = self._step_state.get('completed_tasks', set())
        failed_tasks = self._step_state.get('failed_tasks', set())

        # 2. 检查是否已完成
        if len(completed_tasks) + len(failed_tasks) >= num_tasks:
            if debug:
                print(f"[ENHANCED_STEP] DAG完成! 总任务数: {num_tasks}, 完成: {len(completed_tasks)}, 失败: {len(failed_tasks)}")
                print(f"[ENHANCED_STEP] 总完成时间: {self._step_state['total_completion_time']/1000:.3f}秒")

            # 🔥 关键修复：对失败DAG设置makespan为1000秒的惩罚（与原版一致）
            total_time = self._step_state['total_completion_time']
            if len(completed_tasks) < num_tasks:
                # 如果DAG没有全部完成，设置一个较大的完成时间（1000秒 = 1000000毫秒）
                total_time = 1000000  # 1000秒，单位是毫秒
                if debug:
                    print(f"[TIME] DAG未完全完成，设置总完成时间为1000秒作为惩罚")

            next_state = self.process_state_as_enhanced_sequence(
                task_features, adj_matrix, machine_resources, comm_speed, memory_status)

            return next_state, 0, True, {
                "completed_tasks": completed_tasks,
                "total_time": total_time,  # 使用修正后的时间
                "dag_failed": self._step_state['dag_failed'],
                "ready_tasks": self.get_ready_tasks()
            }

        # 3. 解析复合动作
        if len(compound_action) != 2:
            raise ValueError(f"复合动作应包含2个元素，实际收到{len(compound_action)}个")

        task_choice_action, machine_choice_action = compound_action

        # 4. 任务选择
        ready_tasks = self.get_ready_tasks()
        if not ready_tasks:
            # 没有就绪任务，检查是否死锁
            if debug:
                print("[ENHANCED_STEP] 没有就绪任务，可能发生死锁")

            self._step_state['deadlock_detected'] = True
            self._step_state['dag_failed'] = True

            next_state = self.process_state_as_enhanced_sequence(
                task_features, adj_matrix, machine_resources, comm_speed, memory_status)

            return next_state, -10.0, True, {
                "completed_tasks": completed_tasks,
                "total_time": 1000000,  # 死锁也设置为1000秒惩罚
                "dag_failed": True,
                "deadlock_detected": True,
                "ready_tasks": []
            }

        # 将任务选择动作映射到就绪任务索引
        task_choice_idx = int(((task_choice_action + 1) / 2) * (len(ready_tasks) - 1))
        task_choice_idx = max(0, min(task_choice_idx, len(ready_tasks) - 1))
        selected_task_idx = ready_tasks[task_choice_idx]

        # 将机器选择动作映射到机器索引
        num_machines = len(machine_resources)
        machine_idx = int(((machine_choice_action + 1) / 2) * (num_machines - 1))
        machine_idx = max(0, min(machine_idx, num_machines - 1))

        if debug:
            print(f"\n📋 [调度决策] 步骤详情:")
            print(f"  选择任务: 任务{selected_task_idx} (就绪队列第{task_choice_idx}个)")
            print(f"  分配机器: 机器{machine_idx} ({'用户设备' if machine_idx == 0 else f'边缘设备{machine_idx}'})")
            print(f"  当前就绪任务: {ready_tasks}")
            print(f"  动作值: 任务选择={task_choice_action:.3f}, 机器选择={machine_choice_action:.3f}")

            # 打印详细的内存状态
            print(f"\n💾 [内存状态] 调度前:")
            for i, mem in enumerate(memory_status):
                machine_type = "用户设备" if i == 0 else f"边缘设备{i}"
                total_mem = self._machine_total_memory.get(i, 32.0)  # 使用真实的机器总内存
                used_mem = total_mem - mem
                utilization = (used_mem / total_mem) * 100
                print(f"    {machine_type}: {mem:.2f}GB可用 / {total_mem:.0f}GB总计 (已用{utilization:.1f}%)")

            # 打印任务详细信息
            selected_task_memory = self._task_predicted_memory.get(selected_task_idx, 0)
            selected_task_tokens = self._task_predicted_output_tokens.get(selected_task_idx, 0)
            is_llm = task_features[2, selected_task_idx] == 1

            print(f"\n🎯 [任务信息] 选中任务{selected_task_idx}:")
            print(f"    类型: {'LLM任务' if is_llm else '普通任务'}")
            print(f"    预测内存需求: {selected_task_memory:.2f}GB")
            print(f"    预测输出Token: {selected_task_tokens:.0f}")
            print(f"    目标机器可用内存: {memory_status[machine_idx]:.2f}GB")

            if selected_task_memory > memory_status[machine_idx]:
                print(f"    ⚠️  内存不足警告: 需要{selected_task_memory:.2f}GB > 可用{memory_status[machine_idx]:.2f}GB")
            else:
                print(f"    ✅ 内存充足: 需要{selected_task_memory:.2f}GB ≤ 可用{memory_status[machine_idx]:.2f}GB")

        # 5. 执行任务
        return self._execute_enhanced_task(selected_task_idx, machine_idx,
                                         task_features, adj_matrix, num_tasks,
                                         machine_resources, comm_speed, memory_status, debug)

    def _execute_enhanced_task(self, task_idx, machine_idx, task_features, adj_matrix,
                              num_tasks, machine_resources, comm_speed, memory_status, debug):
        """执行增强的任务调度"""

        # 获取任务信息
        cpu_cycles = task_features[0, task_idx]
        data_size = task_features[1, task_idx]
        is_llm = task_features[2, task_idx]

        # 使用预测的内存需求
        predicted_memory = self._task_predicted_memory.get(task_idx, task_features[4, task_idx])

        # 检查内存是否足够
        if memory_status[machine_idx] < predicted_memory:
            if debug:
                print(f"\n❌ [执行失败] 任务{task_idx}内存不足:")
                print(f"    需要内存: {predicted_memory:.2f}GB")
                print(f"    可用内存: {memory_status[machine_idx]:.2f}GB")
                print(f"    缺口: {predicted_memory - memory_status[machine_idx]:.2f}GB")

            # 内存不足，任务失败
            self._step_state['failed_tasks'].add(task_idx)
            self._task_status[task_idx] = 3  # 已完成/失败

            next_state = self.process_state_as_enhanced_sequence(
                task_features, adj_matrix, machine_resources, comm_speed, memory_status)

            return next_state, -5.0, False, {
                "completed_tasks": self._step_state.get('completed_tasks', set()),
                "failed_task": task_idx,
                "reason": "memory_insufficient",
                "ready_tasks": self.get_ready_tasks()
            }

        # 更新任务状态为运行中
        self._task_status[task_idx] = 2

        # 计算执行时间
        machine_finish_time = self._step_state.get('machine_finish_time',
                                                  np.zeros(len(machine_resources)))

        if machine_idx == 0:  # 本地执行
            cpu_freq = self.config.LOCAL_CPU_FREQ
            execution_time_sec = cpu_cycles / (cpu_freq * 1e9)

            if is_llm == 1:
                # 🔥 关键修复：使用预测的token数量和机器特定参数计算LLM执行时间
                predicted_tokens = self._task_predicted_output_tokens.get(task_idx, 0)
                # 使用机器特定的token处理速度和基础执行时间
                machine_token_per_second = self.token_per_second[machine_idx] if hasattr(self, 'token_per_second') else self.config.TOKEN_PER_SECOND
                machine_base_time = self.base_execution_time[machine_idx] if hasattr(self, 'base_execution_time') else self.config.BASE_EXECUTION_TIME

                # 🔥 修复：确保执行时间计算正确：基础时间 + 预估输出token数 * 每个token的延迟
                token_time_sec = predicted_tokens * machine_token_per_second
                execution_time_sec = (machine_base_time + token_time_sec) * self.config.LOCAL_PENALTY

                if self.debug:
                    print(f"[LLM_TIME_CALC] 任务{task_idx}本地执行时间计算:")
                    print(f"  预测输出tokens: {predicted_tokens}")
                    print(f"  基础时间: {machine_base_time:.3f}s")
                    print(f"  Token时间: {token_time_sec:.3f}s")
                    print(f"  总执行时间: {execution_time_sec:.3f}s")

            execution_time = execution_time_sec * 1000  # 转换为毫秒
            finish_time = machine_finish_time[machine_idx] + execution_time

        else:  # 边缘执行
            cpu_freq = machine_resources[machine_idx]

            # 计算上传时间
            upload_speed = comm_speed[0, machine_idx]
            upload_time = (data_size / upload_speed * 1000) if upload_speed > 0 else 0

            # 计算执行时间
            execution_time_sec = cpu_cycles / (cpu_freq * 1e9)

            if is_llm == 1:
                # 🔥 关键修复：使用预测的token数量和机器特定参数计算LLM执行时间
                predicted_tokens = self._task_predicted_output_tokens.get(task_idx, 0)
                # 使用机器特定的token处理速度和基础执行时间
                machine_token_per_second = self.token_per_second[machine_idx] if hasattr(self, 'token_per_second') else self.config.TOKEN_PER_SECOND
                machine_base_time = self.base_execution_time[machine_idx] if hasattr(self, 'base_execution_time') else self.config.BASE_EXECUTION_TIME

                # 🔥 修复：确保执行时间计算正确：基础时间 + 预估输出token数 * 每个token的延迟
                token_time_sec = predicted_tokens * machine_token_per_second
                execution_time_sec = machine_base_time + token_time_sec

                if self.debug:
                    print(f"[LLM_TIME_CALC] 任务{task_idx}边缘执行时间计算:")
                    print(f"  预测输出tokens: {predicted_tokens}")
                    print(f"  基础时间: {machine_base_time:.3f}s")
                    print(f"  Token时间: {token_time_sec:.3f}s")
                    print(f"  总执行时间: {execution_time_sec:.3f}s")

            execution_time = execution_time_sec * 1000

            # 计算下载时间
            download_speed = comm_speed[machine_idx, 0]
            download_time = (data_size / download_speed * 1000) if download_speed > 0 else 0

            finish_time = machine_finish_time[machine_idx] + upload_time + execution_time + download_time

        # 更新状态
        if debug:
            print(f"\n⚡ [开始执行] 任务{task_idx}:")
            print(f"    执行前机器{machine_idx}内存: {memory_status[machine_idx]:.2f}GB")
            print(f"    任务内存需求: {predicted_memory:.2f}GB")

            # 打印执行时间详情
            if machine_idx == 0:  # 本地执行
                print(f"    执行位置: 用户设备 (本地)")
                if is_llm == 1:
                    predicted_tokens = self._task_predicted_output_tokens.get(task_idx, 0)
                    machine_token_per_second = self.token_per_second[machine_idx] if hasattr(self, 'token_per_second') else self.config.TOKEN_PER_SECOND
                    machine_base_time = self.base_execution_time[machine_idx] if hasattr(self, 'base_execution_time') else self.config.BASE_EXECUTION_TIME
                    token_time = predicted_tokens * machine_token_per_second
                    print(f"    LLM执行时间: 基础{machine_base_time:.3f}s + Token时间{token_time:.3f}s")
                    print(f"    预测输出Token: {predicted_tokens:.0f}")
                    print(f"    机器{machine_idx}参数: token_per_second={machine_token_per_second:.4f}, base_time={machine_base_time:.3f}")
                print(f"    预计完成时间: {finish_time/1000:.3f}秒")
            else:  # 边缘执行
                print(f"    执行位置: 边缘设备{machine_idx}")
                print(f"    上传时间: {upload_time/1000:.3f}秒")
                print(f"    执行时间: {execution_time/1000:.3f}秒")
                print(f"    下载时间: {download_time/1000:.3f}秒")
                print(f"    总时间: {(upload_time + execution_time + download_time)/1000:.3f}秒")
                print(f"    预计完成时间: {finish_time/1000:.3f}秒")

        memory_status[machine_idx] -= predicted_memory  # 分配内存
        machine_finish_time[machine_idx] = finish_time
        self._step_state['task_finish_time'][task_idx] = finish_time
        self._step_state['completed_tasks'].add(task_idx)
        self._task_status[task_idx] = 3  # 已完成

        # 记录任务-机器映射，用于后续内存释放
        if 'task_machines' not in self._step_state:
            self._step_state['task_machines'] = {}
        self._step_state['task_machines'][task_idx] = machine_idx

        if debug:
            print(f"\n✅ [执行完成] 任务{task_idx}:")
            print(f"    执行后机器{machine_idx}内存: {memory_status[machine_idx]:.2f}GB")
            print(f"    内存分配: -{predicted_memory:.2f}GB")
            print(f"    任务-机器映射已记录: 任务{task_idx} -> 机器{machine_idx}")
            print(f"    任务状态更新: 运行中 -> 已完成")

        # 🔥 关键修复：立即释放已完成任务的内存，确保内存可以被后续任务使用
        self._release_completed_tasks_memory()

        # 更新总完成时间
        self._step_state['total_completion_time'] = max(
            self._step_state['total_completion_time'], finish_time)

        # 更新任务就绪状态
        newly_ready = self._update_task_readiness(adj_matrix,
                                                 self._step_state['completed_tasks'], num_tasks)

        # 计算奖励
        reward = self._compute_enhanced_reward(task_idx, finish_time, is_llm, predicted_memory)

        # 检查是否完成
        completed_tasks = self._step_state['completed_tasks']
        failed_tasks = self._step_state.get('failed_tasks', set())
        done = len(completed_tasks) + len(failed_tasks) >= num_tasks

        # 获取下一个状态
        next_state = self.process_state_as_enhanced_sequence(
            task_features, adj_matrix, machine_resources, comm_speed, memory_status)

        if debug:
            print(f"\n📊 [调度结果] 任务{task_idx}执行总结:")
            print(f"    完成时间: {finish_time/1000:.3f}秒")
            print(f"    步骤奖励: {reward:.2f}")
            # 安全处理newly_ready的显示
            if hasattr(newly_ready, '__len__') and len(newly_ready) > 0:
                print(f"    新增就绪任务: {newly_ready}")
            else:
                print(f"    新增就绪任务: 无")
            print(f"    总完成进度: {len(completed_tasks)}/{num_tasks} ({len(completed_tasks)/num_tasks:.1%})")

            # 打印当前内存状态
            print(f"\n💾 [内存状态] 执行后:")
            for i, mem in enumerate(memory_status):
                machine_type = "用户设备" if i == 0 else f"边缘设备{i}"
                total_mem = self._machine_total_memory.get(i, 32.0)  # 使用真实的机器总内存
                used_mem = total_mem - mem
                utilization = (used_mem / total_mem) * 100
                print(f"    {machine_type}: {mem:.2f}GB可用 (已用{utilization:.1f}%)")

            if done:
                total_time = self._step_state['total_completion_time'] / 1000.0
                print(f"\n🏁 [DAG完成] 所有任务执行完毕:")
                print(f"    总Makespan: {total_time:.3f}秒")
                print(f"    成功完成: {len(completed_tasks)}个任务")
                print(f"    失败任务: {len(failed_tasks)}个任务")
                print(f"    完成率: {len(completed_tasks)/num_tasks:.1%}")

        return next_state, reward, done, {
            "completed_tasks": completed_tasks,
            "total_time": self._step_state['total_completion_time'],
            "total_reward": self._step_state.get('total_reward', 0),  # 添加总奖励
            "executed_task": task_idx,
            "assigned_machine": machine_idx,  # 添加分配的机器信息
            "execution_time": finish_time,
            "newly_ready_tasks": newly_ready,
            "ready_tasks": self.get_ready_tasks(),
            "dag_failed": self._step_state.get('dag_failed', False),
            "deadlock_detected": self._step_state.get('deadlock_detected', False)
        }

    def _compute_enhanced_reward(self, task_idx, completion_time, is_llm, predicted_memory):
        """计算增强的奖励函数 - 更接近原版逻辑"""
        # 将毫秒转换为秒
        completion_time_sec = completion_time / 1000.0

        # 获取当前状态信息
        completed_tasks = self._step_state.get('completed_tasks', set())
        failed_tasks = self._step_state.get('failed_tasks', set())

        # 计算任务总数（从adj_matrix获取）
        num_tasks = self._step_state['adj_matrix'].shape[0]
        valid_tasks = num_tasks - len(failed_tasks)

        # 1. 时间奖励 - 与原版一致
        time_reward = -completion_time_sec / 10.0

        # 2. 内存奖励 - LLM任务的内存使用奖励
        memory_reward = 0
        if np.isscalar(is_llm):
            is_llm_task = (is_llm == 1)
        else:
            is_llm_task = np.any(is_llm == 1)

        if is_llm_task:
            # 内存效率奖励：内存使用越少奖励越高
            memory_reward = 1.0 / (predicted_memory + 1.0)

        # 3. 进度奖励 - 与原版一致
        completion_ratio = len(completed_tasks) / max(1, valid_tasks) if valid_tasks > 0 else 0.0
        progress_reward = completion_ratio * 5.0

        # 4. 失败任务惩罚 - 与原版一致
        failure_penalty = 0
        if len(failed_tasks) > 0:
            failure_ratio = len(failed_tasks) / num_tasks
            failure_penalty = -failure_ratio * 20.0

        # 5. LLM任务类型奖励
        llm_bonus = 5.0 if is_llm_task else 0.0

        # 6. 完成奖励 - 只有全部完成时才给予
        completion_bonus = 0
        if len(completed_tasks) == valid_tasks and valid_tasks > 0:
            completion_bonus = 50.0

        # 组合奖励
        reward = time_reward + memory_reward + progress_reward + failure_penalty + llm_bonus

        # 添加完成奖励
        if len(completed_tasks) == valid_tasks and valid_tasks > 0:
            reward += completion_bonus

        # 更新总奖励
        if 'total_reward' not in self._step_state:
            self._step_state['total_reward'] = 0
        self._step_state['total_reward'] += reward

        # 更新完成率
        self._step_state['completion_ratio'] = completion_ratio

        if self.debug:
            print(f"💰 [奖励计算] 任务{task_idx}:")
            print(f"    时间奖励: {time_reward:.2f}")
            print(f"    内存奖励: {memory_reward:.2f}")
            print(f"    进度奖励: {progress_reward:.2f}")
            print(f"    失败惩罚: {failure_penalty:.2f}")
            print(f"    LLM奖励: {llm_bonus:.2f}")
            print(f"    完成奖励: {completion_bonus:.2f}")
            print(f"    总奖励: {reward:.2f}")
            print(f"    已完成: {len(completed_tasks)}/{valid_tasks}, 失败: {len(failed_tasks)}/{num_tasks}")

        return np.clip(reward, -10.0, 60.0)  # 调整上限以容纳完成奖励
