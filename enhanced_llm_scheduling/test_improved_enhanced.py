#!/usr/bin/env python3
"""
测试改进后的Enhanced调度系统
基于原版逻辑优化奖励函数和训练过程
"""

import torch
import numpy as np
import argparse
import time
from enhanced_data_loader import EnhancedDataLoader
from enhanced_config import EnhancedLLMConfig
from enhanced_sac_agent import EnhancedSACA<PERSON>
# from enhanced_replay_buffer import EnhancedReplayBuffer  # 智能体已包含

def test_improved_enhanced():
    """测试改进后的Enhanced调度系统"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--device_num", default=5, type=int, help="边缘设备数量")
    parser.add_argument("--data_path", default="../dataset/training/DAG_30_edges_5_mem_57-64GB_density_0.2_0.6",
                       type=str, help="数据路径")
    parser.add_argument("--episodes", default=100, type=int, help="训练回合数")
    parser.add_argument("--batch_size", default=64, type=int, help="批次大小")
    parser.add_argument("--learning_rate", default=1e-3, type=float, help="学习率")
    parser.add_argument("--device", default="cuda", type=str, help="计算设备")
    args = parser.parse_args()

    print("🚀 测试改进后的Enhanced调度系统")
    print(f"参数: {args}")

    # 创建配置
    config = EnhancedLLMConfig()
    config.update_edge_servers(args.device_num)

    # 如果指定了自定义数据路径，更新配置
    if args.data_path != config.TRAINING_DATA_PATH:
        config.TRAINING_DATA_PATH = args.data_path
        print(f"[ENHANCED_CONFIG] 已设置自定义数据路径: {args.data_path}")

    # 创建数据加载器
    data_loader = EnhancedDataLoader(config)
    data_loader.set_debug(True)  # 开启调试模式

    # 创建智能体
    agent = EnhancedSACAgent(
        config=config,
        device=args.device
    )

    # 智能体已包含经验回放缓冲区

    print(f"📊 配置信息:")
    print(f"  边缘设备数: {args.device_num}")
    print(f"  状态维度: {config.ENHANCED_STATE_DIM}")
    print(f"  动作维度: {config.COMPOUND_ACTION_DIM}")
    print(f"  序列长度: {config.MAX_TASKS_NUM}")
    print(f"  计算设备: {args.device}")

    # 训练统计
    episode_rewards = []
    completion_rates = []
    makespans = []

    print(f"\n🎯 开始训练...")
    start_time = time.time()

    for episode in range(args.episodes):
        # 随机选择一个DAG
        dag_idx = np.random.randint(0, data_loader.config.NUM_STEPS)

        # 加载DAG数据
        task_features = data_loader.load_task_features(dag_idx)
        adj_matrix = data_loader.load_adjacency_matrix(dag_idx)
        machine_resources = data_loader.load_machines_resource()
        comm_speed = data_loader.load_machine_commu_speed()
        memory_status = data_loader.load_memory_status()

        num_tasks = task_features.shape[1]

        # 重置环境
        data_loader._step_state = None

        # 获取初始状态
        state = data_loader.process_state_as_enhanced_sequence(
            task_features, adj_matrix, machine_resources, comm_speed, memory_status
        )

        episode_reward = 0
        step_count = 0
        done = False

        # 逐步调度
        while not done and step_count < num_tasks * 2:  # 防止无限循环
            # 选择动作
            compound_action = agent.select_action(state, deterministic=False)

            # 执行动作
            next_state, reward, done, info = data_loader.step_enhanced_task(
                compound_action=compound_action,
                adj_matrix=adj_matrix,
                task_features=task_features,
                num_tasks=num_tasks,
                machine_resources=machine_resources,
                comm_speed=comm_speed,
                memory_status=memory_status,
                debug=(episode % 20 == 0)  # 每20个episode详细调试
            )

            # 存储经验
            agent.store_transition(state, compound_action, reward, next_state, done)

            # 更新状态和奖励
            state = next_state
            episode_reward += reward
            step_count += 1

            # 训练智能体
            if len(agent.replay_buffer) > args.batch_size:
                train_info = agent.update()

                # 每100步打印训练信息
                if step_count % 100 == 0:
                    print(f"  训练信息 - Critic Loss: {train_info.get('critic_loss', 0):.4f}, "
                          f"Actor Loss: {train_info.get('actor_loss', 0):.4f}")

        # 记录统计信息
        completed_tasks = info.get("completed_tasks", set())
        total_time = info.get("total_time", 0) / 1000.0  # 转换为秒
        completion_rate = len(completed_tasks) / num_tasks

        episode_rewards.append(episode_reward)
        completion_rates.append(completion_rate)
        makespans.append(total_time)

        # 打印进度
        if (episode + 1) % 10 == 0 or episode == 0:
            avg_reward = np.mean(episode_rewards[-10:])
            avg_completion = np.mean(completion_rates[-10:])
            avg_makespan = np.mean(makespans[-10:])

            elapsed_time = time.time() - start_time
            print(f"Episode {episode+1}/{args.episodes} | "
                  f"奖励: {episode_reward:.2f} (平均: {avg_reward:.2f}) | "
                  f"完成率: {completion_rate:.1%} (平均: {avg_completion:.1%}) | "
                  f"Makespan: {total_time:.2f}s (平均: {avg_makespan:.2f}s) | "
                  f"步数: {step_count} | "
                  f"用时: {elapsed_time:.1f}s")

            # 打印完成的任务
            if len(completed_tasks) > 0:
                print(f"  完成任务: {sorted(list(completed_tasks))}")

    # 训练完成统计
    print(f"\n📈 训练完成统计:")
    print(f"  总回合数: {args.episodes}")
    print(f"  平均奖励: {np.mean(episode_rewards):.2f}")
    print(f"  平均完成率: {np.mean(completion_rates):.1%}")
    print(f"  平均Makespan: {np.mean(makespans):.2f}秒")
    print(f"  完成率 >= 90%的回合: {sum(1 for r in completion_rates if r >= 0.9)}/{args.episodes}")
    print(f"  完成率 = 100%的回合: {sum(1 for r in completion_rates if r == 1.0)}/{args.episodes}")

    # 最近10个回合的性能
    if len(episode_rewards) >= 10:
        recent_rewards = episode_rewards[-10:]
        recent_completion = completion_rates[-10:]
        recent_makespan = makespans[-10:]

        print(f"\n📊 最近10回合性能:")
        print(f"  平均奖励: {np.mean(recent_rewards):.2f}")
        print(f"  平均完成率: {np.mean(recent_completion):.1%}")
        print(f"  平均Makespan: {np.mean(recent_makespan):.2f}秒")
        print(f"  完成率 >= 90%: {sum(1 for r in recent_completion if r >= 0.9)}/10")
        print(f"  完成率 = 100%: {sum(1 for r in recent_completion if r == 1.0)}/10")

if __name__ == "__main__":
    test_improved_enhanced()
