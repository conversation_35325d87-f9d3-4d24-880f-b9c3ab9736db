"""
简化的事件驱动系统
参考别人的实现，支持真实的时间推进和任务调度
"""

import heapq
from enum import Enum
from dataclasses import dataclass
from typing import Dict, List, Set, Optional, Tuple
import numpy as np

class EventType(Enum):
    """事件类型"""
    TASK_START = "task_start"
    TASK_COMPLETE = "task_complete"
    MEMORY_RELEASE = "memory_release"

@dataclass
class Event:
    """事件数据结构"""
    time: float
    event_type: EventType
    task_id: int
    machine_id: int
    priority: int = 0  # 优先级，数值越小优先级越高
    
    def __lt__(self, other):
        """用于堆排序的比较函数"""
        if self.time != other.time:
            return self.time < other.time
        return self.priority < other.priority

class EventQueue:
    """事件队列"""
    
    def __init__(self):
        self.events = []
        self.current_time = 0.0
    
    def add_event(self, event: Event):
        """添加事件到队列"""
        heapq.heappush(self.events, event)
    
    def get_next_event(self) -> Optional[Event]:
        """获取下一个事件"""
        if self.events:
            return heapq.heappop(self.events)
        return None
    
    def peek_next_event(self) -> Optional[Event]:
        """查看下一个事件但不移除"""
        if self.events:
            return self.events[0]
        return None
    
    def has_events(self) -> bool:
        """检查是否还有事件"""
        return len(self.events) > 0
    
    def clear(self):
        """清空事件队列"""
        self.events.clear()
        self.current_time = 0.0

class SimplifiedMachine:
    """简化的机器模型"""
    
    def __init__(self, machine_id: int, total_memory: float, cpu_frequency: float, 
                 is_user_device: bool = False):
        self.machine_id = machine_id
        self.total_memory = total_memory
        self.available_memory = total_memory
        self.cpu_frequency = cpu_frequency
        self.is_user_device = is_user_device
        
        # 运行中的任务
        self.running_tasks: Dict[int, Tuple[float, float]] = {}  # task_id -> (start_time, finish_time)
        
        # LLM参数
        self.token_per_second = 0.013 if is_user_device else 0.1
        self.base_execution_time = 0.23 if is_user_device else 0.046
    
    def can_accept_task(self, memory_required: float) -> bool:
        """检查是否可以接受任务"""
        return self.available_memory >= memory_required
    
    def allocate_memory(self, task_id: int, memory_required: float, 
                       start_time: float, finish_time: float) -> bool:
        """分配内存给任务"""
        if not self.can_accept_task(memory_required):
            return False
        
        self.available_memory -= memory_required
        self.running_tasks[task_id] = (start_time, finish_time)
        return True
    
    def release_memory(self, task_id: int, memory_to_release: float):
        """释放任务内存"""
        if task_id in self.running_tasks:
            del self.running_tasks[task_id]
        self.available_memory += memory_to_release
        # 确保不超过总内存
        self.available_memory = min(self.available_memory, self.total_memory)
    
    def get_memory_utilization(self) -> float:
        """获取内存利用率"""
        return 1.0 - (self.available_memory / self.total_memory)
    
    def calculate_execution_time(self, is_llm: bool, predicted_tokens: float = 0) -> float:
        """计算任务执行时间"""
        if is_llm:
            # LLM任务执行时间 = 基础时间 + token时间
            token_time = predicted_tokens * self.token_per_second
            return self.base_execution_time + token_time
        else:
            # 普通任务执行时间（简化）
            base_time = 1.0  # 基础执行时间1秒
            # 根据CPU频率调整
            if self.is_user_device:
                return base_time * 10  # 用户设备慢10倍
            else:
                return base_time / (self.cpu_frequency / 2.4)  # 相对于基准频率2.4GHz

class SimplifiedSchedulingEnvironment:
    """简化的调度环境"""
    
    def __init__(self, config):
        self.config = config
        self.event_queue = EventQueue()
        self.machines: List[SimplifiedMachine] = []
        self.current_time = 0.0
        
        # 任务状态
        self.task_status: Dict[int, str] = {}  # "ready", "running", "completed"
        self.task_dependencies: Dict[int, Set[int]] = {}
        self.task_features: Dict[int, Dict] = {}
        
        # 统计信息
        self.completed_tasks: Set[int] = set()
        self.total_makespan = 0.0
        self.total_memory_usage = 0.0
        
        # 奖励归一化
        self.episode_count = 0
        
        print(f"[SIMPLIFIED_ENV] 初始化完成")
    
    def reset(self, task_features, adj_matrix, machine_resources):
        """重置环境"""
        # 清空状态
        self.event_queue.clear()
        self.current_time = 0.0
        self.task_status.clear()
        self.task_dependencies.clear()
        self.task_features.clear()
        self.completed_tasks.clear()
        self.total_makespan = 0.0
        self.total_memory_usage = 0.0
        
        # 初始化机器
        self.machines.clear()
        for i, memory in enumerate(machine_resources):
            is_user_device = (i == 0)
            cpu_freq = 2.4 if is_user_device else np.random.uniform(10, 15)
            machine = SimplifiedMachine(i, memory, cpu_freq, is_user_device)
            self.machines.append(machine)
        
        # 初始化任务
        num_tasks = task_features.shape[1]
        for task_id in range(num_tasks):
            self.task_features[task_id] = {
                'is_llm': task_features[2, task_id] == 1,
                'memory_required': task_features[0, task_id],
                'predicted_tokens': task_features[1, task_id] if task_features[2, task_id] == 1 else 0
            }
            
            # 初始化依赖关系
            dependencies = set()
            for dep_id in range(num_tasks):
                if adj_matrix[dep_id, task_id] == 1:
                    dependencies.add(dep_id)
            self.task_dependencies[task_id] = dependencies
            
            # 初始化任务状态
            if len(dependencies) == 0:
                self.task_status[task_id] = "ready"
            else:
                self.task_status[task_id] = "waiting"
        
        return self._get_observation()
    
    def get_ready_tasks(self) -> List[int]:
        """获取就绪任务列表"""
        return [task_id for task_id, status in self.task_status.items() if status == "ready"]
    
    def step(self, action: int) -> Tuple[np.ndarray, float, bool, Dict]:
        """执行一步调度"""
        # 获取就绪任务
        ready_tasks = self.get_ready_tasks()
        
        if not ready_tasks:
            # 没有就绪任务，处理事件直到有新的就绪任务或结束
            self._process_events_until_ready()
            ready_tasks = self.get_ready_tasks()
            
            if not ready_tasks:
                # 仍然没有就绪任务，episode结束
                done = True
                reward = self._calculate_final_reward()
                return self._get_observation(), reward, done, self._get_info()
        
        # 选择第一个就绪任务（简化策略）
        selected_task = ready_tasks[0]
        
        # 检查选择的机器是否可以执行任务
        if action >= len(self.machines):
            action = 0  # 默认选择用户设备
        
        machine = self.machines[action]
        task_info = self.task_features[selected_task]
        
        if not machine.can_accept_task(task_info['memory_required']):
            # 机器资源不足，给予惩罚
            reward = -10.0 if not self.config.REWARD_ONLY_AT_END else 0.0
            return self._get_observation(), reward, False, self._get_info()
        
        # 执行任务调度
        self._schedule_task(selected_task, action)
        
        # 处理事件直到下一个就绪任务
        self._process_events_until_ready()
        
        # 检查是否完成
        done = len(self.completed_tasks) == len(self.task_features)
        
        # 计算奖励
        if done:
            reward = self._calculate_final_reward()
        else:
            reward = 0.0 if self.config.REWARD_ONLY_AT_END else self._calculate_step_reward(selected_task, action)
        
        return self._get_observation(), reward, done, self._get_info()
    
    def _schedule_task(self, task_id: int, machine_id: int):
        """调度任务到指定机器"""
        machine = self.machines[machine_id]
        task_info = self.task_features[task_id]
        
        # 计算执行时间
        execution_time = machine.calculate_execution_time(
            task_info['is_llm'], 
            task_info['predicted_tokens']
        )
        
        # 计算开始和结束时间
        start_time = self.current_time
        finish_time = start_time + execution_time
        
        # 分配资源
        machine.allocate_memory(task_id, task_info['memory_required'], start_time, finish_time)
        
        # 更新任务状态
        self.task_status[task_id] = "running"
        
        # 添加完成事件
        self.event_queue.add_event(Event(
            time=finish_time,
            event_type=EventType.TASK_COMPLETE,
            task_id=task_id,
            machine_id=machine_id
        ))
        
        print(f"[SCHEDULE] 任务{task_id} -> 机器{machine_id}, 执行时间: {execution_time:.2f}s")
    
    def _process_events_until_ready(self):
        """处理事件直到有新的就绪任务"""
        while self.event_queue.has_events():
            event = self.event_queue.get_next_event()
            self.current_time = event.time
            
            if event.event_type == EventType.TASK_COMPLETE:
                self._handle_task_completion(event.task_id, event.machine_id)
                
                # 检查是否有新的就绪任务
                if self.get_ready_tasks():
                    break
    
    def _handle_task_completion(self, task_id: int, machine_id: int):
        """处理任务完成"""
        # 更新任务状态
        self.task_status[task_id] = "completed"
        self.completed_tasks.add(task_id)
        
        # 释放机器资源
        machine = self.machines[machine_id]
        task_info = self.task_features[task_id]
        machine.release_memory(task_id, task_info['memory_required'])
        
        # 更新依赖任务状态
        for dependent_task, dependencies in self.task_dependencies.items():
            if task_id in dependencies and self.task_status[dependent_task] == "waiting":
                # 检查所有依赖是否都完成
                if all(dep_id in self.completed_tasks for dep_id in dependencies):
                    self.task_status[dependent_task] = "ready"
        
        print(f"[COMPLETE] 任务{task_id}完成，时间: {self.current_time:.2f}s")
    
    def _calculate_final_reward(self) -> float:
        """计算最终奖励"""
        # 计算makespan
        makespan = self.current_time
        
        # 计算平均内存利用率
        avg_memory_utilization = np.mean([m.get_memory_utilization() for m in self.machines])
        
        # 更新归一化边界
        self.config.update_reward_bounds(makespan, avg_memory_utilization)
        
        # 计算归一化奖励
        reward = self.config.get_normalized_reward(makespan, avg_memory_utilization)
        
        self.total_makespan = makespan
        self.total_memory_usage = avg_memory_utilization
        
        return reward
    
    def _calculate_step_reward(self, task_id: int, machine_id: int) -> float:
        """计算步骤奖励（如果不使用稀疏奖励）"""
        # 简化的步骤奖励
        machine = self.machines[machine_id]
        if machine.is_user_device:
            return -1.0  # 惩罚使用用户设备
        else:
            return 0.1   # 奖励使用边缘设备
    
    def _get_observation(self) -> np.ndarray:
        """获取当前状态观察"""
        # 简化的状态表示
        state = []
        
        # 机器状态
        for machine in self.machines:
            state.extend([
                machine.available_memory / machine.total_memory,  # 可用内存比例
                machine.get_memory_utilization(),                 # 内存利用率
                len(machine.running_tasks),                       # 运行任务数
                machine.cpu_frequency / 15.0,                     # 归一化CPU频率
            ])
        
        # 任务状态
        ready_count = len(self.get_ready_tasks())
        running_count = sum(1 for status in self.task_status.values() if status == "running")
        completed_count = len(self.completed_tasks)
        total_tasks = len(self.task_features)
        
        state.extend([
            ready_count / total_tasks,
            running_count / total_tasks,
            completed_count / total_tasks,
            self.current_time / 1000.0,  # 归一化时间
        ])
        
        # 填充到固定长度
        while len(state) < self.config.STATE_DIM:
            state.append(0.0)
        
        return np.array(state[:self.config.STATE_DIM], dtype=np.float32)
    
    def _get_info(self) -> Dict:
        """获取额外信息"""
        return {
            'current_time': self.current_time,
            'completed_tasks': len(self.completed_tasks),
            'total_tasks': len(self.task_features),
            'completion_rate': len(self.completed_tasks) / len(self.task_features),
            'makespan': self.total_makespan,
            'memory_utilization': self.total_memory_usage,
            'ready_tasks': self.get_ready_tasks()
        }
