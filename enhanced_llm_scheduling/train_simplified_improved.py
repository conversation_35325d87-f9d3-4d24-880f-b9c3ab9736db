#!/usr/bin/env python3
"""
改进的简化调度系统训练脚本
解决DAG多样性导致的学习困难问题
"""

import os
import sys
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
import argparse
from datetime import datetime
from collections import defaultdict

# 添加路径
sys.path.append('..')
sys.path.append('.')

from simplified_data_loader import SimplifiedDataLoader
from simplified_config import SimplifiedConfig
from simplified_event_system import SimplifiedSchedulingEnvironment
from simplified_sac_agent import SimplifiedSACAgent

def train_with_curriculum_learning(data_path, num_episodes=1000, save_interval=500,
                                 device_num=5, device='cuda'):
    """
    使用课程学习训练简化的调度系统
    解决DAG多样性问题的策略：
    1. 课程学习：从简单DAG开始，逐步增加复杂度
    2. 重复训练：对相同DAG多次训练
    3. 分层学习：按DAG复杂度分组
    """
    print("🚀 开始改进的简化调度系统训练")
    print("=" * 80)
    print("📋 改进策略:")
    print("  ✅ 课程学习 (从简单到复杂)")
    print("  ✅ 重复训练 (相同DAG多次训练)")
    print("  ✅ 分层学习 (按复杂度分组)")
    print("  ✅ 自适应采样 (根据性能调整)")
    print("=" * 80)

    # 创建配置
    config = SimplifiedConfig()
    config.update_edge_servers(device_num)
    config.set_data_path(data_path)

    # 创建数据加载器和环境
    data_loader = SimplifiedDataLoader(config)
    env = SimplifiedSchedulingEnvironment(config)
    agent = SimplifiedSACAgent(config, device=device)

    # 创建保存目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_dir = f"models/improved_simplified_sac_{device_num}devices_{timestamp}"
    os.makedirs(model_dir, exist_ok=True)

    # 分析DAG复杂度
    print("📊 分析DAG复杂度...")
    dag_complexity = analyze_dag_complexity(data_loader, num_dags=50)
    
    # 课程学习策略
    curriculum_phases = create_curriculum(dag_complexity, num_episodes)
    
    # 训练历史记录
    episode_rewards = []
    episode_completion_rates = []
    episode_makespans = []
    episode_dag_ids = []
    dag_performance = defaultdict(list)  # 记录每个DAG的性能
    
    successful_episodes = 0
    start_time = time.time()

    print(f"\n🎯 开始课程学习训练...")
    
    for episode in range(num_episodes):
        try:
            # 根据课程学习策略选择DAG
            dag_idx, phase_info = select_dag_by_curriculum(episode, curriculum_phases, dag_performance)
            
            # 加载DAG数据
            task_features = data_loader.load_task_features(dag_idx)
            adj_matrix = data_loader.load_adjacency_matrix(dag_idx)
            machine_resources = data_loader.load_memory_status()
            num_tasks = task_features.shape[1]

            # 重置环境
            state = env.reset(task_features, adj_matrix, machine_resources)

            episode_reward = 0
            step_count = 0
            max_steps = num_tasks * 3

            # 执行回合
            while step_count < max_steps:
                step_count += 1

                # 选择动作
                action = agent.select_action(state, deterministic=False)

                # 执行动作
                next_state, reward, done, info = env.step(action)

                # 存储经验
                agent.store_transition(state, action, reward, next_state, done)

                # 更新状态和奖励
                state = next_state
                episode_reward += reward

                # 训练智能体
                if len(agent.replay_buffer) > agent.batch_size:
                    agent.update()

                if done:
                    break

            # 记录episode结果
            completion_rate = info.get('completion_rate', 0.0)
            makespan = info.get('makespan', 1000.0)

            episode_rewards.append(episode_reward)
            episode_completion_rates.append(completion_rate)
            episode_makespans.append(makespan if completion_rate >= 0.8 else 1000.0)
            episode_dag_ids.append(dag_idx)
            
            # 记录DAG性能
            dag_performance[dag_idx].append({
                'completion_rate': completion_rate,
                'makespan': makespan,
                'episode': episode
            })

            if completion_rate >= 0.8:
                successful_episodes += 1

            # 打印进度
            if (episode + 1) % 20 == 0 or episode < 10:
                recent_completion = np.mean(episode_completion_rates[-20:])
                recent_makespan = np.mean([m for m in episode_makespans[-20:] if m < 1000])
                recent_reward = np.mean(episode_rewards[-20:])
                success_rate = successful_episodes / (episode + 1)

                elapsed_time = time.time() - start_time
                print(f"Episode {episode+1:4d} | "
                      f"DAG {dag_idx:2d} ({phase_info}) | "
                      f"完成率: {completion_rate:.1%} (平均: {recent_completion:.1%}) | "
                      f"Makespan: {makespan:6.1f}s (平均: {recent_makespan:6.1f}s) | "
                      f"奖励: {episode_reward:6.2f} | "
                      f"成功率: {success_rate:.1%}")

            # 保存模型
            if (episode + 1) % save_interval == 0:
                checkpoint_dir = f"{model_dir}/checkpoint_{episode+1}"
                os.makedirs(checkpoint_dir, exist_ok=True)
                agent.save_models(f"{checkpoint_dir}/agent.pth")
                print(f"💾 已保存检查点: {checkpoint_dir}")

        except Exception as e:
            print(f"❌ Episode {episode} 出错: {e}")
            continue

    # 训练完成分析
    analyze_training_results(episode_rewards, episode_completion_rates, episode_makespans, 
                           episode_dag_ids, dag_performance, model_dir)

    return {
        'rewards': episode_rewards,
        'completion_rates': episode_completion_rates,
        'makespans': episode_makespans,
        'dag_ids': episode_dag_ids,
        'dag_performance': dict(dag_performance),
        'success_rate': successful_episodes / num_episodes,
        'model_dir': model_dir
    }

def analyze_dag_complexity(data_loader, num_dags=50):
    """分析DAG复杂度"""
    print("  正在分析DAG复杂度...")
    complexities = []
    
    for dag_idx in range(min(num_dags, 200)):
        try:
            task_features = data_loader.load_task_features(dag_idx)
            adj_matrix = data_loader.load_adjacency_matrix(dag_idx)
            
            num_tasks = task_features.shape[1]
            num_edges = np.sum(adj_matrix)
            avg_memory = np.mean(task_features[0, :])  # 平均内存需求
            llm_ratio = np.mean(task_features[2, :])   # LLM任务比例
            
            # 计算复杂度分数
            complexity_score = (
                num_tasks * 0.3 +           # 任务数量
                num_edges * 0.2 +           # 依赖关系
                avg_memory * 0.3 +          # 内存需求
                llm_ratio * 20 * 0.2        # LLM任务比例
            )
            
            complexities.append({
                'dag_id': dag_idx,
                'num_tasks': num_tasks,
                'num_edges': num_edges,
                'avg_memory': avg_memory,
                'llm_ratio': llm_ratio,
                'complexity_score': complexity_score
            })
            
        except Exception as e:
            print(f"    警告: DAG {dag_idx} 分析失败: {e}")
            continue
    
    # 按复杂度排序
    complexities.sort(key=lambda x: x['complexity_score'])
    
    print(f"  分析完成: {len(complexities)} 个DAG")
    print(f"  复杂度范围: {complexities[0]['complexity_score']:.1f} - {complexities[-1]['complexity_score']:.1f}")
    
    return complexities

def create_curriculum(dag_complexity, num_episodes):
    """创建课程学习计划"""
    print("📚 创建课程学习计划...")
    
    # 将DAG按复杂度分为3个阶段
    total_dags = len(dag_complexity)
    phase1_dags = dag_complexity[:total_dags//3]          # 简单DAG
    phase2_dags = dag_complexity[total_dags//3:2*total_dags//3]  # 中等DAG
    phase3_dags = dag_complexity[2*total_dags//3:]        # 复杂DAG
    
    # 分配episode
    phase1_episodes = num_episodes // 2      # 50% 时间学习简单DAG
    phase2_episodes = num_episodes // 3      # 33% 时间学习中等DAG
    phase3_episodes = num_episodes - phase1_episodes - phase2_episodes  # 剩余时间学习复杂DAG
    
    curriculum = {
        'phase1': {
            'episodes': (0, phase1_episodes),
            'dags': [d['dag_id'] for d in phase1_dags],
            'repeat_count': 3,  # 每个简单DAG重复3次
            'description': '简单DAG'
        },
        'phase2': {
            'episodes': (phase1_episodes, phase1_episodes + phase2_episodes),
            'dags': [d['dag_id'] for d in phase2_dags],
            'repeat_count': 2,  # 每个中等DAG重复2次
            'description': '中等DAG'
        },
        'phase3': {
            'episodes': (phase1_episodes + phase2_episodes, num_episodes),
            'dags': [d['dag_id'] for d in phase3_dags],
            'repeat_count': 1,  # 复杂DAG不重复
            'description': '复杂DAG'
        }
    }
    
    print(f"  阶段1 ({curriculum['phase1']['description']}): Episode 0-{phase1_episodes}, {len(phase1_dags)} DAGs")
    print(f"  阶段2 ({curriculum['phase2']['description']}): Episode {phase1_episodes}-{phase1_episodes + phase2_episodes}, {len(phase2_dags)} DAGs")
    print(f"  阶段3 ({curriculum['phase3']['description']}): Episode {phase1_episodes + phase2_episodes}-{num_episodes}, {len(phase3_dags)} DAGs")
    
    return curriculum

def select_dag_by_curriculum(episode, curriculum_phases, dag_performance):
    """根据课程学习策略选择DAG"""
    # 确定当前阶段
    current_phase = None
    for phase_name, phase_info in curriculum_phases.items():
        start_ep, end_ep = phase_info['episodes']
        if start_ep <= episode < end_ep:
            current_phase = phase_info
            current_phase_name = phase_name
            break
    
    if current_phase is None:
        # 默认使用最后阶段
        current_phase = curriculum_phases['phase3']
        current_phase_name = 'phase3'
    
    # 在当前阶段的DAG中选择
    available_dags = current_phase['dags']
    repeat_count = current_phase['repeat_count']
    
    # 简单的轮询策略，考虑重复次数
    phase_episode = episode - current_phase['episodes'][0]
    dag_cycle_length = len(available_dags) * repeat_count
    
    if dag_cycle_length > 0:
        cycle_position = phase_episode % dag_cycle_length
        dag_index = cycle_position // repeat_count
        repeat_number = cycle_position % repeat_count + 1
        
        selected_dag = available_dags[dag_index % len(available_dags)]
        phase_info = f"{current_phase['description']}, 重复{repeat_number}/{repeat_count}"
    else:
        selected_dag = np.random.choice(available_dags) if available_dags else 0
        phase_info = current_phase['description']
    
    return selected_dag, phase_info

def analyze_training_results(episode_rewards, episode_completion_rates, episode_makespans, 
                           episode_dag_ids, dag_performance, model_dir):
    """分析训练结果"""
    print(f"\n📈 训练完成分析:")
    print(f"  总回合数: {len(episode_rewards)}")
    
    successful_episodes = sum(1 for cr in episode_completion_rates if cr >= 0.8)
    print(f"  成功回合数: {successful_episodes}")
    print(f"  成功率: {successful_episodes/len(episode_completion_rates):.1%}")

    # 成功完成的episode统计
    successful_makespans = [m for m, cr in zip(episode_makespans, episode_completion_rates) 
                           if cr >= 0.8 and m < 1000]
    if successful_makespans:
        print(f"  最佳Makespan: {min(successful_makespans):.2f}秒")
        print(f"  平均Makespan: {np.mean(successful_makespans):.2f}秒")
        print(f"  Makespan标准差: {np.std(successful_makespans):.2f}秒")

    # DAG性能分析
    print(f"\n📊 DAG性能分析:")
    dag_success_rates = {}
    for dag_id, performances in dag_performance.items():
        success_count = sum(1 for p in performances if p['completion_rate'] >= 0.8)
        success_rate = success_count / len(performances) if performances else 0
        dag_success_rates[dag_id] = success_rate
        
    print(f"  训练过的DAG数量: {len(dag_performance)}")
    print(f"  平均DAG成功率: {np.mean(list(dag_success_rates.values())):.1%}")
    print(f"  最佳DAG成功率: {max(dag_success_rates.values()):.1%}")
    print(f"  最差DAG成功率: {min(dag_success_rates.values()):.1%}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='改进的简化调度系统训练')
    parser.add_argument('--episodes', type=int, default=500, help='训练回合数')
    parser.add_argument('--device_num', type=int, default=5, help='边缘设备数量')
    parser.add_argument('--data_path', type=str, 
                       default='../dataset/training/DAG_30_edges_5_mem_57-64GB_density_0.2_0.6',
                       help='训练数据路径')
    parser.add_argument('--device', type=str, default='cuda', help='计算设备')
    parser.add_argument('--save_interval', type=int, default=250, help='保存间隔')

    args = parser.parse_args()

    print(f"🎯 改进训练参数:")
    print(f"  回合数: {args.episodes}")
    print(f"  设备数: {args.device_num}")
    print(f"  数据路径: {args.data_path}")
    print(f"  计算设备: {args.device}")

    # 开始训练
    results = train_with_curriculum_learning(
        data_path=args.data_path,
        num_episodes=args.episodes,
        save_interval=args.save_interval,
        device_num=args.device_num,
        device=args.device
    )

    print(f"\n🎉 改进训练完成!")
    print(f"  最终成功率: {results['success_rate']:.1%}")
    print(f"  模型保存路径: {results['model_dir']}")

if __name__ == "__main__":
    main()
