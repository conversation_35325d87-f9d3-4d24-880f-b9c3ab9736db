"""
简化的数据加载器
专门为简化系统设计，避免复杂的依赖
"""

import os
import sys
import numpy as np
import pandas as pd

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

class SimplifiedDataLoader:
    """简化的数据加载器"""
    
    def __init__(self, config):
        self.config = config
        self.data_path = config.TRAINING_DATA_PATH
        
        # 机器资源
        self.machine_memory = None
        self.machine_cpu_freq = None
        
        # 加载机器资源
        self._load_machine_resources()
        
        print(f"[SIMPLIFIED_LOADER] 初始化完成")
        print(f"  数据路径: {self.data_path}")
        print(f"  机器数量: {len(self.machine_memory)}")
    
    def _load_machine_resources(self):
        """加载机器资源"""
        try:
            # 尝试加载机器资源文件
            if os.path.exists(self.config.MACHINES_RESOURCE_PATH):
                df = pd.read_excel(self.config.MACHINES_RESOURCE_PATH)
                self.machine_memory = df['Memory(GB)'].values
                self.machine_cpu_freq = df.get('CPU_Frequency(GHz)', [2.4] * len(self.machine_memory)).values
            else:
                # 使用默认配置
                self.machine_memory = np.array([32.0, 30.0, 25.0, 24.0, 30.0, 22.0])  # 用户设备 + 5个边缘设备
                self.machine_cpu_freq = np.array([2.4, 12.0, 14.0, 11.0, 14.0, 13.0])
        except Exception as e:
            print(f"[SIMPLIFIED_LOADER] 警告: 无法加载机器资源文件，使用默认配置: {e}")
            # 默认配置：用户设备 + 边缘设备
            num_machines = self.config.NUM_EDGE_SERVERS + 1
            self.machine_memory = np.array([32.0] + [28.0] * self.config.NUM_EDGE_SERVERS)
            self.machine_cpu_freq = np.array([2.4] + [12.0] * self.config.NUM_EDGE_SERVERS)
        
        # 确保机器数量正确
        expected_machines = self.config.NUM_EDGE_SERVERS + 1
        if len(self.machine_memory) != expected_machines:
            # 调整到正确的机器数量
            if len(self.machine_memory) > expected_machines:
                self.machine_memory = self.machine_memory[:expected_machines]
                self.machine_cpu_freq = self.machine_cpu_freq[:expected_machines]
            else:
                # 扩展到正确数量
                while len(self.machine_memory) < expected_machines:
                    self.machine_memory = np.append(self.machine_memory, 28.0)
                    self.machine_cpu_freq = np.append(self.machine_cpu_freq, 12.0)
    
    def load_task_features(self, dag_idx):
        """加载任务特征"""
        try:
            feature_file = os.path.join(self.data_path, f"task_feature_{dag_idx}.npy")
            if os.path.exists(feature_file):
                features = np.load(feature_file)
                return features
            else:
                # 生成模拟数据
                return self._generate_mock_task_features()
        except Exception as e:
            print(f"[SIMPLIFIED_LOADER] 警告: 无法加载任务特征 {dag_idx}: {e}")
            return self._generate_mock_task_features()
    
    def load_adjacency_matrix(self, dag_idx):
        """加载邻接矩阵"""
        try:
            adj_file = os.path.join(self.data_path, f"adj_matrix_{dag_idx}.npy")
            if os.path.exists(adj_file):
                adj_matrix = np.load(adj_file)
                return adj_matrix
            else:
                # 生成模拟数据
                return self._generate_mock_adjacency_matrix()
        except Exception as e:
            print(f"[SIMPLIFIED_LOADER] 警告: 无法加载邻接矩阵 {dag_idx}: {e}")
            return self._generate_mock_adjacency_matrix()
    
    def load_memory_status(self):
        """加载内存状态"""
        return self.machine_memory.copy()
    
    def load_machines_resource(self):
        """加载机器资源"""
        return self.machine_memory.copy()
    
    def load_machine_commu_speed(self):
        """加载通信速度"""
        num_machines = len(self.machine_memory)
        # 生成默认通信速度矩阵
        comm_speed = np.ones((num_machines, num_machines)) * 100.0  # 100 Mbps
        # 对角线设为0（自己到自己）
        np.fill_diagonal(comm_speed, 0.0)
        return comm_speed
    
    def _generate_mock_task_features(self, num_tasks=30):
        """生成模拟任务特征"""
        features = np.zeros((5, num_tasks))
        
        for i in range(num_tasks):
            # 内存需求 (0.5GB - 15GB)
            features[0, i] = np.random.uniform(0.5, 15.0)
            
            # 预测输出token (LLM任务)
            is_llm = np.random.random() < 0.4  # 40%概率是LLM任务
            if is_llm:
                features[1, i] = np.random.uniform(500, 2000)  # 500-2000 tokens
                features[2, i] = 1  # LLM标记
            else:
                features[1, i] = 0
                features[2, i] = 0
            
            # 其他特征
            features[3, i] = np.random.uniform(0.1, 1.0)  # 计算强度
            features[4, i] = np.random.uniform(0.1, 1.0)  # 数据大小
        
        return features
    
    def _generate_mock_adjacency_matrix(self, num_tasks=30):
        """生成模拟邻接矩阵"""
        adj_matrix = np.zeros((num_tasks, num_tasks))
        
        # 生成简单的链式依赖
        for i in range(num_tasks - 1):
            if np.random.random() < 0.6:  # 60%概率有依赖
                adj_matrix[i, i + 1] = 1
        
        # 添加一些随机依赖
        for i in range(num_tasks):
            for j in range(i + 2, min(i + 5, num_tasks)):
                if np.random.random() < 0.2:  # 20%概率有跨越依赖
                    adj_matrix[i, j] = 1
        
        return adj_matrix
    
    def get_dag_count(self):
        """获取DAG数量"""
        if not os.path.exists(self.data_path):
            return 200  # 默认200个DAG
        
        # 计算实际DAG数量
        count = 0
        for filename in os.listdir(self.data_path):
            if filename.startswith("task_feature_") and filename.endswith(".npy"):
                count += 1
        
        return max(count, 200)  # 至少200个
    
    def validate_dag(self, dag_idx):
        """验证DAG是否有效"""
        try:
            task_features = self.load_task_features(dag_idx)
            adj_matrix = self.load_adjacency_matrix(dag_idx)
            
            # 基本验证
            if task_features.shape[1] != adj_matrix.shape[0]:
                return False
            
            if task_features.shape[1] == 0:
                return False
            
            return True
        except:
            return False
    
    def get_task_info(self, task_features, task_idx):
        """获取任务信息"""
        if task_idx >= task_features.shape[1]:
            return None
        
        return {
            'memory_required': task_features[0, task_idx],
            'predicted_tokens': task_features[1, task_idx],
            'is_llm': task_features[2, task_idx] == 1,
            'compute_intensity': task_features[3, task_idx],
            'data_size': task_features[4, task_idx]
        }
    
    def calculate_execution_time(self, task_info, machine_idx):
        """计算任务执行时间"""
        if machine_idx >= len(self.machine_cpu_freq):
            machine_idx = 0
        
        cpu_freq = self.machine_cpu_freq[machine_idx]
        is_user_device = (machine_idx == 0)
        
        if task_info['is_llm']:
            # LLM任务执行时间
            if is_user_device:
                base_time = 0.23
                token_time = task_info['predicted_tokens'] * 0.013
            else:
                base_time = 0.046
                token_time = task_info['predicted_tokens'] * 0.1
            
            return base_time + token_time
        else:
            # 普通任务执行时间
            base_time = 1.0 * task_info['compute_intensity']
            
            # 根据CPU频率调整
            if is_user_device:
                return base_time * 10  # 用户设备慢10倍
            else:
                return base_time / (cpu_freq / 2.4)  # 相对于基准频率
    
    def calculate_transmission_time(self, task_info, from_machine, to_machine):
        """计算传输时间"""
        if from_machine == to_machine:
            return 0.0
        
        # 简化的传输时间计算
        data_size_mb = task_info['data_size'] * 100  # 转换为MB
        bandwidth_mbps = 100.0  # 100 Mbps
        
        return data_size_mb / bandwidth_mbps
