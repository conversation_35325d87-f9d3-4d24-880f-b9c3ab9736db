#!/usr/bin/env python3
"""
改进的xLSTM训练脚本，专门解决95%完成率问题
主要改进：
1. 增加死锁检测阈值
2. 改进奖励函数，减少对失败的过度惩罚
3. 添加渐进式奖励
4. 过滤高内存需求的问题DAG
"""

import os
import sys
import numpy as np
import torch
import torch.nn as nn
import matplotlib.pyplot as plt
from datetime import datetime
import pandas as pd

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_loader import DataLoader
from sac_xlstm_encoder_decoder import SAC_XLSTMEncoderDecoder as SAC_xLSTM
from base_config import BaseConfig
import random

def filter_problematic_dags(dataset_dir, max_memory_ratio=0.75):
    """
    过滤掉内存需求过高的问题DAG

    Args:
        dataset_dir: 数据集目录
        max_memory_ratio: 最大内存使用比例阈值

    Returns:
        good_dag_ids: 可用的DAG ID列表
    """
    print(f"🔍 分析数据集，过滤问题DAG...")

    # 读取机器配置
    machines_file = os.path.join(dataset_dir, 'machines_resource1.xlsx')
    try:
        df_machines = pd.read_excel(machines_file)
        total_memory = df_machines['Memory_Available'].sum()
        memory_threshold = total_memory * max_memory_ratio

        print(f"系统总内存: {total_memory:.2f} GB")
        print(f"内存阈值 ({max_memory_ratio*100}%): {memory_threshold:.2f} GB")

    except Exception as e:
        print(f"读取机器配置失败: {e}")
        return list(range(200))  # 返回所有DAG

    good_dag_ids = []
    problematic_count = 0

    for dag_id in range(200):
        task_file = os.path.join(dataset_dir, f'task_{dag_id}_features.xlsx')
        if os.path.exists(task_file):
            try:
                df_tasks = pd.read_excel(task_file)
                total_memory_req = df_tasks['Memory_Req'].sum()

                if total_memory_req <= memory_threshold:
                    good_dag_ids.append(dag_id)
                else:
                    problematic_count += 1

            except Exception as e:
                print(f"分析DAG {dag_id}失败: {e}")
                continue

    print(f"✅ 过滤结果: {len(good_dag_ids)}/200 DAG可用，过滤掉{problematic_count}个高内存需求DAG")
    return good_dag_ids

def create_improved_reward_function():
    """创建改进的奖励函数，减少对失败的过度惩罚"""

    def improved_reward(info):
        """
        改进的奖励函数
        - 减少死锁惩罚
        - 增加渐进式奖励
        - 平衡完成率和效率
        """
        reward = 0.0

        # 1. 任务完成奖励（渐进式）
        completion_rate = info.get('completion_rate', 0.0)
        if completion_rate > 0:
            # 渐进式奖励，避免稀疏奖励问题
            reward += completion_rate * 10.0  # 基础完成奖励

            # 高完成率额外奖励
            if completion_rate >= 0.9:
                reward += (completion_rate - 0.9) * 50.0  # 90%以上额外奖励
            if completion_rate >= 0.95:
                reward += (completion_rate - 0.95) * 100.0  # 95%以上大额奖励
            if completion_rate == 1.0:
                reward += 50.0  # 完美完成奖励

        # 2. 时间效率奖励
        makespan = info.get('makespan', float('inf'))
        if makespan < float('inf') and completion_rate > 0.8:
            # 只有在高完成率时才考虑时间效率
            time_efficiency = max(0, 1000 - makespan) / 1000
            reward += time_efficiency * 5.0

        # 3. 内存效率奖励
        memory_efficiency = info.get('memory_efficiency', 0.0)
        reward += memory_efficiency * 2.0

        # 4. 失败惩罚（减轻）
        failed_tasks = info.get('failed_tasks', 0)
        if failed_tasks > 0:
            # 减轻失败惩罚，避免过度惩罚
            reward -= failed_tasks * 2.0  # 从原来的-5.0减少到-2.0

        # 5. 死锁惩罚（大幅减轻）
        if info.get('deadlock_detected', False):
            reward -= 20.0  # 从原来的-100.0减少到-20.0

        return reward

    return improved_reward

def train_improved_xlstm(num_episodes=1000, save_interval=500):
    """
    改进的xLSTM训练函数
    """
    print("🚀 开始改进的xLSTM训练...")

    # 配置
    config = BaseConfig()
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")

    # 数据集路径
    dataset_dir = "dataset/training/DAG_30_edges_5_mem_57-64GB_density_0.2_0.6"

    # 过滤问题DAG
    good_dag_ids = filter_problematic_dags(dataset_dir, max_memory_ratio=0.75)

    if len(good_dag_ids) < 50:
        print("⚠️  可用DAG数量太少，使用所有DAG")
        good_dag_ids = list(range(200))

    # 初始化数据加载器
    data_loader = DataLoader(config)

    # 初始化智能体
    agent = SAC_xLSTM(
        state_dim=36,
        action_dim=2,
        max_action=1.0,
        seq_len=32,
        task_feature_dim=37,
        config=config
    )

    # 创建改进的奖励函数
    improved_reward_fn = create_improved_reward_function()

    # 训练统计
    episode_rewards = []
    completion_rates = []
    makespans = []
    success_count = 0

    # 创建保存目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    save_dir = f"models/improved_completion_xlstm_{timestamp}"
    os.makedirs(save_dir, exist_ok=True)

    print(f"📁 模型保存目录: {save_dir}")
    print(f"📊 开始训练 {num_episodes} 个回合...")

    for episode in range(num_episodes):
        # 随机选择一个好的DAG
        dag_id = random.choice(good_dag_ids)

        try:
            # 加载DAG数据
            adj_matrix = data_loader.load_adjacency_matrix(f"{dataset_dir}/adjacency_{dag_id}_matrix.npy")
            task_features = data_loader.load_task_features(f"{dataset_dir}/task_{dag_id}_features.xlsx")
            machine_resources = data_loader.load_machine_resources(f"{dataset_dir}/machines_resource1.xlsx")
            comm_speed = data_loader.load_communication_speed(f"{dataset_dir}/communication_speed.xlsx")
            memory_status = machine_resources[:, 1].copy()

            num_tasks = task_features.shape[1]

            # 重置环境
            state = data_loader.reset_dag(adj_matrix, task_features, num_tasks,
                                        machine_resources, comm_speed, memory_status)

            episode_reward = 0
            step_count = 0
            max_steps = num_tasks * 3  # 增加最大步数

            while step_count < max_steps:
                # 获取就绪任务
                ready_tasks = data_loader.get_ready_tasks()
                if not ready_tasks:
                    break

                # 选择动作
                action = agent.select_action(state, ready_tasks[0])

                # 执行动作
                next_state, reward, done, info = data_loader.step_task(
                    action, adj_matrix, task_features, num_tasks,
                    machine_resources, comm_speed, memory_status,
                    use_sequence=True
                )

                # 使用改进的奖励函数
                improved_reward = improved_reward_fn(info)

                # 存储经验
                agent.store_transition(state, action, improved_reward, next_state, done)

                # 更新网络
                if len(agent.replay_buffer) > agent.batch_size:
                    agent.update()

                state = next_state
                episode_reward += improved_reward
                step_count += 1

                if done:
                    break

            # 记录统计信息
            completion_rate = info.get('completion_rate', 0.0)
            makespan = info.get('makespan', float('inf'))

            episode_rewards.append(episode_reward)
            completion_rates.append(completion_rate)
            makespans.append(makespan if makespan < float('inf') else 1000)

            if completion_rate >= 0.95:
                success_count += 1

            # 打印进度
            if (episode + 1) % 100 == 0:
                avg_reward = np.mean(episode_rewards[-100:])
                avg_completion = np.mean(completion_rates[-100:])
                avg_makespan = np.mean([m for m in makespans[-100:] if m < 1000])
                success_rate = success_count / (episode + 1)

                print(f"回合 {episode + 1:4d}: "
                      f"奖励={avg_reward:7.2f}, "
                      f"完成率={avg_completion:.3f}, "
                      f"Makespan={avg_makespan:.2f}s, "
                      f"成功率={success_rate:.3f}")

            # 保存中间结果
            if (episode + 1) % save_interval == 0:
                checkpoint_dir = os.path.join(save_dir, f"checkpoint_{episode + 1}")
                os.makedirs(checkpoint_dir, exist_ok=True)

                # 保存模型
                actor_path = os.path.join(checkpoint_dir, "actor.pth")
                critic_path = os.path.join(checkpoint_dir, "critic.pth")
                critic_target_path = os.path.join(checkpoint_dir, "critic_target.pth")
                agent.save(actor_path, critic_path, critic_target_path)

                # 保存训练曲线
                plt.figure(figsize=(15, 5))

                plt.subplot(1, 3, 1)
                plt.plot(episode_rewards)
                plt.title('Training Rewards')
                plt.xlabel('Episode')
                plt.ylabel('Reward')

                plt.subplot(1, 3, 2)
                plt.plot(completion_rates)
                plt.title('Completion Rate')
                plt.xlabel('Episode')
                plt.ylabel('Completion Rate')
                plt.ylim(0, 1)

                plt.subplot(1, 3, 3)
                plt.plot([m for m in makespans if m < 1000])
                plt.title('Makespan (Completed DAGs)')
                plt.xlabel('Episode')
                plt.ylabel('Makespan (s)')

                plt.tight_layout()
                plt.savefig(os.path.join(checkpoint_dir, 'training_curves.png'))
                plt.close()

                print(f"💾 中间结果已保存到: {checkpoint_dir}")

        except Exception as e:
            print(f"⚠️  回合 {episode + 1} 执行失败: {e}")
            continue

    # 保存最终模型
    final_dir = os.path.join(save_dir, "final")
    os.makedirs(final_dir, exist_ok=True)

    # 保存最终模型
    actor_path = os.path.join(final_dir, "actor.pth")
    critic_path = os.path.join(final_dir, "critic.pth")
    critic_target_path = os.path.join(final_dir, "critic_target.pth")
    agent.save(actor_path, critic_path, critic_target_path)

    # 计算最终统计
    final_success_rate = success_count / num_episodes
    final_avg_completion = np.mean(completion_rates)
    final_avg_reward = np.mean(episode_rewards)

    print(f"\n🎉 训练完成!")
    print(f"📊 最终统计:")
    print(f"  总回合数: {num_episodes}")
    print(f"  成功率 (≥95%): {final_success_rate:.3f}")
    print(f"  平均完成率: {final_avg_completion:.3f}")
    print(f"  平均奖励: {final_avg_reward:.2f}")
    print(f"🏆 最终模型已保存到: {final_dir}")

    return agent, episode_rewards, completion_rates, makespans

if __name__ == "__main__":
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    random.seed(42)

    # 开始训练
    agent, rewards, completion_rates, makespans = train_improved_xlstm(
        num_episodes=1000,
        save_interval=500
    )
