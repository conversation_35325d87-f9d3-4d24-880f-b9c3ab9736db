from dataclasses import dataclass

import numpy as np
import random
import matplotlib.pyplot as plt
import networkx as nx
from typing import List, Tuple
import copy


def _get_predecessors_successors(adj_matrix: np.ndarray, node_idx: int) -> Tuple[List[int], List[int]]:
    predecessors = np.where(adj_matrix[:, node_idx] == 1)[0].tolist()
    successors = np.where(adj_matrix[node_idx, :] == 1)[0].tolist()
    return predecessors, successors


def _get_topological_order(adj_matrix: np.ndarray) -> List[int]:
    in_degree = np.sum(adj_matrix, axis=0)
    queue = list(np.where(in_degree == 0)[0])
    topo_order = []
    while queue:
        node = queue.pop(0)
        topo_order.append(node)
        for successor in np.where(adj_matrix[node] == 1)[0]:
            in_degree[successor] -= 1
            if in_degree[successor] == 0:
                queue.append(successor)
    return topo_order


def _convert_edges_to_matrix(edges: List[Tuple], num_nodes: int) -> np.ndarray:
    adj_matrix = np.zeros((num_nodes, num_nodes), dtype=int)
    for edge in edges:
        if isinstance(edge[0], str) or isinstance(edge[1], str):
            continue
        adj_matrix[edge[0] - 1][edge[1] - 1] = 1
    return adj_matrix


def daggen_py(n: int, fat: float, density: float = 0.0, seed: int = None) -> np.ndarray:
    if seed is not None:
        np.random.seed(seed)
        random.seed(seed)
    dag = np.zeros((n, n), dtype=int)
    layers = []
    total = 0
    while total < n:
        layer_size = int(fat * n * random.random())
        layer_size = max(1, layer_size)
        if total + layer_size > n:
            layer_size = n - total
        layer = list(range(total, total + layer_size))
        layers.append(layer)
        total += layer_size
    for i in range(len(layers) - 1):
        src_layer = layers[i]
        dst_layer = layers[i + 1]
        for src in src_layer:
            out_deg = random.randint(1, len(dst_layer))
            selected = random.sample(dst_layer, out_deg)
            for dst in selected:
                dag[src][dst] = 1
    if density > 0:
        max_edges = n * (n - 1) // 2
        target_edges = int(density * max_edges)
        current_edges = int(np.sum(dag))
        while current_edges < target_edges:
            u = random.randint(0, n - 2)
            v = random.randint(u + 1, n - 1)
            if dag[u][v] == 0:
                dag[u][v] = 1
                current_edges += 1
    return dag

def generate_dag_shape(n=10, fat=0.4, density=0.7, seed=None):
    adj_matrix = daggen_py(n=n, fat=fat, density=density, seed=seed)
    into_degree = np.sum(adj_matrix, axis=0).tolist()
    out_degree = np.sum(adj_matrix, axis=1).tolist()
    edges = []
    for i in range(n):
        for j in range(n):
            if adj_matrix[i][j] == 1:
                edges.append((i + 1, j + 1))
    for i in range(n):
        if into_degree[i] == 0:
            edges.append(('Start', i + 1))
            into_degree[i] += 1
        if out_degree[i] == 0:
            edges.append((i + 1, 'Exit'))
            out_degree[i] += 1
    position = {'Start': (0, 0), 'Exit': (n + 1, 0)}
    for i in range(n):
        position[i + 1] = (i + 1, 0)
    return edges, into_degree, out_degree, position

@dataclass
class Function:
    id: int
    true_id: int
    workflow_id: int
    priority: int
    input_size: float  # KB
    output_size: float  # KB
    cpu_cycles: float  # G cycles
    memory: float  # MB
    predecessors: List[int]
    successors: List[int]
    arrive_time: float

    location: int = -1  # Current execution location
    start_time: float = 0.0
    finish_time: float = 0.0
    cold_start: bool = None

class Workflow:
    def __init__(self, config: dict, workflow_id: int, workflow_seed: int, arrive_time: float):

        self.id = workflow_id
        self.seed = workflow_seed
        self.arrive_time = arrive_time

        workflow_config = {'n': config['workflow']['n'], 'fat': config['workflow']['fat'], 'density': config['workflow']['density']}
        function_config = config['function']

        # DAG 构建
        edges, _, _, _ = generate_dag_shape(**workflow_config, seed=self.seed)
        self.adj_matrix = _convert_edges_to_matrix(edges, workflow_config['n'])

        # 函数属性构建    functions和function_statuses按照function_id排序
        from numpy.random import default_rng
        rng = default_rng(self.seed)
        self.functions = []
        for i in range(workflow_config['n']):
            pred_ids, succ_ids = _get_predecessors_successors(self.adj_matrix, i)  #self.id是工作流ID
            pred_ids1 = [p + self.id * 1000 for p in pred_ids]
            succ_ids1 = [s + self.id * 1000 for s in succ_ids]
            # print(self.id, pred_ids, succ_ids)
            self.functions.append({
                'id': self.id * 1000 + i,   # 唯一，逻辑
                'true_id': self.seed * 1000 + i,    #真正的函数id
                'workflow_id': self.id,
                'priority': -1,
                'input_size': rng.uniform(function_config['input_size_range'][0], function_config['input_size_range'][1]),
                'output_size': rng.uniform(function_config['output_size_range'][0], function_config['output_size_range'][1]),
                'cpu_cycles': rng.uniform(function_config['cpu_cycles_range'][0], function_config['cpu_cycles_range'][1]),    #1-5
                'memory': rng.choice(function_config['memory_range']),  # 64, 128, 256, 512
                'predecessors': pred_ids1,
                'successors': succ_ids1,
                'arrive_time': arrive_time
            })

        # 函数状态 pending completed
        self.function_statuses = np.full(len(self.functions), 'pending', dtype=object)

    # 在现有类中添加 __deepcopy__ 方法
    def __deepcopy__(self, memo):
        """实现深拷贝，确保所有属性都被正确复制"""
        # 创建一个新的空对象
        result = type(self).__new__(type(self))

        # 将当前对象添加到 memo 字典，避免循环引用
        memo[id(self)] = result

        # 深拷贝所有属性
        result.id = self.id
        result.seed = self.seed
        result.adj_matrix = copy.deepcopy(self.adj_matrix, memo)
        result.functions = copy.deepcopy(self.functions, memo)
        result.function_statuses = copy.deepcopy(self.function_statuses, memo)

        return result

    def clone(self):
        """返回此工作流的深拷贝"""
        return copy.deepcopy(self)

    def update_function_status(self, function_id: int, status: str):
        self.function_statuses[function_id % 1000] = status

    def is_completed(self):
        # Check statuses of all tasks
        return np.all(self.function_statuses == 'completed')

    def get_ready_function_ids(self):
        """
        Returns a list of function IDs that are ready to be executed.
        A function is ready if:
        - Its status is 'pending'.
        - All its dependencies have been completed.
        """
        ready_function_ids = []
        # Indices of pending functions
        pending_function_ids = np.where(self.function_statuses == 'pending')[0]
        for function_id in pending_function_ids:
            # function = self.functions[function_id]
            pred_ids, succ_ids = _get_predecessors_successors(self.adj_matrix, function_id)
            if not pred_ids:
                ready_function_ids.append(function_id)
            else:
                pred_statuses = self.function_statuses[pred_ids]
                if np.all(pred_statuses == 'completed'):
                    ready_function_ids.append(function_id)
        return ready_function_ids

    def get_function_by_id(self, function_id: int):
        return Function(**self.functions[function_id % 1000])

    def compute_heft_priorities(self, server_freqs: list, bandwidth_mbps: float):
        n = len(self.functions)
        rank_u = [0.0] * n
        bandwidth_bps = bandwidth_mbps * 1e6
        avg_exec = [np.mean([f['cpu_cycles'] / freq for freq in server_freqs]) for f in self.functions]
        avg_comm = np.zeros((n, n))
        for i in range(n):
            for j in range(n):
                if self.adj_matrix[i][j] == 1:
                    bits = self.functions[i]['output_size'] * 1024 * 8
                    avg_comm[i][j] = bits / bandwidth_bps
        reverse_topo = _get_topological_order(self.adj_matrix)[::-1]
        for i in reverse_topo:
            # successors = self.functions[i]['successors']
            successors = [s % 1000 for s in self.functions[i]['successors']]
            # print(self.id, successors)
            if not successors:
                rank_u[i] = avg_exec[i]
            else:
                rank_u[i] = avg_exec[i] + max(avg_comm[i][s] + rank_u[s] for s in successors)
        for i in range(n):
            self.functions[i]['priority'] = rank_u[i]

    def draw(self):
        G = nx.DiGraph()
        for func in self.functions:
            label = f"p={func['priority']:.2f}，id={func['id']}"
            G.add_node(func['id'], label=label)
        n = self.adj_matrix.shape[0]
        for i in range(n):
            for j in range(n):
                if self.adj_matrix[i][j] == 1:
                    G.add_edge(i, j)
        pos = nx.spring_layout(G, seed=seed)
        labels = nx.get_node_attributes(G, 'label')
        plt.figure(figsize=(14, 8))
        nx.draw(G, pos, with_labels=False, node_color='lightblue', node_size=1800, arrows=True)
        nx.draw_networkx_labels(G, pos, labels=labels, font_size=8)
        plt.title(f"Workflow (App {config}, Seed {self.seed})")
        plt.axis('off')
        plt.tight_layout()
        plt.show()

    def print_summary(self):
        print(f"App Config: {config}, Seed: {self.seed}")
        for f in self.functions:
            print(f"Task {f['id']:2d} | Rank: {f['priority']:.4f} | Pred: {f['predecessors']} | Succ: {f['successors']}")

if __name__ == '__main__':
    # config
    configs = {
        1: {'n': 10, 'fat': 0.4, 'density': 0.3},
        2: {'n': 10, 'fat': 0.6},
        3: {'n': 20, 'fat': 0.4},
        4: {'n': 20, 'fat': 0.6},
        5: {'n': 30, 'fat': 0.4},
        6: {'n': 30, 'fat': 0.6},
    }
    config = configs[1]

    workflows = []

    # 测试调用
    for seed in range(1):
        server_freqs = [2.0, 2.5, 3.0]
        bandwidth_mbps = 10.0

        workflow = Workflow(config=config, seed=seed)
        workflow.compute_heft_priorities(server_freqs, bandwidth_mbps)
        workflows.append(workflow)
        workflow.print_summary()
        workflow.draw()
        workflow.update_function_status(0, 'completed')
        workflow.update_function_status(2, 'completed')
        print(workflow.get_ready_function_ids())

