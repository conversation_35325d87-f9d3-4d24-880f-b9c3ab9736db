"""
增强的LLM调度系统配置
基于原有配置，增加了动态状态管理和复合动作空间
"""
import os
import sys
import numpy as np

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from base_config import BaseConfig

class EnhancedLLMConfig(BaseConfig):
    """增强的LLM调度配置类"""

    def __init__(self):
        super().__init__()

        # 任务状态定义
        self.TASK_STATUS_DIM = 4  # 任务状态的one-hot编码维度
        self.TASK_STATUS_NAMES = [
            "未就绪/等待输入",      # 0: 前序依赖未满足，或LLM任务的输入token尚未确定
            "信息完整/就绪可调度",   # 1: 所有依赖满足，LLM任务的输入输出已预估完成
            "运行中",              # 2: 任务正在执行
            "已完成/失败"          # 3: 任务已完成或失败
        ]

        # 动态特征维度
        self.DYNAMIC_FEATURES_DIM = (
            self.TASK_STATUS_DIM +  # 任务状态one-hot编码
            1 +                     # 预估输出token数量（归一化）
            1                       # 预估运行时内存（归一化）
        )

        # 静态任务特征维度（简化）
        self.STATIC_TASK_DIM = 3  # CPU周期、数据传输、是否LLM

        # 重新计算状态维度
        self.ENHANCED_STATE_DIM = (
            self.STATIC_TASK_DIM +      # 静态任务特征
            self.DYNAMIC_FEATURES_DIM + # 动态特征
            self.STATE_COMM_DIM +       # 通信特征
            self.STATE_MEM_DIM +        # 内存状态
            self.STATE_COMPUTE_DIM +    # 计算能力
            self.STATE_TTFT_DIM +       # TTFT
            self.STATE_TPOT_DIM +       # TPOT
            self.STATE_DAG_DIM +        # DAG结构
            1                           # 有效任务掩码
        )

        # 复合动作空间
        self.TASK_CHOICE_DIM = 1      # 任务选择（连续值映射到就绪任务索引）
        self.MACHINE_CHOICE_DIM = 1   # 机器选择（连续值映射到机器索引）
        self.COMPOUND_ACTION_DIM = self.TASK_CHOICE_DIM + self.MACHINE_CHOICE_DIM

        # LLM预测相关参数
        self.MAX_PREDICTED_TOKENS = 3000  # 最大预测输出token数，用于归一化
        self.MAX_RUNTIME_MEMORY = 64      # 最大运行时内存(GB)，用于归一化
        self.TOKEN_PREDICTION_FACTOR = 1.2  # 输出token预测因子（输出=输入*因子+基础值）
        self.BASE_OUTPUT_TOKENS = 50      # 基础输出token数

        # 内存预测参数
        self.BASE_LLM_MEMORY = 8.0       # LLM基础内存需求(GB)
        self.MEMORY_PER_TOKEN = 0.002    # 每个token的额外内存需求(GB)

        # 更新总特征维度
        self.TASK_FEATURE_DIM = self.ENHANCED_STATE_DIM

        # 确保有SEQ_LEN属性
        if not hasattr(self, 'SEQ_LEN'):
            self.SEQ_LEN = 32  # 默认序列长度

        # 覆盖训练参数以提高数值稳定性
        self.LEARNING_RATE = 1e-3  # 进一步降低学习率
        self.BATCH_SIZE = 64       # 进一步减少批次大小
        self.ALPHA = 0.05          # 进一步降低熵权重
        self.TAU = 0.01           # 更慢的目标网络更新

        print(f"[ENHANCED_CONFIG] 增强状态维度: {self.ENHANCED_STATE_DIM}")
        print(f"[ENHANCED_CONFIG] 动态特征维度: {self.DYNAMIC_FEATURES_DIM}")
        print(f"[ENHANCED_CONFIG] 复合动作维度: {self.COMPOUND_ACTION_DIM}")
        print(f"[ENHANCED_CONFIG] 学习率: {self.LEARNING_RATE}")
        print(f"[ENHANCED_CONFIG] 批次大小: {self.BATCH_SIZE}")

    def get_task_status_index(self, status_name):
        """获取任务状态的索引"""
        try:
            return self.TASK_STATUS_NAMES.index(status_name)
        except ValueError:
            return 0  # 默认返回"未就绪"状态

    def create_task_status_onehot(self, status_index):
        """创建任务状态的one-hot编码"""
        onehot = np.zeros(self.TASK_STATUS_DIM)
        if 0 <= status_index < self.TASK_STATUS_DIM:
            onehot[status_index] = 1.0
        return onehot

    def predict_llm_output_tokens(self, input_tokens):
        """预测LLM任务的输出token数量"""
        if input_tokens <= 0:
            return self.BASE_OUTPUT_TOKENS

        predicted_output = input_tokens * self.TOKEN_PREDICTION_FACTOR + self.BASE_OUTPUT_TOKENS
        return min(predicted_output, self.MAX_PREDICTED_TOKENS)

    def predict_llm_runtime_memory(self, input_tokens, output_tokens):
        """预测LLM任务的运行时内存需求"""
        total_tokens = input_tokens + output_tokens
        runtime_memory = self.BASE_LLM_MEMORY + total_tokens * self.MEMORY_PER_TOKEN
        return min(runtime_memory, self.MAX_RUNTIME_MEMORY)

    def normalize_tokens(self, token_count):
        """归一化token数量"""
        return min(token_count / self.MAX_PREDICTED_TOKENS, 1.0)

    def normalize_memory(self, memory_gb):
        """归一化内存大小"""
        return min(memory_gb / self.MAX_RUNTIME_MEMORY, 1.0)

    def _update_file_paths(self):
        """更新文件路径 - 支持2-6个边缘设备"""
        # 验证边缘设备数量
        if self.NUM_EDGE_SERVERS not in [2, 3, 4, 5, 6]:
            print(f"[ENHANCED_CONFIG] 警告: 不支持的边缘设备数量: {self.NUM_EDGE_SERVERS}")
            print(f"[ENHANCED_CONFIG] 支持的设备数量: 2, 3, 4, 5, 6")

        # 标准数据路径格式：DAG_200_edges_[边缘设备数]_mem_32GB_density_0.4_0.6
        standard_path = f'../dataset/training/DAG_200_edges_{self.NUM_EDGE_SERVERS}_mem_32GB_density_0.4_0.6'

        if os.path.exists(standard_path):
            self.TRAINING_DATA_PATH = standard_path
            print(f"[ENHANCED_CONFIG] 使用数据路径: {standard_path}")
        else:
            # 如果标准路径不存在，提示用户手动设置
            print(f"[ENHANCED_CONFIG] 警告: 数据路径不存在: {standard_path}")
            print(f"[ENHANCED_CONFIG] 支持的设备数量: 2, 3, 4, 5, 6")
            print(f"[ENHANCED_CONFIG] 请使用 --data_path 参数指定正确的数据路径")
            self.TRAINING_DATA_PATH = standard_path  # 仍然设置，让用户知道期望的路径

        # 设置机器资源和通信速度文件路径
        # 首先尝试在数据路径中查找
        machine_files = [
            f'{self.TRAINING_DATA_PATH}/machines_resource1.xlsx',
            f'{self.TRAINING_DATA_PATH}/machines_resource.xlsx',
            'dataset/machines_resource1.xlsx',
            'dataset/machines_resource.xlsx'
        ]

        comm_files = [
            f'{self.TRAINING_DATA_PATH}/machine_commu_speed1.xlsx',
            f'{self.TRAINING_DATA_PATH}/machine_commu_speed.xlsx',
            'dataset/machine_commu_speed1.xlsx',
            'dataset/machine_commu_speed.xlsx'
        ]

        # 查找机器资源文件
        self.MACHINES_RESOURCE_PATH = None
        for path in machine_files:
            if os.path.exists(path):
                self.MACHINES_RESOURCE_PATH = path
                break

        if self.MACHINES_RESOURCE_PATH is None:
            self.MACHINES_RESOURCE_PATH = 'dataset/machines_resource1.xlsx'

        # 查找通信速度文件
        self.MACHINE_COMMU_SPEED_PATH = None
        for path in comm_files:
            if os.path.exists(path):
                self.MACHINE_COMMU_SPEED_PATH = path
                break

        if self.MACHINE_COMMU_SPEED_PATH is None:
            self.MACHINE_COMMU_SPEED_PATH = 'dataset/machine_commu_speed1.xlsx'

    def update_edge_servers(self, num_edge_servers, task_num=None, memory_limit=None):
        """重写父类方法，更新增强配置"""
        super().update_edge_servers(num_edge_servers, task_num, memory_limit)

        # 重新计算增强状态维度
        self.ENHANCED_STATE_DIM = (
            self.STATIC_TASK_DIM +
            self.DYNAMIC_FEATURES_DIM +
            self.STATE_COMM_DIM +
            self.STATE_MEM_DIM +
            self.STATE_COMPUTE_DIM +
            self.STATE_TTFT_DIM +
            self.STATE_TPOT_DIM +
            self.STATE_DAG_DIM +
            1
        )

        self.TASK_FEATURE_DIM = self.ENHANCED_STATE_DIM

        print(f"[ENHANCED_CONFIG] 更新后增强状态维度: {self.ENHANCED_STATE_DIM}")

    def set_custom_data_path(self, data_path):
        """手动设置自定义数据路径"""
        if os.path.exists(data_path):
            self.TRAINING_DATA_PATH = data_path

            # 更新相关文件路径
            self.TASK_FEATURES_PATH = os.path.join(data_path, 'task_{}_features.xlsx')
            self.ADJACENCY_MATRIX_PATH = os.path.join(data_path, 'task_{}_adjacency_matrix.xlsx')
            self.MACHINES_RESOURCE_PATH = os.path.join(data_path, 'machines_resource1.xlsx')
            self.MACHINE_COMMU_SPEED_PATH = os.path.join(data_path, 'machine_commu_speed1.xlsx')

            print(f"[ENHANCED_CONFIG] 已设置自定义数据路径: {data_path}")
            return True
        else:
            print(f"[ENHANCED_CONFIG] 错误: 数据路径不存在: {data_path}")
            return False
