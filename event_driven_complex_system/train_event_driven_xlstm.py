#!/usr/bin/env python3
"""
基于复杂版本的事件驱动xLSTM调度系统
结合复杂版本的网络架构和事件驱动的调度逻辑
使用新生成的DAG进行训练
"""

import os
import sys
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
import argparse
from datetime import datetime

# 添加路径
sys.path.append('..')
sys.path.append('.')

from enhanced_data_loader import EnhancedDataLoader
from improved_xlstm_networks import ImprovedXLSTMSACAgent
from improved_reward_function import ImprovedRewardFunction
from enhanced_config import EnhancedLLMConfig
from dag_generator_new import daggen_py

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class EventDrivenScheduler:
    """事件驱动调度器"""

    def __init__(self, config, data_loader):
        self.config = config
        self.data_loader = data_loader
        self.current_time = 0.0
        self.tasks = []
        self.machines = []
        self.completed_tasks = set()
        self.failed_tasks = set()
        self.running_tasks = {}  # {task_id: (machine_id, end_time)}

    def reset(self, task_features, adj_matrix, machine_resources, comm_speed, memory_status):
        """重置调度器状态"""
        self.current_time = 0.0
        self.completed_tasks = set()
        self.failed_tasks = set()
        self.running_tasks = {}

        # 初始化任务
        num_tasks = task_features.shape[1]
        self.tasks = []
        for i in range(num_tasks):
            task = {
                'id': i,
                'status': 0,  # 0: 未就绪, 1: 就绪, 2: 运行中, 3: 已完成
                'dependencies': [],
                'dependents': [],
                'cpu_cycles': task_features[0, i],
                'data_size': task_features[1, i],
                'is_llm': task_features[2, i] == 1,
                'predicted_memory': self.data_loader._task_predicted_memory.get(i, 0),
                'predicted_tokens': self.data_loader._task_predicted_output_tokens.get(i, 0)
            }
            self.tasks.append(task)

        # 构建依赖关系
        for i in range(num_tasks):
            for j in range(num_tasks):
                if adj_matrix[i, j] == 1:  # i依赖j
                    self.tasks[i]['dependencies'].append(j)
                    self.tasks[j]['dependents'].append(i)

        # 初始化机器
        self.machines = []
        for i in range(len(machine_resources)):
            machine = {
                'id': i,
                'available_memory': memory_status[i],
                'total_memory': machine_resources[i],
                'cpu_frequency': 2.0 if i == 0 else 3.0,  # 用户设备2GHz，边缘设备3GHz
                'ttft': 0.23 if i == 0 else 0.046,
                'tpot': 0.013 if i == 0 else 0.1,
                'running_tasks': []
            }
            self.machines.append(machine)

        # 更新初始就绪任务
        self._update_task_statuses()

    def _update_task_statuses(self):
        """更新任务状态"""
        for task in self.tasks:
            if task['status'] == 0:  # 未就绪任务
                # 检查所有依赖是否完成
                if all(dep_id in self.completed_tasks for dep_id in task['dependencies']):
                    task['status'] = 1  # 设为就绪

    def get_ready_tasks(self):
        """获取就绪任务列表"""
        return [task['id'] for task in self.tasks if task['status'] == 1]

    def get_state(self):
        """获取当前状态"""
        # 使用数据加载器的状态处理方法
        task_features = np.zeros((3, len(self.tasks)))
        for i, task in enumerate(self.tasks):
            task_features[0, i] = task['cpu_cycles']
            task_features[1, i] = task['data_size']
            task_features[2, i] = 1.0 if task['is_llm'] else 0.0

        # 构建邻接矩阵
        adj_matrix = np.zeros((len(self.tasks), len(self.tasks)))
        for task in self.tasks:
            for dep_id in task['dependencies']:
                adj_matrix[task['id'], dep_id] = 1

        # 构建机器资源和内存状态
        machine_resources = [m['total_memory'] for m in self.machines]
        memory_status = [m['available_memory'] for m in self.machines]
        comm_speed = np.ones((len(self.machines), len(self.machines))) * 100  # 简化通信速度

        # 更新数据加载器状态
        self.data_loader._init_step_state(task_features, adj_matrix, machine_resources, comm_speed, memory_status)

        # 更新任务状态
        for task in self.tasks:
            self.data_loader._task_status[task['id']] = task['status']

        # 获取增强状态
        state = self.data_loader.process_state_as_enhanced_sequence(
            task_features, adj_matrix, machine_resources, comm_speed, memory_status)

        return state

    def execute_action(self, task_id, machine_id):
        """执行调度动作"""
        if task_id >= len(self.tasks) or self.tasks[task_id]['status'] != 1:
            return False, "任务不可执行"

        task = self.tasks[task_id]
        machine = self.machines[machine_id]

        # 检查内存是否足够
        if task['predicted_memory'] > machine['available_memory']:
            self.failed_tasks.add(task_id)
            task['status'] = 3  # 标记为失败
            return False, "内存不足"

        # 计算执行时间
        if task['is_llm']:
            # LLM任务执行时间 = TTFT + 预测输出token数 * TPOT
            execution_time = machine['ttft'] + task['predicted_tokens'] * machine['tpot']
        else:
            # 普通任务执行时间 = CPU周期 / CPU频率
            execution_time = task['cpu_cycles'] / machine['cpu_frequency']

        # 分配资源
        machine['available_memory'] -= task['predicted_memory']
        machine['running_tasks'].append(task_id)

        # 更新任务状态
        task['status'] = 2  # 运行中
        end_time = self.current_time + execution_time
        self.running_tasks[task_id] = (machine_id, end_time)

        return True, f"任务{task_id}在机器{machine_id}上开始执行，预计{execution_time:.2f}秒后完成"

    def advance_time(self):
        """推进时间到下一个事件"""
        if not self.running_tasks:
            return False  # 没有运行中的任务

        # 找到最早完成的任务
        next_completion_time = min(end_time for _, end_time in self.running_tasks.values())
        self.current_time = next_completion_time

        # 处理所有在当前时间完成的任务
        completed_now = []
        for task_id, (machine_id, end_time) in list(self.running_tasks.items()):
            if end_time <= self.current_time:
                completed_now.append((task_id, machine_id))
                del self.running_tasks[task_id]

        # 完成任务并释放资源
        for task_id, machine_id in completed_now:
            task = self.tasks[task_id]
            machine = self.machines[machine_id]

            # 释放内存
            machine['available_memory'] += task['predicted_memory']
            machine['running_tasks'].remove(task_id)

            # 标记任务完成
            task['status'] = 3
            self.completed_tasks.add(task_id)

            # 更新依赖任务状态
            self._update_task_statuses()

        return True

    def is_done(self):
        """检查是否完成"""
        total_tasks = len(self.tasks)
        finished_tasks = len(self.completed_tasks) + len(self.failed_tasks)
        return finished_tasks >= total_tasks or len(self.get_ready_tasks()) == 0

    def get_completion_rate(self):
        """获取完成率"""
        return len(self.completed_tasks) / len(self.tasks)

    def get_makespan(self):
        """获取makespan"""
        if len(self.completed_tasks) == len(self.tasks):
            return self.current_time
        else:
            return 1000.0  # 失败惩罚

def generate_new_dag_dataset(num_dags=200, num_tasks=30, save_path="dataset/event_driven_dags"):
    """生成新的DAG数据集"""
    print(f"🔧 生成新的DAG数据集: {num_dags}个DAG，每个{num_tasks}个任务")

    os.makedirs(save_path, exist_ok=True)
    os.makedirs(f"{save_path}/task_features", exist_ok=True)
    os.makedirs(f"{save_path}/dependency_matrices", exist_ok=True)

    for dag_id in range(num_dags):
        # 生成分层DAG
        density = 0.3 + 0.3 * (dag_id / num_dags)  # 密度从0.3到0.6
        adj_matrix = daggen_py(n=num_tasks, fat=0.4, density=density, seed=dag_id)

        # 生成任务特征
        np.random.seed(dag_id)
        task_features = np.zeros((3, num_tasks))

        # CPU周期 (1-10 G cycles)
        task_features[0, :] = np.random.uniform(1.0, 10.0, num_tasks)

        # 数据大小 (0.1-5.0 MB)
        task_features[1, :] = np.random.uniform(0.1, 5.0, num_tasks)

        # LLM标记 (30%概率为LLM任务)
        llm_mask = np.random.random(num_tasks) < 0.3
        task_features[2, :] = llm_mask.astype(float)

        # 保存任务特征
        np.save(f"{save_path}/task_features/dag_{dag_id}.npy", task_features)

        # 保存依赖矩阵
        np.save(f"{save_path}/dependency_matrices/dag_{dag_id}.npy", adj_matrix)

    # 生成机器资源配置
    machine_config = {
        'user_device': {'memory': 16.0, 'cpu_freq': 2.0, 'ttft': 0.23, 'tpot': 0.013},
        'edge_devices': [
            {'memory': 28.94, 'cpu_freq': 3.0, 'ttft': 0.046, 'tpot': 0.1},
            {'memory': 30.12, 'cpu_freq': 3.0, 'ttft': 0.046, 'tpot': 0.1},
            {'memory': 31.30, 'cpu_freq': 3.0, 'ttft': 0.046, 'tpot': 0.1},
            {'memory': 29.56, 'cpu_freq': 3.0, 'ttft': 0.046, 'tpot': 0.1},
            {'memory': 30.78, 'cpu_freq': 3.0, 'ttft': 0.046, 'tpot': 0.1}
        ]
    }

    import json
    with open(f"{save_path}/machine_config.json", 'w') as f:
        json.dump(machine_config, f, indent=2)

    print(f"✅ DAG数据集生成完成，保存到: {save_path}")
    return save_path

def train_event_driven_xlstm(data_path=None, num_episodes=1000, save_interval=250,
                           device_num=5, device='cuda', network_type='xlstm'):
    """
    训练事件驱动xLSTM调度系统
    Args:
        data_path: 训练数据路径（如果为None则生成新数据）
        num_episodes: 训练回合数
        save_interval: 保存间隔
        device_num: 边缘设备数量
        device: 计算设备
        network_type: 网络类型 (xlstm, lstm, mlp)
    """
    print("🚀 开始训练事件驱动xLSTM调度系统")
    print("=" * 80)

    # 生成或使用现有数据集
    if data_path is None:
        data_path = generate_new_dag_dataset(num_dags=200, num_tasks=30)

    # 创建配置实例
    config = EnhancedLLMConfig()
    config.NUM_EDGE_SERVERS = device_num
    config.update_edge_servers(device_num)
    config.set_custom_data_path(data_path)

    print(f"📊 训练配置:")
    print(f"  数据路径: {data_path}")
    print(f"  训练回合数: {num_episodes}")
    print(f"  边缘设备数: {device_num}")
    print(f"  网络类型: {network_type}")
    print(f"  状态维度: {config.ENHANCED_STATE_DIM}")
    print(f"  序列长度: {config.SEQ_LEN}")
    print(f"  计算设备: {device}")

    # 创建数据加载器
    data_loader = EnhancedDataLoader(config, debug=False)

    # 创建事件驱动调度器
    scheduler = EventDrivenScheduler(config, data_loader)

    # 创建智能体
    agent = ImprovedXLSTMSACAgent(
        state_dim=config.ENHANCED_STATE_DIM,
        action_dim=2,  # [task_selection, machine_assignment]
        hidden_dim=256,
        seq_len=config.SEQ_LEN,
        num_machines=device_num + 1,  # 包括用户设备
        xlstm_layers=2,
        lr=3e-4,
        device=device
    )

    # 创建奖励函数
    reward_function = ImprovedRewardFunction(
        max_makespan=200.0,
        completion_weight=100.0,    # 重点关注完成率
        makespan_weight=50.0,       # 适度关注makespan
        efficiency_weight=10.0,     # 轻度关注效率
        penalty_weight=50.0,        # 失败惩罚
        step_reward_scale=1.0,      # 步骤奖励
        task_completion_reward=10.0, # 任务完成奖励
        llm_edge_bonus=5.0,         # LLM边缘执行奖励
        memory_fail_penalty=20.0,   # 内存失败惩罚
        no_ready_penalty=5.0,       # 无就绪任务惩罚
        time_penalty_factor=0.01    # 时间惩罚因子
    )

    # 创建保存目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_dir = f"models/event_driven_{network_type}_{device_num}devices_{timestamp}"
    os.makedirs(model_dir, exist_ok=True)

    # 训练历史记录
    episode_rewards = []
    episode_completion_rates = []
    episode_makespans = []
    episode_success_rates = []

    print(f"\n🎯 开始事件驱动训练...")

    for episode in range(num_episodes):
        try:
            # 随机选择DAG
            dag_idx = np.random.randint(0, 200)

            # 加载DAG数据（使用新生成的数据）
            task_features_file = f"{data_path}/task_features/dag_{dag_idx}.npy"
            dependency_file = f"{data_path}/dependency_matrices/dag_{dag_idx}.npy"

            if os.path.exists(task_features_file) and os.path.exists(dependency_file):
                task_features = np.load(task_features_file)
                adj_matrix = np.load(dependency_file)
            else:
                # 如果文件不存在，使用原有数据加载器
                task_features = data_loader.load_task_features(dag_idx % 200)
                adj_matrix = data_loader.load_adjacency_matrix(dag_idx % 200)

            # 加载机器资源
            machine_resources = data_loader.load_machines_resource()
            comm_speed = data_loader.load_machine_commu_speed()
            memory_status = data_loader.load_memory_status()

            num_tasks = task_features.shape[1]

            # 重置调度器
            scheduler.reset(task_features, adj_matrix, machine_resources, comm_speed, memory_status)

            episode_reward = 0
            step_count = 0
            max_steps = num_tasks * 3  # 增加最大步数

            # 事件驱动调度循环
            while step_count < max_steps and not scheduler.is_done():
                step_count += 1

                # 获取当前状态
                state = scheduler.get_state()
                ready_tasks = scheduler.get_ready_tasks()

                if not ready_tasks:
                    # 没有就绪任务，推进时间
                    if not scheduler.advance_time():
                        break  # 没有运行中的任务，结束
                    continue

                # 创建ready_mask
                ready_mask = np.zeros(config.SEQ_LEN)
                for i, task_id in enumerate(ready_tasks[:config.SEQ_LEN]):
                    if i < config.SEQ_LEN:
                        ready_mask[i] = 1.0

                # 转换为tensor
                state_tensor = torch.FloatTensor(state).unsqueeze(0).to(device)
                ready_mask_tensor = torch.FloatTensor(ready_mask).unsqueeze(0).to(device)

                # 选择动作
                action = agent.select_action(state_tensor, ready_mask_tensor, deterministic=False)

                # 解析动作
                task_selection = int(action[0] * len(ready_tasks))
                task_selection = min(task_selection, len(ready_tasks) - 1)
                selected_task_id = ready_tasks[task_selection]

                machine_selection = int(action[1] * len(scheduler.machines))
                machine_selection = min(machine_selection, len(scheduler.machines) - 1)

                # 执行动作
                success, message = scheduler.execute_action(selected_task_id, machine_selection)

                # 计算步骤奖励
                step_info = {
                    'task_completed_successfully': success,
                    'task_failed_memory': not success and "内存不足" in message,
                    'no_ready_tasks_but_not_done': False,
                    'is_llm': scheduler.tasks[selected_task_id]['is_llm'],
                    'assigned_machine_idx': machine_selection,
                    'task_execution_time_sec': 1.0,  # 简化
                    'predicted_memory': scheduler.tasks[selected_task_id]['predicted_memory']
                }

                step_reward, _ = reward_function.calculate_reward(step_info)
                episode_reward += step_reward

                # 推进时间到下一个事件
                scheduler.advance_time()

            # 计算最终奖励
            completion_rate = scheduler.get_completion_rate()
            makespan = scheduler.get_makespan()

            episode_info = {
                'total_tasks': num_tasks,
                'completed_tasks': len(scheduler.completed_tasks),
                'makespan': makespan,
                'failed_tasks': len(scheduler.failed_tasks),
                'deadlock_occurred': False
            }

            final_reward, _ = reward_function.calculate_reward({}, episode_info)
            episode_reward += final_reward

            # 记录结果
            episode_rewards.append(episode_reward)
            episode_completion_rates.append(completion_rate)
            episode_makespans.append(makespan if completion_rate >= 1.0 else 0.0)
            episode_success_rates.append(1.0 if completion_rate >= 0.8 else 0.0)

            # 打印进度
            if episode % 50 == 0:
                avg_completion = np.mean(episode_completion_rates[-50:]) if episode > 0 else completion_rate
                avg_success = np.mean(episode_success_rates[-50:]) if episode > 0 else episode_success_rates[-1]
                avg_makespan = np.mean([m for m in episode_makespans[-50:] if m > 0]) if any(m > 0 for m in episode_makespans[-50:]) else 0

                print(f"Episode {episode:4d}: "
                      f"完成率={completion_rate:.1%}, "
                      f"Makespan={makespan:.1f}s, "
                      f"奖励={episode_reward:.1f} | "
                      f"近50回合: 平均完成率={avg_completion:.1%}, "
                      f"成功率={avg_success:.1%}, "
                      f"平均Makespan={avg_makespan:.1f}s")

            # 保存模型
            if episode > 0 and episode % save_interval == 0:
                model_path = f"{model_dir}/model_episode_{episode}"
                agent.save_models(model_path)
                print(f"💾 模型已保存: {model_path}")

        except Exception as e:
            print(f"❌ Episode {episode} 出错: {e}")
            continue

    # 保存最终模型和结果
    final_model_path = f"{model_dir}/final_model"
    agent.save_models(final_model_path)

    # 保存训练历史
    history = {
        'episode_rewards': episode_rewards,
        'episode_completion_rates': episode_completion_rates,
        'episode_makespans': episode_makespans,
        'episode_success_rates': episode_success_rates
    }

    import pickle
    with open(f"{model_dir}/training_history.pkl", 'wb') as f:
        pickle.dump(history, f)

    # 绘制训练曲线
    plot_training_results(history, model_dir)

    print(f"\n🎉 训练完成!")
    print(f"📊 最终统计:")
    print(f"  平均完成率: {np.mean(episode_completion_rates):.1%}")
    print(f"  成功率: {np.mean(episode_success_rates):.1%}")
    print(f"  平均有效Makespan: {np.mean([m for m in episode_makespans if m > 0]):.1f}s")
    print(f"💾 模型保存路径: {model_dir}")

def plot_training_results(history, save_dir):
    """绘制训练结果"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # 完成率
    axes[0, 0].plot(history['episode_completion_rates'])
    axes[0, 0].set_title('完成率')
    axes[0, 0].set_xlabel('Episode')
    axes[0, 0].set_ylabel('完成率')
    axes[0, 0].grid(True)

    # 成功率（滑动平均）
    success_rates = history['episode_success_rates']
    window_size = 50
    if len(success_rates) >= window_size:
        smoothed = [np.mean(success_rates[max(0, i-window_size):i+1]) for i in range(len(success_rates))]
        axes[0, 1].plot(smoothed)
    else:
        axes[0, 1].plot(success_rates)
    axes[0, 1].set_title('成功率 (滑动平均)')
    axes[0, 1].set_xlabel('Episode')
    axes[0, 1].set_ylabel('成功率')
    axes[0, 1].grid(True)

    # Makespan（只显示成功的）
    valid_makespans = [m for m in history['episode_makespans'] if m > 0]
    if valid_makespans:
        axes[1, 0].plot(valid_makespans)
        axes[1, 0].set_title('Makespan (仅成功回合)')
        axes[1, 0].set_xlabel('成功回合')
        axes[1, 0].set_ylabel('Makespan (秒)')
        axes[1, 0].grid(True)

    # 奖励
    axes[1, 1].plot(history['episode_rewards'])
    axes[1, 1].set_title('回合奖励')
    axes[1, 1].set_xlabel('Episode')
    axes[1, 1].set_ylabel('奖励')
    axes[1, 1].grid(True)

    plt.tight_layout()
    plt.savefig(f'{save_dir}/training_results.png', dpi=300, bbox_inches='tight')
    plt.close()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='训练事件驱动xLSTM调度系统')
    parser.add_argument('--episodes', type=int, default=1000, help='训练回合数')
    parser.add_argument('--device_num', type=int, default=5, help='边缘设备数量')
    parser.add_argument('--save_interval', type=int, default=250, help='模型保存间隔')
    parser.add_argument('--data_path', type=str, default="../dataset/training/DAG_30_layered_fat0.4_density0.5_5devices", help='数据路径（为空则生成新数据）')
    parser.add_argument('--network_type', type=str, default='xlstm', choices=['xlstm', 'lstm', 'mlp'], help='网络类型')
    parser.add_argument('--device', type=str, default='cuda', help='计算设备')

    args = parser.parse_args()

    train_event_driven_xlstm(
        data_path=args.data_path,
        num_episodes=args.episodes,
        save_interval=args.save_interval,
        device_num=args.device_num,
        device=args.device,
        network_type=args.network_type
    )
