"""
增强的SAC智能体
支持复合动作空间和动态状态管理
"""
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
import random
from collections import deque
from enhanced_sac_networks import EnhancedActorNetwork, EnhancedCriticNetwork

class EnhancedReplayBuffer:
    """增强的经验回放缓冲区"""

    def __init__(self, capacity, state_dim, action_dim, seq_len):
        self.capacity = capacity
        self.buffer = deque(maxlen=capacity)
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.seq_len = seq_len

    def push(self, state, action, reward, next_state, done, ready_mask=None, next_ready_mask=None):
        """添加经验"""
        experience = {
            'state': state,
            'action': action,
            'reward': reward,
            'next_state': next_state,
            'done': done,
            'ready_mask': ready_mask,
            'next_ready_mask': next_ready_mask
        }
        self.buffer.append(experience)

    def sample(self, batch_size):
        """采样批次经验"""
        batch = random.sample(self.buffer, batch_size)

        states = torch.stack([exp['state'] for exp in batch])
        actions = torch.stack([exp['action'] for exp in batch])
        rewards = torch.tensor([exp['reward'] for exp in batch], dtype=torch.float32)
        next_states = torch.stack([exp['next_state'] for exp in batch])
        dones = torch.tensor([exp['done'] for exp in batch], dtype=torch.bool)

        ready_masks = None
        next_ready_masks = None

        if batch[0]['ready_mask'] is not None:
            ready_masks = torch.stack([exp['ready_mask'] for exp in batch])
            next_ready_masks = torch.stack([exp['next_ready_mask'] for exp in batch])

        return states, actions, rewards, next_states, dones, ready_masks, next_ready_masks

    def __len__(self):
        return len(self.buffer)

class EnhancedSACAgent:
    """增强的SAC智能体"""

    def __init__(self, config, device='cuda'):
        self.config = config
        self.device = device

        # 网络参数
        self.state_dim = config.ENHANCED_STATE_DIM
        self.action_dim = config.COMPOUND_ACTION_DIM
        self.seq_len = config.SEQ_LEN
        self.hidden_dim = 256

        # 初始化网络
        self.actor = EnhancedActorNetwork(
            self.state_dim, self.action_dim, self.hidden_dim,
            self.seq_len, config.NUM_EDGE_SERVERS + 1
        ).to(device)

        self.critic1 = EnhancedCriticNetwork(
            self.state_dim, self.action_dim, self.hidden_dim, self.seq_len
        ).to(device)

        self.critic2 = EnhancedCriticNetwork(
            self.state_dim, self.action_dim, self.hidden_dim, self.seq_len
        ).to(device)

        # 目标网络
        self.target_critic1 = EnhancedCriticNetwork(
            self.state_dim, self.action_dim, self.hidden_dim, self.seq_len
        ).to(device)

        self.target_critic2 = EnhancedCriticNetwork(
            self.state_dim, self.action_dim, self.hidden_dim, self.seq_len
        ).to(device)

        # 复制参数到目标网络
        self.target_critic1.load_state_dict(self.critic1.state_dict())
        self.target_critic2.load_state_dict(self.critic2.state_dict())

        # 优化器
        self.actor_optimizer = optim.Adam(self.actor.parameters(), lr=config.LEARNING_RATE)
        self.critic1_optimizer = optim.Adam(self.critic1.parameters(), lr=config.LEARNING_RATE)
        self.critic2_optimizer = optim.Adam(self.critic2.parameters(), lr=config.LEARNING_RATE)

        # SAC参数
        self.gamma = config.GAMMA
        self.tau = config.TAU
        self.alpha = config.ALPHA
        self.target_entropy = -config.COMPOUND_ACTION_DIM  # 调整目标熵

        # 自动调整alpha
        if config.AUTO_ALPHA:
            self.log_alpha = torch.zeros(1, requires_grad=True, device=device)
            self.alpha_optimizer = optim.Adam([self.log_alpha], lr=config.LEARNING_RATE)
        else:
            self.log_alpha = None
            self.alpha_optimizer = None

        # 经验回放
        self.replay_buffer = EnhancedReplayBuffer(
            config.REPLAY_BUFFER_SIZE, self.state_dim, self.action_dim, self.seq_len
        )

        self.batch_size = config.BATCH_SIZE
        self.update_count = 0

        print(f"[ENHANCED_SAC] 初始化完成")
        print(f"  状态维度: {self.state_dim}")
        print(f"  动作维度: {self.action_dim}")
        print(f"  序列长度: {self.seq_len}")
        print(f"  目标熵: {self.target_entropy}")

    def select_action(self, state, ready_mask=None, deterministic=False):
        """选择动作"""
        if not isinstance(state, torch.Tensor):
            state = torch.FloatTensor(state).unsqueeze(0).to(self.device)

        if ready_mask is not None and not isinstance(ready_mask, torch.Tensor):
            ready_mask = torch.FloatTensor(ready_mask).unsqueeze(0).to(self.device)

        with torch.no_grad():
            if deterministic:
                # 确定性动作（用于评估）
                task_mean, _, machine_mean, _ = self.actor(state, ready_mask)
                action = torch.stack([task_mean, machine_mean], dim=-1)
            else:
                # 随机动作（用于探索）
                action, _ = self.actor.sample(state, ready_mask)

        return action.cpu().numpy()[0]

    def store_transition(self, state, action, reward, next_state, done,
                        ready_mask=None, next_ready_mask=None):
        """存储转移经验"""
        if not isinstance(state, torch.Tensor):
            state = torch.FloatTensor(state)
        if not isinstance(action, torch.Tensor):
            action = torch.FloatTensor(action)
        if not isinstance(next_state, torch.Tensor):
            next_state = torch.FloatTensor(next_state)

        if ready_mask is not None and not isinstance(ready_mask, torch.Tensor):
            ready_mask = torch.FloatTensor(ready_mask)
        if next_ready_mask is not None and not isinstance(next_ready_mask, torch.Tensor):
            next_ready_mask = torch.FloatTensor(next_ready_mask)

        self.replay_buffer.push(state, action, reward, next_state, done,
                               ready_mask, next_ready_mask)

    def update(self):
        """更新网络参数"""
        if len(self.replay_buffer) < self.batch_size:
            return {}

        # 采样批次数据
        states, actions, rewards, next_states, dones, ready_masks, next_ready_masks = \
            self.replay_buffer.sample(self.batch_size)

        states = states.to(self.device)
        actions = actions.to(self.device)
        rewards = rewards.to(self.device)
        next_states = next_states.to(self.device)
        dones = dones.to(self.device)

        if ready_masks is not None:
            ready_masks = ready_masks.to(self.device)
            next_ready_masks = next_ready_masks.to(self.device)

        # 更新Critic网络
        critic_loss = self._update_critics(states, actions, rewards, next_states, dones,
                                         ready_masks, next_ready_masks)

        # 更新Actor网络
        actor_loss, alpha_loss = self._update_actor(states, ready_masks)

        # 软更新目标网络
        self._soft_update_targets()

        self.update_count += 1

        return {
            'critic_loss': critic_loss,
            'actor_loss': actor_loss,
            'alpha_loss': alpha_loss if alpha_loss is not None else 0.0,
            'alpha': self.alpha
        }

    def _update_critics(self, states, actions, rewards, next_states, dones,
                       ready_masks, next_ready_masks):
        """更新Critic网络"""
        with torch.no_grad():
            # 计算目标Q值
            next_actions, next_log_probs = self.actor.sample(next_states, next_ready_masks)
            target_q1 = self.target_critic1(next_states, next_actions)
            target_q2 = self.target_critic2(next_states, next_actions)
            target_q = torch.min(target_q1, target_q2) - self.alpha * next_log_probs.unsqueeze(-1)
            target_q = rewards.unsqueeze(-1) + self.gamma * (1 - dones.float().unsqueeze(-1)) * target_q

        # 当前Q值
        current_q1 = self.critic1(states, actions)
        current_q2 = self.critic2(states, actions)

        # Critic损失
        critic1_loss = F.mse_loss(current_q1, target_q)
        critic2_loss = F.mse_loss(current_q2, target_q)

        # 更新Critic1
        self.critic1_optimizer.zero_grad()
        critic1_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.critic1.parameters(), max_norm=1.0)
        self.critic1_optimizer.step()

        # 更新Critic2
        self.critic2_optimizer.zero_grad()
        critic2_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.critic2.parameters(), max_norm=1.0)
        self.critic2_optimizer.step()

        return (critic1_loss + critic2_loss).item() / 2

    def _update_actor(self, states, ready_masks):
        """更新Actor网络"""
        # 采样动作
        actions, log_probs = self.actor.sample(states, ready_masks)

        # 计算Q值
        q1 = self.critic1(states, actions)
        q2 = self.critic2(states, actions)
        q = torch.min(q1, q2)

        # Actor损失
        actor_loss = (self.alpha * log_probs.unsqueeze(-1) - q).mean()

        # 更新Actor
        self.actor_optimizer.zero_grad()
        actor_loss.backward()

        # 添加梯度裁剪防止梯度爆炸
        torch.nn.utils.clip_grad_norm_(self.actor.parameters(), max_norm=1.0)

        self.actor_optimizer.step()

        # 更新alpha
        alpha_loss = None
        if self.alpha_optimizer is not None:
            alpha_loss = -(self.log_alpha * (log_probs + self.target_entropy).detach()).mean()

            self.alpha_optimizer.zero_grad()
            alpha_loss.backward()
            self.alpha_optimizer.step()

            self.alpha = self.log_alpha.exp().item()
            alpha_loss = alpha_loss.item()

        return actor_loss.item(), alpha_loss

    def _soft_update_targets(self):
        """软更新目标网络"""
        for target_param, param in zip(self.target_critic1.parameters(), self.critic1.parameters()):
            target_param.data.copy_(self.tau * param.data + (1 - self.tau) * target_param.data)

        for target_param, param in zip(self.target_critic2.parameters(), self.critic2.parameters()):
            target_param.data.copy_(self.tau * param.data + (1 - self.tau) * target_param.data)

    def save_models(self, filepath):
        """保存模型"""
        torch.save({
            'actor': self.actor.state_dict(),
            'critic1': self.critic1.state_dict(),
            'critic2': self.critic2.state_dict(),
            'target_critic1': self.target_critic1.state_dict(),
            'target_critic2': self.target_critic2.state_dict(),
            'actor_optimizer': self.actor_optimizer.state_dict(),
            'critic1_optimizer': self.critic1_optimizer.state_dict(),
            'critic2_optimizer': self.critic2_optimizer.state_dict(),
            'log_alpha': self.log_alpha,
            'alpha_optimizer': self.alpha_optimizer.state_dict() if self.alpha_optimizer else None,
        }, filepath)

    def load_models(self, filepath):
        """加载模型"""
        checkpoint = torch.load(filepath, map_location=self.device)

        self.actor.load_state_dict(checkpoint['actor'])
        self.critic1.load_state_dict(checkpoint['critic1'])
        self.critic2.load_state_dict(checkpoint['critic2'])
        self.target_critic1.load_state_dict(checkpoint['target_critic1'])
        self.target_critic2.load_state_dict(checkpoint['target_critic2'])

        self.actor_optimizer.load_state_dict(checkpoint['actor_optimizer'])
        self.critic1_optimizer.load_state_dict(checkpoint['critic1_optimizer'])
        self.critic2_optimizer.load_state_dict(checkpoint['critic2_optimizer'])

        if checkpoint['log_alpha'] is not None:
            self.log_alpha = checkpoint['log_alpha']
            if checkpoint['alpha_optimizer'] is not None:
                self.alpha_optimizer.load_state_dict(checkpoint['alpha_optimizer'])
