"""
改进的xLSTM网络架构
实现"先选任务，再分配机器"的清晰逻辑
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import sys
import os

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

try:
    from xlstm import (
        xLSTMBlockStack,
        xLSTMBlockStackConfig,
        mLSTMBlockConfig,
        mLSTMLayerConfig,
        sLSTMBlockConfig,
        sLSTMLayerConfig,
        FeedForwardConfig,
    )
    XLSTM_AVAILABLE = True
    print("[IMPROVED_xLSTM] 真正的xLSTM库可用")
except ImportError:
    print("[IMPROVED_xLSTM] 警告: 真正的xLSTM库不可用，将使用简化版本")
    XLSTM_AVAILABLE = False

def create_xlstm_config(seq_len, hidden_dim, num_blocks):
    """创建xLSTM配置"""
    if not XLSTM_AVAILABLE:
        return None

    # 确保hidden_dim是16的倍数
    hidden_dim = ((hidden_dim + 15) // 16) * 16

    cfg = xLSTMBlockStackConfig(
        mlstm_block=mLSTMBlockConfig(
            mlstm=mLSTMLayerConfig(
                conv1d_kernel_size=2,
                qkv_proj_blocksize=2,
                num_heads=2
            )
        ),
        context_length=seq_len,
        num_blocks=num_blocks,
        embedding_dim=hidden_dim,
        slstm_at=[]
    )
    return cfg

class SimpleXLSTMBlock(nn.Module):
    """简化的xLSTM块"""
    def __init__(self, input_dim, hidden_dim):
        super(SimpleXLSTMBlock, self).__init__()
        self.lstm = nn.LSTM(input_dim, hidden_dim, batch_first=True)
        self.layer_norm = nn.LayerNorm(hidden_dim)

    def forward(self, x):
        output, _ = self.lstm(x)
        return self.layer_norm(output)

class ImprovedXLSTMActorNetwork(nn.Module):
    """改进的xLSTM Actor网络 - 实现清晰的任务选择+机器分配逻辑"""

    def __init__(self, state_dim, action_dim, hidden_dim=256, seq_len=32,
                 num_machines=5, xlstm_layers=2):
        super(ImprovedXLSTMActorNetwork, self).__init__()

        self.state_dim = state_dim
        self.action_dim = action_dim
        self.hidden_dim = ((hidden_dim + 15) // 16) * 16  # 确保是16的倍数
        self.seq_len = seq_len
        self.num_machines = num_machines
        self.xlstm_layers = xlstm_layers

        print(f"[IMPROVED_xLSTM_ACTOR] 初始化改进版xLSTM Actor")
        print(f"  状态维度: {state_dim}, 隐藏维度: {self.hidden_dim}")
        print(f"  序列长度: {seq_len}, 机器数量: {num_machines}")

        # 输入投影层
        self.input_projection = nn.Linear(state_dim, self.hidden_dim)

        # xLSTM编码器
        if XLSTM_AVAILABLE:
            xlstm_cfg = create_xlstm_config(seq_len, self.hidden_dim, xlstm_layers)
            self.xlstm_encoder = xLSTMBlockStack(xlstm_cfg)
            print(f"[IMPROVED_xLSTM_ACTOR] 使用真正的xLSTM实现")
        else:
            self.xlstm_encoder = nn.ModuleList([
                SimpleXLSTMBlock(self.hidden_dim, self.hidden_dim)
                for _ in range(xlstm_layers)
            ])
            print(f"[IMPROVED_xLSTM_ACTOR] 使用简化版xLSTM实现")

        # 全局上下文提取
        self.global_context_layer = nn.Sequential(
            nn.Linear(self.hidden_dim, self.hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(self.hidden_dim, self.hidden_dim)
        )

        # 任务选择头 - 直接输出logits
        self.task_selector = nn.Sequential(
            nn.Linear(self.hidden_dim, self.hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(self.hidden_dim, self.hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(self.hidden_dim // 2, 1)
        )

        # 机器分配头 - 针对选定任务
        self.machine_allocator = nn.Sequential(
            nn.Linear(self.hidden_dim * 2, self.hidden_dim),  # selected_task + global_context
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(self.hidden_dim, self.hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(self.hidden_dim // 2, 1)
        )

        # 可学习的机器分配log_std
        self.machine_log_std_head = nn.Sequential(
            nn.Linear(self.hidden_dim * 2, self.hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(self.hidden_dim // 2, 1)
        )

        # 数值稳定性参数
        self.log_std_min = -10
        self.log_std_max = 1

        self._init_weights()

    def _init_weights(self):
        """初始化网络权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight, gain=0.1)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0.0)

        # 🔧 特别为任务选择头设置更好的初始化，避免偏向第一个任务
        for i, layer in enumerate(self.task_selector):
            if isinstance(layer, nn.Linear):
                # 使用更小的权重初始化，减少初始偏差
                nn.init.xavier_uniform_(layer.weight, gain=0.01)
                if layer.bias is not None:
                    if i == len(self.task_selector) - 1:  # 最后一层
                        # 最后一层的偏置设为0，确保初始logits接近0
                        nn.init.constant_(layer.bias, 0.0)
                    else:
                        # 中间层使用小的随机偏置
                        nn.init.uniform_(layer.bias, -0.01, 0.01)

    def forward(self, state_sequence, ready_mask=None):
        """
        前向传播 - 只进行编码，不进行采样
        Args:
            state_sequence: [batch_size, seq_len, state_dim]
            ready_mask: [batch_size, seq_len] - 1表示就绪任务，0表示非就绪
        Returns:
            task_selection_logits: [batch_size, seq_len] - 任务选择的logits（已应用mask）
            global_context: [batch_size, hidden_dim] - 全局上下文
            encoded: [batch_size, seq_len, hidden_dim] - 编码后的序列
        """
        batch_size, seq_len, _ = state_sequence.shape

        # 1. 输入投影
        projected = self.input_projection(state_sequence)  # [B, L, H]

        # 2. xLSTM编码
        if XLSTM_AVAILABLE:
            encoded = self.xlstm_encoder(projected)  # [B, L, H]
        else:
            encoded = projected
            for xlstm_layer in self.xlstm_encoder:
                encoded = xlstm_layer(encoded)  # [B, L, H]

        # 3. 全局上下文（只对就绪任务进行平均池化）
        if ready_mask is not None:
            mask_expanded = ready_mask.unsqueeze(-1).expand_as(encoded)
            masked_encoded = encoded * mask_expanded
            ready_count = ready_mask.sum(dim=1, keepdim=True).clamp(min=1)
            global_context = masked_encoded.sum(dim=1) / ready_count
        else:
            global_context = encoded.mean(dim=1)  # [B, H]

        global_context = self.global_context_layer(global_context)  # [B, H]

        # 4. 任务选择 - 直接输出logits
        task_scores = self.task_selector(encoded).squeeze(-1)  # [B, L]

        # 应用ready_mask（将非就绪任务的分数设为合理的负值）
        task_selection_logits = task_scores
        if ready_mask is not None:
            # 🔧 修复：使用更合理的掩码值，避免数值不稳定
            mask_value = -100.0  # 足够大的负值，但不会导致数值问题
            task_selection_logits = task_scores.masked_fill(~ready_mask.bool(), mask_value)

        # 添加调试信息
        if hasattr(self, '_debug_counter'):
            self._debug_counter += 1
        else:
            self._debug_counter = 1

        if self._debug_counter % 100 == 0:  # 每100次打印一次
            print(f"[TASK_SELECTOR_DEBUG] Logits分布: min={task_selection_logits.min().item():.3f}, "
                  f"max={task_selection_logits.max().item():.3f}, "
                  f"std={task_selection_logits.std().item():.3f}")

            # 🔍 打印详细的序列处理信息
            if ready_mask is not None:
                ready_indices = torch.nonzero(ready_mask[0]).squeeze(-1).tolist()
                print(f"[SEQUENCE_DEBUG] 就绪任务: {ready_indices}")
                if ready_indices:
                    ready_logits = task_selection_logits[0][ready_indices]
                    print(f"[SEQUENCE_DEBUG] 就绪任务logits: {ready_logits.tolist()}")

                    # 🔧 计算就绪任务的softmax概率分布
                    if len(ready_indices) > 1:
                        ready_probs = F.softmax(ready_logits, dim=0)
                        print(f"[SEQUENCE_DEBUG] 就绪任务概率: {ready_probs.tolist()}")
                        print(f"[SEQUENCE_DEBUG] 任务选择熵: {-(ready_probs * torch.log(ready_probs + 1e-10)).sum().item():.3f}")

                        # 检查是否存在强烈偏好
                        max_prob = ready_probs.max().item()
                        if max_prob > 0.9:
                            print(f"[SEQUENCE_DEBUG] ⚠️ 检测到强烈偏好: 最高概率 = {max_prob:.3f}")

                        # 计算概率比例
                        sorted_probs, _ = torch.sort(ready_probs, descending=True)
                        if len(sorted_probs) >= 2:
                            ratio = sorted_probs[0] / sorted_probs[1] if sorted_probs[1] > 0 else float('inf')
                            print(f"[SEQUENCE_DEBUG] 前两个概率比例: {ratio:.2f}")

            # 打印编码后的序列统计
            print(f"[SEQUENCE_DEBUG] 编码序列: shape={encoded.shape}, "
                  f"min={encoded.min().item():.3f}, max={encoded.max().item():.3f}, "
                  f"mean={encoded.mean().item():.3f}")

            # 打印全局上下文
            print(f"[SEQUENCE_DEBUG] 全局上下文: shape={global_context.shape}, "
                  f"norm={global_context.norm().item():.3f}")

        return task_selection_logits, global_context, encoded

    def get_machine_action_for_task(self, selected_task_embedding, global_context):
        """
        为选定的任务计算机器分配动作
        Args:
            selected_task_embedding: [batch_size, hidden_dim] - 选定任务的嵌入
            global_context: [batch_size, hidden_dim] - 全局上下文
        Returns:
            machine_action_mean: [batch_size, 1] - 机器动作均值
            machine_action_log_std: [batch_size, 1] - 机器动作log标准差
        """
        # 拼接选定任务嵌入和全局上下文
        combined_features = torch.cat([selected_task_embedding, global_context], dim=-1)

        # 计算机器动作均值
        machine_action_mean = self.machine_allocator(combined_features)
        machine_action_mean = torch.tanh(machine_action_mean)  # 限制在[-1, 1]

        # 计算可学习的log_std
        machine_action_log_std = self.machine_log_std_head(combined_features)
        machine_action_log_std = torch.clamp(
            machine_action_log_std, self.log_std_min, self.log_std_max
        )

        return machine_action_mean.squeeze(-1), machine_action_log_std.squeeze(-1)

    def sample(self, state_sequence, ready_mask=None):
        """
        采样动作 - 实现"先选任务，再分配机器"的逻辑，使用真正的离散采样
        Args:
            state_sequence: [batch_size, seq_len, state_dim]
            ready_mask: [batch_size, seq_len]
        Returns:
            actions: [batch_size, 2] - [task_index_normalized, machine_action]
            log_probs: [batch_size] - 总log概率
            selected_task_indices: [batch_size] - 选定的任务索引
            selected_task_embeddings: [batch_size, hidden_dim] - 选定任务的嵌入
        """
        batch_size = state_sequence.shape[0]

        # 1. 前向传播获取编码（logits已经应用了mask）
        task_logits, global_context, encoded = self.forward(state_sequence, ready_mask)

        # 2. 任务选择 - 改进的采样策略
        # 🔧 修复：增强探索性，避免总是选择第一个任务

        # 检查是否只有一个就绪任务
        if ready_mask is not None:
            ready_counts = ready_mask.sum(dim=-1)  # [batch_size]
            single_task_mask = ready_counts == 1
        else:
            single_task_mask = torch.zeros(batch_size, dtype=torch.bool, device=task_logits.device)

        # 对于只有一个就绪任务的情况，直接选择
        single_task_indices = torch.zeros(batch_size, dtype=torch.long, device=task_logits.device)
        if single_task_mask.any():
            for i in range(batch_size):
                if single_task_mask[i] and ready_mask is not None:
                    ready_idx = torch.nonzero(ready_mask[i]).squeeze(-1)
                    if len(ready_idx) > 0:
                        single_task_indices[i] = ready_idx[0]

        # 对于多个就绪任务的情况，使用改进的采样
        # 添加噪声增强探索
        exploration_noise = 0.1  # 探索噪声强度
        noisy_logits = task_logits + torch.randn_like(task_logits) * exploration_noise

        # 使用温度缩放
        temperature = getattr(self, 'temperature', 1.5)  # 适中的温度
        scaled_logits = noisy_logits / temperature

        task_dist = torch.distributions.Categorical(logits=scaled_logits)
        selected_task_indices = task_dist.sample()  # [batch_size] - 离散任务索引

        # 对于只有一个就绪任务的情况，覆盖选择结果
        if single_task_mask.any():
            selected_task_indices = torch.where(single_task_mask, single_task_indices, selected_task_indices)

        # 使用原始logits计算log_prob（用于训练）
        original_dist = torch.distributions.Categorical(logits=task_logits)
        task_log_probs = original_dist.log_prob(selected_task_indices)  # [batch_size]

        # 3. 获取选定任务的嵌入
        batch_indices = torch.arange(batch_size, device=state_sequence.device)
        selected_task_embeddings = encoded[batch_indices, selected_task_indices]  # [batch_size, hidden_dim]

        # 4. 为选定任务分配机器（使用可学习的log_std）
        machine_mean, machine_log_std = self.get_machine_action_for_task(
            selected_task_embeddings, global_context
        )

        # 从Normal分布中采样机器动作
        machine_std = machine_log_std.exp()
        machine_dist = torch.distributions.Normal(machine_mean, machine_std)
        machine_action_raw = machine_dist.rsample()  # [batch_size]
        machine_action = torch.tanh(machine_action_raw)  # [batch_size]

        # 计算机器动作的log概率（考虑tanh变换）
        machine_log_probs = machine_dist.log_prob(machine_action_raw) - \
                           torch.log(1 - machine_action.pow(2) + 1e-6)

        # 5. 组合动作和log概率
        # 将任务索引归一化到[-1, 1]（用于与环境交互）
        task_action_normalized = (selected_task_indices.float() / max(self.seq_len - 1, 1)) * 2 - 1

        actions = torch.stack([task_action_normalized, machine_action], dim=-1)  # [batch_size, 2]
        total_log_probs = task_log_probs + machine_log_probs  # [batch_size]

        # 🔍 打印采样过程的详细信息（每100次打印一次）
        if hasattr(self, '_sample_counter'):
            self._sample_counter += 1
        else:
            self._sample_counter = 1

        if self._sample_counter % 100 == 0:
            print(f"[SAMPLING_DEBUG] 采样过程:")
            print(f"  选中任务索引: {selected_task_indices.tolist()}")
            print(f"  任务动作归一化: {task_action_normalized.tolist()}")
            print(f"  机器动作: {machine_action.tolist()}")
            print(f"  任务log_prob: {task_log_probs.tolist()}")
            print(f"  机器log_prob: {machine_log_probs.tolist()}")
            print(f"  总log_prob: {total_log_probs.tolist()}")
            print(f"  最终动作: {actions.tolist()}")

        return actions, total_log_probs, selected_task_indices, selected_task_embeddings

class ImprovedXLSTMCriticNetwork(nn.Module):
    """改进的xLSTM Critic网络 - 明确评估具体的任务选择和机器分配"""

    def __init__(self, state_dim, action_dim, hidden_dim=256, seq_len=32, xlstm_layers=2):
        super(ImprovedXLSTMCriticNetwork, self).__init__()

        self.state_dim = state_dim
        self.action_dim = action_dim
        self.hidden_dim = ((hidden_dim + 15) // 16) * 16
        self.seq_len = seq_len
        self.xlstm_layers = xlstm_layers

        # 输入投影层
        self.input_projection = nn.Linear(state_dim, self.hidden_dim)

        # xLSTM编码器
        if XLSTM_AVAILABLE:
            xlstm_cfg = create_xlstm_config(seq_len, self.hidden_dim, xlstm_layers)
            self.xlstm_encoder = xLSTMBlockStack(xlstm_cfg)
        else:
            self.xlstm_encoder = nn.ModuleList([
                SimpleXLSTMBlock(self.hidden_dim, self.hidden_dim)
                for _ in range(xlstm_layers)
            ])

        # 状态编码
        self.state_encoder = nn.Sequential(
            nn.Linear(self.hidden_dim, self.hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(self.hidden_dim, self.hidden_dim)
        )

        # 任务嵌入编码（用于处理选定的任务）
        self.task_embedding_encoder = nn.Sequential(
            nn.Linear(self.hidden_dim, self.hidden_dim),
            nn.ReLU(),
            nn.Linear(self.hidden_dim, self.hidden_dim // 2)
        )

        # 机器动作编码
        self.machine_action_encoder = nn.Sequential(
            nn.Linear(1, self.hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(self.hidden_dim // 2, self.hidden_dim // 2)
        )

        # Q值输出头
        self.q_head = nn.Sequential(
            nn.Linear(self.hidden_dim + self.hidden_dim // 2 + self.hidden_dim // 2, self.hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(self.hidden_dim, self.hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(self.hidden_dim // 2, 1)
        )

        self._init_weights()

        # 只在第一次初始化时打印信息
        if not hasattr(ImprovedXLSTMCriticNetwork, '_init_count'):
            ImprovedXLSTMCriticNetwork._init_count = 0
        ImprovedXLSTMCriticNetwork._init_count += 1

        if ImprovedXLSTMCriticNetwork._init_count <= 2:
            print(f"[IMPROVED_xLSTM_CRITIC] 初始化完成: state_dim={state_dim}")

    def _init_weights(self):
        """初始化网络权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight, gain=0.1)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0.0)

    def forward(self, state_sequence, action, selected_task_embedding=None):
        """
        前向传播
        Args:
            state_sequence: [batch_size, seq_len, state_dim]
            action: [batch_size, 2] - [task_action, machine_action]
            selected_task_embedding: [batch_size, hidden_dim] - 可选的选定任务嵌入
        Returns:
            q_value: [batch_size, 1]
        """
        batch_size = state_sequence.shape[0]

        # 1. 状态编码
        projected = self.input_projection(state_sequence)  # [B, L, H]

        if XLSTM_AVAILABLE:
            encoded = self.xlstm_encoder(projected)
        else:
            encoded = projected
            for xlstm_layer in self.xlstm_encoder:
                encoded = xlstm_layer(encoded)

        # 全局状态特征
        state_features = encoded.mean(dim=1)  # [B, H]
        state_encoded = self.state_encoder(state_features)  # [B, H]

        # 2. 任务嵌入编码
        if selected_task_embedding is not None:
            # 使用提供的任务嵌入
            task_encoded = self.task_embedding_encoder(selected_task_embedding)
        else:
            # 从动作中推断任务嵌入（备用方案）
            task_action = action[:, 0]  # [B]
            # 将归一化的任务动作转换回索引
            task_indices = ((task_action + 1) / 2 * (self.seq_len - 1)).long()
            task_indices = torch.clamp(task_indices, 0, self.seq_len - 1)

            # 获取对应的任务嵌入
            batch_indices = torch.arange(batch_size, device=state_sequence.device)
            task_embeddings = encoded[batch_indices, task_indices]
            task_encoded = self.task_embedding_encoder(task_embeddings)

        # 3. 机器动作编码
        machine_action = action[:, 1:2]  # [B, 1]
        machine_encoded = self.machine_action_encoder(machine_action)  # [B, H//2]

        # 4. 组合所有特征并输出Q值
        combined = torch.cat([state_encoded, task_encoded, machine_encoded], dim=-1)
        q_value = self.q_head(combined)

        return q_value

class ImprovedXLSTMSACAgent:
    """改进的xLSTM SAC智能体 - 实现清晰的任务选择+机器分配逻辑"""

    def __init__(self, state_dim, action_dim, hidden_dim=256, seq_len=32,
                 num_machines=6, xlstm_layers=2, lr=3e-4, device='cuda'):
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.hidden_dim = hidden_dim
        self.seq_len = seq_len
        self.num_machines = num_machines
        self.device = device

        # 训练参数
        self.lr = lr
        self.gamma = 0.99
        self.tau = 0.005
        self.alpha = 0.2
        self.target_entropy = -action_dim

        print(f"[IMPROVED_xLSTM_SAC] 初始化改进版xLSTM SAC智能体")
        print(f"  状态维度: {state_dim}, 动作维度: {action_dim}")
        print(f"  隐藏维度: {hidden_dim}, 序列长度: {seq_len}")

        # 创建网络
        self.actor = ImprovedXLSTMActorNetwork(
            state_dim, action_dim, hidden_dim, seq_len, num_machines, xlstm_layers
        ).to(device)

        self.critic1 = ImprovedXLSTMCriticNetwork(
            state_dim, action_dim, hidden_dim, seq_len, xlstm_layers
        ).to(device)

        self.critic2 = ImprovedXLSTMCriticNetwork(
            state_dim, action_dim, hidden_dim, seq_len, xlstm_layers
        ).to(device)

        # 目标网络
        self.target_critic1 = ImprovedXLSTMCriticNetwork(
            state_dim, action_dim, hidden_dim, seq_len, xlstm_layers
        ).to(device)

        self.target_critic2 = ImprovedXLSTMCriticNetwork(
            state_dim, action_dim, hidden_dim, seq_len, xlstm_layers
        ).to(device)

        # 复制参数到目标网络
        self.target_critic1.load_state_dict(self.critic1.state_dict())
        self.target_critic2.load_state_dict(self.critic2.state_dict())

        # 优化器
        self.actor_optimizer = torch.optim.Adam(self.actor.parameters(), lr=lr)
        self.critic1_optimizer = torch.optim.Adam(self.critic1.parameters(), lr=lr)
        self.critic2_optimizer = torch.optim.Adam(self.critic2.parameters(), lr=lr)

        # 自动调节熵权重
        self.log_alpha = torch.tensor(np.log(self.alpha), requires_grad=True, device=device)
        self.alpha_optimizer = torch.optim.Adam([self.log_alpha], lr=lr)

        # 创建增强的replay buffer
        from enhanced_replay_buffer import EnhancedSequenceReplayBuffer
        self.replay_buffer = EnhancedSequenceReplayBuffer(
            seq_len=seq_len,
            feature_dim=state_dim,
            action_dim=action_dim,
            max_size=100000
        )

        print(f"[IMPROVED_xLSTM_SAC] 初始化完成")

    def select_action(self, state, ready_mask=None, deterministic=False):
        """选择动作"""
        self.actor.eval()
        with torch.no_grad():
            if len(state.shape) == 2:
                state = state.unsqueeze(0)  # 添加batch维度
            if ready_mask is not None and len(ready_mask.shape) == 1:
                ready_mask = ready_mask.unsqueeze(0)

            if deterministic:
                # 确定性动作选择
                task_logits, global_context, encoded = self.actor.forward(state, ready_mask)

                # 选择概率最高的任务（logits已经应用了mask）
                selected_task_indices = task_logits.argmax(dim=-1)

                # 获取选定任务的嵌入
                batch_indices = torch.arange(state.shape[0], device=state.device)
                selected_task_embeddings = encoded[batch_indices, selected_task_indices]

                # 获取机器动作（使用均值）
                machine_mean, _ = self.actor.get_machine_action_for_task(
                    selected_task_embeddings, global_context
                )

                # 归一化任务索引
                task_action_normalized = (selected_task_indices.float() / max(self.seq_len - 1, 1)) * 2 - 1

                action = torch.stack([task_action_normalized, machine_mean], dim=-1)
            else:
                # 随机动作选择（使用真正的离散采样）
                action, _, _, _ = self.actor.sample(state, ready_mask)

        self.actor.train()
        return action.cpu().numpy().squeeze()

    def store_transition(self, state, action, reward, next_state, done,
                        ready_mask=None, next_ready_mask=None):
        """
        存储经验到replay buffer

        参数:
            state: 当前状态
            action: 执行的动作 [task_action, machine_action]
            reward: 获得的奖励
            next_state: 下一个状态
            done: 是否结束
            ready_mask: 当前状态的就绪掩码
            next_ready_mask: 下一状态的就绪掩码
        """
        # 从动作中提取任务索引
        task_action = action[0]
        # 将归一化的任务动作转换回索引
        selected_task_index = int(((task_action + 1) / 2) * (self.seq_len - 1))
        selected_task_index = max(0, min(selected_task_index, self.seq_len - 1))

        self.replay_buffer.add(
            state=state,
            action=action,
            next_state=next_state,
            reward=np.array([reward]),
            done=np.array([done]),
            selected_task_index=np.array([selected_task_index]),
            ready_mask=ready_mask,
            next_ready_mask=next_ready_mask
        )

    def update(self, replay_buffer=None, batch_size=256):
        """更新网络参数"""
        # 使用内置的replay buffer
        if replay_buffer is None:
            replay_buffer = self.replay_buffer

        if len(replay_buffer) < batch_size:
            return {}

        # 从增强的replay buffer采样
        batch = replay_buffer.sample(batch_size)

        state = batch['state']
        action = batch['action']
        reward = batch['reward']
        next_state = batch['next_state']
        done = batch['done']
        ready_mask = batch['ready_masks']
        next_ready_mask = batch['next_ready_masks']

        # 更新Critic
        with torch.no_grad():
            next_action, next_log_prob, _, next_selected_embeddings = \
                self.actor.sample(next_state, next_ready_mask)

            target_q1 = self.target_critic1(next_state, next_action, next_selected_embeddings)
            target_q2 = self.target_critic2(next_state, next_action, next_selected_embeddings)
            target_q = torch.min(target_q1, target_q2) - self.alpha * next_log_prob.unsqueeze(1)
            target_q = reward + (1 - done) * self.gamma * target_q

        # 获取当前状态的任务嵌入（关键修复）
        with torch.no_grad():
            _, _, encoded_states = self.actor.forward(state, ready_mask)
            # 从动作中提取任务索引
            task_actions = action[:, 0]  # [batch_size]
            task_indices = ((task_actions + 1) / 2 * (self.seq_len - 1)).long()
            task_indices = torch.clamp(task_indices, 0, self.seq_len - 1)
            batch_indices = torch.arange(state.shape[0], device=self.device)
            actual_selected_embeddings = encoded_states[batch_indices, task_indices]

        current_q1 = self.critic1(state, action, actual_selected_embeddings)
        current_q2 = self.critic2(state, action, actual_selected_embeddings)

        critic1_loss = F.mse_loss(current_q1, target_q)
        critic2_loss = F.mse_loss(current_q2, target_q)

        self.critic1_optimizer.zero_grad()
        critic1_loss.backward()
        self.critic1_optimizer.step()

        self.critic2_optimizer.zero_grad()
        critic2_loss.backward()
        self.critic2_optimizer.step()

        # 更新Actor
        new_action, log_prob, _, selected_embeddings = self.actor.sample(state, ready_mask)
        q1_new = self.critic1(state, new_action, selected_embeddings)
        q2_new = self.critic2(state, new_action, selected_embeddings)
        q_new = torch.min(q1_new, q2_new)

        actor_loss = (self.alpha * log_prob.unsqueeze(1) - q_new).mean()

        self.actor_optimizer.zero_grad()
        actor_loss.backward()
        self.actor_optimizer.step()

        # 更新熵权重
        alpha_loss = -(self.log_alpha * (log_prob + self.target_entropy).detach()).mean()

        self.alpha_optimizer.zero_grad()
        alpha_loss.backward()
        self.alpha_optimizer.step()

        self.alpha = self.log_alpha.exp()

        # 软更新目标网络
        self._soft_update(self.target_critic1, self.critic1)
        self._soft_update(self.target_critic2, self.critic2)

        return {
            'critic1_loss': critic1_loss.item(),
            'critic2_loss': critic2_loss.item(),
            'actor_loss': actor_loss.item(),
            'alpha_loss': alpha_loss.item(),
            'alpha': self.alpha.item()
        }

    def _soft_update(self, target, source):
        """软更新目标网络"""
        for target_param, param in zip(target.parameters(), source.parameters()):
            target_param.data.copy_(target_param.data * (1.0 - self.tau) + param.data * self.tau)

    def save_models(self, filepath):
        """保存模型"""
        torch.save({
            'actor': self.actor.state_dict(),
            'critic1': self.critic1.state_dict(),
            'critic2': self.critic2.state_dict(),
            'target_critic1': self.target_critic1.state_dict(),
            'target_critic2': self.target_critic2.state_dict(),
            'actor_optimizer': self.actor_optimizer.state_dict(),
            'critic1_optimizer': self.critic1_optimizer.state_dict(),
            'critic2_optimizer': self.critic2_optimizer.state_dict(),
            'log_alpha': self.log_alpha,
            'alpha_optimizer': self.alpha_optimizer.state_dict()
        }, filepath)

    def load_models(self, filepath):
        """加载模型"""
        checkpoint = torch.load(filepath, map_location=self.device)
        self.actor.load_state_dict(checkpoint['actor'])
        self.critic1.load_state_dict(checkpoint['critic1'])
        self.critic2.load_state_dict(checkpoint['critic2'])
        self.target_critic1.load_state_dict(checkpoint['target_critic1'])
        self.target_critic2.load_state_dict(checkpoint['target_critic2'])
        self.actor_optimizer.load_state_dict(checkpoint['actor_optimizer'])
        self.critic1_optimizer.load_state_dict(checkpoint['critic1_optimizer'])
        self.critic2_optimizer.load_state_dict(checkpoint['critic2_optimizer'])
        self.log_alpha = checkpoint['log_alpha']
        self.alpha_optimizer.load_state_dict(checkpoint['alpha_optimizer'])
        self.alpha = self.log_alpha.exp()
