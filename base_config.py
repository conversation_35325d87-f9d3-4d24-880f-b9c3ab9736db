"""
基础配置类，包含所有模型共享的基本配置
"""
import os
import numpy as np

class BaseConfig:
    """基础配置类，包含所有模型共享的基本配置"""

    def __init__(self):
        # 系统参数
        self.NUM_EDGE_SERVERS = 5  # 边缘服务器数量
        self.MEMORY_LIMIT = 128  # GB，边缘服务器内存限制
        self.LOCAL_MEMORY_LIMIT = 32  # GB，本地设备内存限制
        self.MAX_QUEUE_LENGTH = 10
        self.LOCAL_CPU_FREQ = 2.4  # GHz，本地CPU频率

        # 奖励权重
        self.LAMBDA_DELAY = 1.0     # 完成时间奖励权重
        self.LAMBDA_MEMORY = 2.0    # 内存使用奖励权重
        self.LAMBDA_PENALTY = 0.2   # 总完成时间惩罚权重

        # 任务参数 - 现在作为默认值，可被机器特定参数覆盖
        self.TOKEN_PER_SECOND = 0.013  # 后续生成每个的token的时间(秒) - 默认值
        self.BASE_EXECUTION_TIME = 0.23  # 生成首个token的时间（秒） - 默认值
        self.LOCAL_PENALTY = 5
        self.MAX_TOKEN_COUNT = 2000  # 最大token数，用于归一化

        # 状态维度 - 简化版本
        self.STATE_TASK_DIM = 5  # 任务特征：CPU周期、数据传输、是否LLM、token数、内存需求
        self.STATE_COMM_DIM = 1  # 通信特征：平均通信速度
        self.STATE_MEM_DIM = self.NUM_EDGE_SERVERS + 1  # 内存状态
        self.STATE_COMPUTE_DIM = self.NUM_EDGE_SERVERS + 1  # 计算能力
        self.STATE_TTFT_DIM = self.NUM_EDGE_SERVERS + 1  # 生成首个token的时间（TTFT）
        self.STATE_TPOT_DIM = self.NUM_EDGE_SERVERS + 1  # 每个token的生成时间（TPOT）

        # DAG结构信息
        self.MAX_PREDECESSORS = 3  # 每个任务最多考虑3个直接前继任务
        self.MAX_SUCCESSORS = 3  # 每个任务最多考虑3个直接后继任务
        self.STATE_DAG_DIM = self.MAX_PREDECESSORS + self.MAX_SUCCESSORS  # 前继和后继任务索引

        # 计算总状态维度 - 包含图结构特征
        self.STATE_DIM = (self.STATE_TASK_DIM + self.STATE_COMM_DIM +
                         self.STATE_MEM_DIM + self.STATE_COMPUTE_DIM +
                         self.STATE_TTFT_DIM + self.STATE_TPOT_DIM +
                         self.STATE_DAG_DIM)
        self.TASK_FEATURE_DIM = self.STATE_DIM + 1

        # 动作维度（任务索引和机器索引）
        self.ACTION_DIM = 2  # [任务索引, 机器索引]

        # 最大任务数
        self.MAX_TASKS_NUM = 30

        # 训练参数
        self.BATCH_SIZE = 64       # 批量大小
        self.NUM_EPISODES = 3000    # 训练总回合数
        self.NUM_STEPS = 200         # 每个回合的步数
        self.MAX_EPISODE_STEPS = 100    # 每个回合的最大步数
        self.REPLAY_BUFFER_SIZE = 1000000   # 经验回放缓冲区大小
        self.BUFFER_SIZE = 1000000          # 同上，为兼容性保留
        self.LEARNING_RATE = 1e-3    # 学习率
        self.GAMMA = 0.99            # 折扣因子
        self.TAU = 0.005             # 目标网络更新系数
        self.TARGET_ENTROPY = -self.MAX_TASKS_NUM  # 目标熵值，设为最大任务数的负值
        self.AUTO_ALPHA = False      # 是否自动调整熵参数
        self.ALPHA = 0.5             # 熵权重
        self.TD_BOUND = 20           # TD误差边界
        self.BOUND = True            # 是否使用边界
        self.DELAY_UPDATE = 2        # 延迟更新步数

        # 评估参数
        self.EVAL_EPISODES = 10      # 每次评估的回合数

        # 其他参数
        self.USE_SEQUENCE = "True"   # 是否使用序列化状态
        self.CUDA = "True"           # 是否使用GPU加速

        # 文件路径
        self._update_file_paths()

    def _update_file_paths(self):
        """更新文件路径"""
        # base_path = f'dataset/training/DAG_{self.MAX_TASKS_NUM}_edges_{self.NUM_EDGE_SERVERS}_mem_{self.MEMORY_LIMIT}GB_density_0.4_0.6'
        base_path = f'dataset/training/DAG_{self.MAX_TASKS_NUM}_edges_{self.NUM_EDGE_SERVERS}_mem_25-38GB_density_0.4_0.6'
        # base_path = f'dataset/training/DAG_200_edges_{self.NUM_EDGE_SERVERS}_mem_32GB_density_0.4_0.6'
        self.MACHINE_COMMU_SPEED_PATH = f'./{base_path}/machine_commu_speed1.xlsx'
        self.MACHINES_RESOURCE_PATH = f'./{base_path}/machines_resource1.xlsx'
        self.TRAINING_DATA_PATH = base_path

    def update_edge_servers(self, num_edge_servers, task_num=None, memory_limit=None):
        """
        更新边缘服务器数量和相关参数

        参数:
            num_edge_servers: 新的边缘服务器数量
            task_num: 最大任务数，如果为None则不更新
            memory_limit: 内存限制(GB)，如果为None则不更新
        """
        self.NUM_EDGE_SERVERS = num_edge_servers

        # 更新依赖于NUM_EDGE_SERVERS的参数
        self.STATE_MEM_DIM = self.NUM_EDGE_SERVERS + 1
        self.STATE_COMPUTE_DIM = self.NUM_EDGE_SERVERS + 1
        self.STATE_TTFT_DIM = self.NUM_EDGE_SERVERS + 1
        self.STATE_TPOT_DIM = self.NUM_EDGE_SERVERS + 1

        # 确保DAG结构信息正确
        self.STATE_DAG_DIM = self.MAX_PREDECESSORS + self.MAX_SUCCESSORS

        # 更新总状态维度
        self.STATE_DIM = (self.STATE_TASK_DIM + self.STATE_COMM_DIM +
                         self.STATE_MEM_DIM + self.STATE_COMPUTE_DIM +
                         self.STATE_TTFT_DIM + self.STATE_TPOT_DIM +
                         self.STATE_DAG_DIM)

        # 计算任务特征维度
        # 状态维度 = 任务特征(5) + 通信特征(1) + 内存状态(NUM_EDGE_SERVERS+1) +
        #           计算能力(NUM_EDGE_SERVERS+1) + TTFT(NUM_EDGE_SERVERS+1) +
        #           TPOT(NUM_EDGE_SERVERS+1) + 前继任务(MAX_PREDECESSORS) +
        #           后继任务(MAX_SUCCESSORS) + 掩码(1)
        self.TASK_FEATURE_DIM = (5 + 1 +
                                (self.NUM_EDGE_SERVERS + 1) +
                                (self.NUM_EDGE_SERVERS + 1) +
                                (self.NUM_EDGE_SERVERS + 1) +  # TTFT
                                (self.NUM_EDGE_SERVERS + 1) +  # TPOT
                                self.MAX_PREDECESSORS +
                                self.MAX_SUCCESSORS +
                                1)

        print(f"[CONFIG] 更新状态维度: STATE_DIM={self.STATE_DIM}, TASK_FEATURE_DIM={self.TASK_FEATURE_DIM}")

        # 更新最大任务数
        if task_num is not None:
            self.MAX_TASKS_NUM = task_num
            self.TARGET_ENTROPY = -self.MAX_TASKS_NUM  # 更新目标熵值

        # 更新内存限制
        if memory_limit is not None:
            self.MEMORY_LIMIT = memory_limit

        # 更新文件路径
        self._update_file_paths()

        # 检查路径是否存在
        if not os.path.exists(self.TRAINING_DATA_PATH):
            print(f"警告: 路径 {self.TRAINING_DATA_PATH} 不存在，使用默认路径")

    def update_from_args(self, args):
        """
        从命令行参数更新配置

        参数:
            args: 命令行参数
        """
        # 更新边缘设备数量、任务数量和内存限制
        update_needed = False

        if hasattr(args, 'edge_num') and args.edge_num != self.NUM_EDGE_SERVERS:
            print(f"更新边缘服务器数量: {self.NUM_EDGE_SERVERS} -> {args.edge_num}")
            update_needed = True

        if hasattr(args, 'task_num') and args.task_num != self.MAX_TASKS_NUM:
            print(f"更新最大任务数: {self.MAX_TASKS_NUM} -> {args.task_num}")
            update_needed = True

        if hasattr(args, 'memory_limit') and args.memory_limit != self.MEMORY_LIMIT:
            print(f"更新内存限制: {self.MEMORY_LIMIT}GB -> {args.memory_limit}GB")
            update_needed = True

        if update_needed:
            self.update_edge_servers(
                num_edge_servers=args.edge_num if hasattr(args, 'edge_num') else self.NUM_EDGE_SERVERS,
                task_num=args.task_num if hasattr(args, 'task_num') else None,
                memory_limit=args.memory_limit if hasattr(args, 'memory_limit') else None
            )

        # 更新其他参数
        if hasattr(args, 'batch_size'):
            self.BATCH_SIZE = args.batch_size

        if hasattr(args, 'learning_rate'):
            self.LEARNING_RATE = args.learning_rate

        if hasattr(args, 'epochs'):
            self.NUM_EPISODES = args.epochs

        if hasattr(args, 'buffer_size'):
            self.REPLAY_BUFFER_SIZE = args.buffer_size
            self.BUFFER_SIZE = args.buffer_size

        if hasattr(args, 'cuda'):
            self.CUDA = args.cuda

        if hasattr(args, 'use_sequence'):
            self.USE_SEQUENCE = args.use_sequence

    def get_model_save_dir(self, model_name, timestamp=None, experiment_name=None):
        """
        获取模型保存目录

        参数:
            model_name: 模型名称
            timestamp: 时间戳，如果为None则使用当前时间
            experiment_name: 实验名称，如果为None则使用默认名称

        返回:
            保存目录路径
        """
        import datetime

        # 使用当前时间作为时间戳
        if timestamp is None:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

        # 使用默认实验名称
        if experiment_name is None:
            experiment_name = f"edges_{self.NUM_EDGE_SERVERS}_tasks_{self.MAX_TASKS_NUM}_mem_{self.MEMORY_LIMIT}"

        # 构建保存目录
        save_dir = f"models/{model_name}_{timestamp}_{experiment_name}"

        return save_dir
