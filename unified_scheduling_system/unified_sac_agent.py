#!/usr/bin/env python3
"""
统一SAC智能体
支持MLP、LSTM、xLSTM三种网络架构和双动作/单动作模式
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
from collections import deque
import random
from typing import Tuple, Dict, Any, Optional, Union

from unified_networks import create_networks

class ReplayBuffer:
    """经验回放缓冲区"""

    def __init__(self, capacity: int):
        self.buffer = deque(maxlen=capacity)

    def push(self, state, action, reward, next_state, done):
        """添加经验"""
        self.buffer.append((state, action, reward, next_state, done))

    def sample(self, batch_size: int):
        """采样经验"""
        batch = random.sample(self.buffer, batch_size)

        states, actions, rewards, next_states, dones = zip(*batch)

        return states, actions, rewards, next_states, dones

    def __len__(self):
        return len(self.buffer)

class UnifiedSACAgent:
    """统一SAC智能体"""

    def __init__(self, config, device='cuda'):
        self.config = config
        self.device = device

        # 创建网络
        self.actor, self.critic1, self.critic2 = create_networks(config)
        self.target_critic1, self.target_critic2 = create_networks(config)[1:]

        # 移动到设备
        self.actor.to(device)
        self.critic1.to(device)
        self.critic2.to(device)
        self.target_critic1.to(device)
        self.target_critic2.to(device)

        # 初始化目标网络
        self._hard_update(self.target_critic1, self.critic1)
        self._hard_update(self.target_critic2, self.critic2)

        # 优化器
        self.actor_optimizer = optim.Adam(self.actor.parameters(), lr=config.LEARNING_RATE)
        self.critic1_optimizer = optim.Adam(self.critic1.parameters(), lr=config.LEARNING_RATE)
        self.critic2_optimizer = optim.Adam(self.critic2.parameters(), lr=config.LEARNING_RATE)

        # SAC参数
        self.gamma = config.GAMMA
        self.tau = config.TAU
        self.alpha = 0.2  # 熵正则化系数

        # 经验回放
        self.replay_buffer = ReplayBuffer(config.REPLAY_BUFFER_SIZE)
        self.batch_size = config.BATCH_SIZE

        # 训练统计
        self.update_count = 0
        self.actor_loss_history = deque(maxlen=1000)
        self.critic_loss_history = deque(maxlen=1000)

        print(f"[UNIFIED_SAC] 智能体初始化完成")
        print(f"  设备: {device}")
        print(f"  网络类型: {config.NETWORK_TYPE}")
        print(f"  动作模式: {config.ACTION_MODE}")
        print(f"  学习率: {config.LEARNING_RATE}")
        print(f"  批大小: {config.BATCH_SIZE}")

    def _hard_update(self, target, source):
        """硬更新目标网络"""
        for target_param, param in zip(target.parameters(), source.parameters()):
            target_param.data.copy_(param.data)

    def _soft_update(self, target, source):
        """软更新目标网络"""
        for target_param, param in zip(target.parameters(), source.parameters()):
            target_param.data.copy_(target_param.data * (1.0 - self.tau) + param.data * self.tau)

    def _prepare_state_input(self, state):
        """准备状态输入"""
        if self.config.NETWORK_TYPE == "mlp":
            # MLP模式：使用序列状态的第一个任务特征作为全局状态
            if isinstance(state, np.ndarray) and len(state.shape) == 2:
                # 如果是序列状态 [seq_len, feature_dim]，取第一个任务的特征
                global_state = state[0, :]  # 取第一个任务的特征作为全局状态
                return torch.FloatTensor(global_state).to(self.device)
            else:
                return torch.FloatTensor(state).to(self.device)
        else:
            # 序列模式：直接使用序列状态
            if isinstance(state, np.ndarray) and len(state.shape) == 2:
                # 序列状态 [seq_len, feature_dim]
                task_sequence_tensor = torch.FloatTensor(state).to(self.device)
                # 创建全局状态（使用平均值）
                global_state = np.mean(state, axis=0)  # 对序列取平均作为全局状态
                global_state_tensor = torch.FloatTensor(global_state).to(self.device)
                return (global_state_tensor, task_sequence_tensor)
            else:
                # 如果是一维状态，创建序列
                global_state_tensor = torch.FloatTensor(state).to(self.device)
                empty_sequence = torch.zeros(self.config.SEQ_LEN, len(state)).to(self.device)
                empty_sequence[0, :] = global_state_tensor  # 第一个位置填入状态
                return (global_state_tensor, empty_sequence)

    def select_action(self, state, deterministic=False):
        """选择动作"""
        self.actor.eval()

        with torch.no_grad():
            # 准备输入
            if isinstance(state, tuple):
                state_input = self._prepare_state_input(state)
                if self.config.NETWORK_TYPE != "mlp":
                    # 添加batch维度
                    global_state, task_sequence = state_input
                    state_input = (global_state.unsqueeze(0), task_sequence.unsqueeze(0))
                else:
                    state_input = state_input.unsqueeze(0)
            else:
                state_input = self._prepare_state_input(state).unsqueeze(0)

            if deterministic:
                # 确定性动作选择
                if self.config.ACTION_MODE == "dual":
                    task_logits, machine_logits = self.actor(state_input)
                    task_action = torch.argmax(task_logits, dim=-1)
                    machine_action = torch.argmax(machine_logits, dim=-1)
                    return task_action.cpu().numpy()[0], machine_action.cpu().numpy()[0]
                else:
                    machine_logits = self.actor(state_input)
                    machine_action = torch.argmax(machine_logits, dim=-1)
                    return machine_action.cpu().numpy()[0]
            else:
                # 随机动作选择
                if self.config.ACTION_MODE == "dual":
                    (task_action, machine_action), _ = self.actor.sample_action(state_input)
                    return task_action.cpu().numpy()[0], machine_action.cpu().numpy()[0]
                else:
                    machine_action, _ = self.actor.sample_action(state_input)
                    return machine_action.cpu().numpy()[0]

        self.actor.train()

    def store_transition(self, state, action, reward, next_state, done):
        """存储转换"""
        self.replay_buffer.push(state, action, reward, next_state, done)

    def update(self):
        """更新网络"""
        if len(self.replay_buffer) < self.batch_size:
            return

        # 采样批次
        states, actions, rewards, next_states, dones = self.replay_buffer.sample(self.batch_size)

        # 转换为张量
        rewards = torch.FloatTensor(rewards).to(self.device)
        dones = torch.BoolTensor(dones).to(self.device)

        # 准备状态输入
        state_inputs = []
        next_state_inputs = []

        for i in range(self.batch_size):
            state_input = self._prepare_state_input(states[i])
            next_state_input = self._prepare_state_input(next_states[i])

            state_inputs.append(state_input)
            next_state_inputs.append(next_state_input)

        # 批处理状态
        if self.config.NETWORK_TYPE == "mlp":
            batch_states = torch.stack(state_inputs)
            batch_next_states = torch.stack(next_state_inputs)
        else:
            batch_global_states = torch.stack([s[0] for s in state_inputs])
            batch_task_sequences = torch.stack([s[1] for s in state_inputs])
            batch_next_global_states = torch.stack([s[0] for s in next_state_inputs])
            batch_next_task_sequences = torch.stack([s[1] for s in next_state_inputs])

            batch_states = (batch_global_states, batch_task_sequences)
            batch_next_states = (batch_next_global_states, batch_next_task_sequences)

        # 处理动作
        if self.config.ACTION_MODE == "dual":
            task_actions = torch.LongTensor([a[0] for a in actions]).to(self.device)
            machine_actions = torch.LongTensor([a[1] for a in actions]).to(self.device)
            batch_actions = (task_actions, machine_actions)
        else:
            batch_actions = torch.LongTensor(actions).to(self.device)

        # 更新Critic
        critic_loss = self._update_critics(batch_states, batch_actions, rewards, batch_next_states, dones)

        # 更新Actor
        actor_loss = self._update_actor(batch_states)

        # 软更新目标网络
        self._soft_update(self.target_critic1, self.critic1)
        self._soft_update(self.target_critic2, self.critic2)

        # 记录损失
        self.actor_loss_history.append(actor_loss)
        self.critic_loss_history.append(critic_loss)
        self.update_count += 1

    def _update_critics(self, states, actions, rewards, next_states, dones):
        """更新Critic网络"""
        with torch.no_grad():
            # 计算目标Q值
            if self.config.ACTION_MODE == "dual":
                (next_task_actions, next_machine_actions), (next_task_log_probs, next_machine_log_probs) = self.actor.sample_action(next_states)
                next_actions = (next_task_actions, next_machine_actions)
                next_log_probs = next_task_log_probs + next_machine_log_probs
            else:
                next_actions, next_log_probs = self.actor.sample_action(next_states)

            target_q1 = self.target_critic1(next_states, next_actions)
            target_q2 = self.target_critic2(next_states, next_actions)
            target_q = torch.min(target_q1, target_q2) - self.alpha * next_log_probs.unsqueeze(-1)

            target_q = rewards.unsqueeze(-1) + (1 - dones.unsqueeze(-1).float()) * self.gamma * target_q

        # 当前Q值
        current_q1 = self.critic1(states, actions)
        current_q2 = self.critic2(states, actions)

        # Critic损失
        critic1_loss = F.mse_loss(current_q1, target_q)
        critic2_loss = F.mse_loss(current_q2, target_q)

        # 更新Critic1
        self.critic1_optimizer.zero_grad()
        critic1_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.critic1.parameters(), 1.0)
        self.critic1_optimizer.step()

        # 更新Critic2
        self.critic2_optimizer.zero_grad()
        critic2_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.critic2.parameters(), 1.0)
        self.critic2_optimizer.step()

        return (critic1_loss + critic2_loss).item() / 2

    def _update_actor(self, states):
        """更新Actor网络"""
        # 采样动作
        if self.config.ACTION_MODE == "dual":
            (task_actions, machine_actions), (task_log_probs, machine_log_probs) = self.actor.sample_action(states)
            actions = (task_actions, machine_actions)
            log_probs = task_log_probs + machine_log_probs
        else:
            actions, log_probs = self.actor.sample_action(states)

        # 计算Q值
        q1 = self.critic1(states, actions)
        q2 = self.critic2(states, actions)
        q = torch.min(q1, q2)

        # Actor损失
        actor_loss = (self.alpha * log_probs.unsqueeze(-1) - q).mean()

        # 更新Actor
        self.actor_optimizer.zero_grad()
        actor_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.actor.parameters(), 1.0)
        self.actor_optimizer.step()

        return actor_loss.item()

    def save_models(self, filepath):
        """保存模型"""
        torch.save({
            'actor_state_dict': self.actor.state_dict(),
            'critic1_state_dict': self.critic1.state_dict(),
            'critic2_state_dict': self.critic2.state_dict(),
            'target_critic1_state_dict': self.target_critic1.state_dict(),
            'target_critic2_state_dict': self.target_critic2.state_dict(),
            'actor_optimizer_state_dict': self.actor_optimizer.state_dict(),
            'critic1_optimizer_state_dict': self.critic1_optimizer.state_dict(),
            'critic2_optimizer_state_dict': self.critic2_optimizer.state_dict(),
            'config': self.config,
            'update_count': self.update_count
        }, filepath)

    def load_models(self, filepath):
        """加载模型"""
        checkpoint = torch.load(filepath, map_location=self.device)

        self.actor.load_state_dict(checkpoint['actor_state_dict'])
        self.critic1.load_state_dict(checkpoint['critic1_state_dict'])
        self.critic2.load_state_dict(checkpoint['critic2_state_dict'])
        self.target_critic1.load_state_dict(checkpoint['target_critic1_state_dict'])
        self.target_critic2.load_state_dict(checkpoint['target_critic2_state_dict'])

        self.actor_optimizer.load_state_dict(checkpoint['actor_optimizer_state_dict'])
        self.critic1_optimizer.load_state_dict(checkpoint['critic1_optimizer_state_dict'])
        self.critic2_optimizer.load_state_dict(checkpoint['critic2_optimizer_state_dict'])

        self.update_count = checkpoint.get('update_count', 0)

    def get_training_stats(self):
        """获取训练统计"""
        return {
            'update_count': self.update_count,
            'avg_actor_loss': np.mean(self.actor_loss_history) if self.actor_loss_history else 0,
            'avg_critic_loss': np.mean(self.critic_loss_history) if self.critic_loss_history else 0,
            'replay_buffer_size': len(self.replay_buffer)
        }
