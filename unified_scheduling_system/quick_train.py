#!/usr/bin/env python3
"""
快速训练脚本
提供简单的命令行接口来启动不同配置的训练
"""

import argparse
import subprocess
import sys

def main():
    parser = argparse.ArgumentParser(description='统一调度系统快速训练')
    
    # 预定义配置
    parser.add_argument('--config', type=str, required=True,
                       choices=[
                           'mlp_single_2dev',
                           'mlp_single_3dev', 
                           'mlp_single_4dev',
                           'mlp_single_5dev',
                           'mlp_single_6dev',
                           'mlp_dual_3dev',
                           'mlp_dual_5dev',
                           'lstm_single_3dev',
                           'lstm_single_5dev',
                           'xlstm_single_3dev',
                           'xlstm_single_5dev',
                           'xlstm_dual_5dev'
                       ],
                       help='预定义的训练配置')
    
    parser.add_argument('--episodes', type=int, default=None, help='训练回合数（覆盖默认值）')
    parser.add_argument('--data_density', type=str, default='0.5', help='数据密度')
    
    args = parser.parse_args()
    
    # 配置映射
    config_map = {
        'mlp_single_2dev': {
            'network_type': 'mlp',
            'action_mode': 'single', 
            'device_num': 2,
            'episodes': 500,
            'save_interval': 125
        },
        'mlp_single_3dev': {
            'network_type': 'mlp',
            'action_mode': 'single',
            'device_num': 3, 
            'episodes': 600,
            'save_interval': 150
        },
        'mlp_single_4dev': {
            'network_type': 'mlp',
            'action_mode': 'single',
            'device_num': 4,
            'episodes': 700,
            'save_interval': 175
        },
        'mlp_single_5dev': {
            'network_type': 'mlp',
            'action_mode': 'single',
            'device_num': 5,
            'episodes': 800,
            'save_interval': 200
        },
        'mlp_single_6dev': {
            'network_type': 'mlp',
            'action_mode': 'single',
            'device_num': 6,
            'episodes': 900,
            'save_interval': 225
        },
        'mlp_dual_3dev': {
            'network_type': 'mlp',
            'action_mode': 'dual',
            'device_num': 3,
            'episodes': 800,
            'save_interval': 200
        },
        'mlp_dual_5dev': {
            'network_type': 'mlp',
            'action_mode': 'dual',
            'device_num': 5,
            'episodes': 1000,
            'save_interval': 250
        },
        'lstm_single_3dev': {
            'network_type': 'lstm',
            'action_mode': 'single',
            'device_num': 3,
            'episodes': 1000,
            'save_interval': 250
        },
        'lstm_single_5dev': {
            'network_type': 'lstm',
            'action_mode': 'single',
            'device_num': 5,
            'episodes': 1200,
            'save_interval': 300
        },
        'xlstm_single_3dev': {
            'network_type': 'xlstm',
            'action_mode': 'single',
            'device_num': 3,
            'episodes': 1200,
            'save_interval': 300
        },
        'xlstm_single_5dev': {
            'network_type': 'xlstm',
            'action_mode': 'single',
            'device_num': 5,
            'episodes': 1500,
            'save_interval': 375
        },
        'xlstm_dual_5dev': {
            'network_type': 'xlstm',
            'action_mode': 'dual',
            'device_num': 5,
            'episodes': 1800,
            'save_interval': 450
        }
    }
    
    # 获取配置
    config = config_map[args.config]
    
    # 覆盖回合数
    if args.episodes:
        config['episodes'] = args.episodes
        config['save_interval'] = args.episodes // 4
    
    # 构建数据路径
    data_path = f"../enhanced_llm_scheduling/dataset/training/DAG_30_layered_fat0.4_density{args.data_density}_{config['device_num']}devices"
    
    # 构建训练命令
    cmd = [
        'python3', 'train_unified.py',
        '--episodes', str(config['episodes']),
        '--network_type', config['network_type'],
        '--action_mode', config['action_mode'],
        '--device_num', str(config['device_num']),
        '--data_path', data_path,
        '--save_interval', str(config['save_interval'])
    ]
    
    print(f"🚀 启动训练配置: {args.config}")
    print(f"📋 训练参数:")
    print(f"  网络类型: {config['network_type']}")
    print(f"  动作模式: {config['action_mode']}")
    print(f"  设备数量: {config['device_num']}")
    print(f"  训练回合: {config['episodes']}")
    print(f"  数据路径: {data_path}")
    print(f"  保存间隔: {config['save_interval']}")
    print()
    print(f"🔧 执行命令: {' '.join(cmd)}")
    print("=" * 80)
    
    # 执行训练
    try:
        subprocess.run(cmd, check=True)
        print(f"\n✅ 训练配置 {args.config} 完成!")
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 训练配置 {args.config} 失败: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print(f"\n⏹️  训练配置 {args.config} 被用户中断")
        sys.exit(1)

if __name__ == "__main__":
    main()
