#!/usr/bin/env python3
"""
批量训练脚本
支持多种设备配置和网络架构的自动化训练
"""

import os
import sys
import subprocess
import time
from datetime import datetime

def run_training(network_type, action_mode, device_num, episodes=500, data_density="0.5"):
    """运行单个训练配置"""
    
    # 构建数据路径
    data_path = f"../enhanced_llm_scheduling/dataset/training/DAG_30_layered_fat0.4_density{data_density}_{device_num}devices"
    
    # 检查数据集是否存在
    if not os.path.exists(data_path):
        print(f"❌ 数据集不存在: {data_path}")
        return False
    
    # 构建训练命令
    cmd = [
        "python3", "train_unified.py",
        "--episodes", str(episodes),
        "--network_type", network_type,
        "--action_mode", action_mode,
        "--device_num", str(device_num),
        "--data_path", data_path,
        "--save_interval", str(episodes // 4)  # 保存4个检查点
    ]
    
    print(f"\n🚀 开始训练: {network_type.upper()}-{action_mode.upper()}-{device_num}设备")
    print(f"📁 数据路径: {data_path}")
    print(f"⏱️  训练回合: {episodes}")
    print("=" * 60)
    
    start_time = time.time()
    
    try:
        # 运行训练
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=3600)  # 1小时超时
        
        training_time = time.time() - start_time
        
        if result.returncode == 0:
            print(f"✅ 训练成功完成! 用时: {training_time:.1f}秒")
            
            # 提取关键结果
            output_lines = result.stdout.split('\n')
            for line in output_lines:
                if "成功率:" in line or "平均Makespan:" in line or "模型保存路径:" in line:
                    print(f"📊 {line.strip()}")
            
            return True
        else:
            print(f"❌ 训练失败!")
            print(f"错误输出: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ 训练超时 (1小时)")
        return False
    except Exception as e:
        print(f"❌ 训练出错: {e}")
        return False

def main():
    """主函数"""
    print("🎯 统一调度系统批量训练")
    print("=" * 80)
    
    # 训练配置
    configurations = [
        # 网络类型, 动作模式, 设备数, 训练回合数
        ("mlp", "single", 2, 300),
        ("mlp", "single", 3, 300), 
        ("mlp", "single", 4, 300),
        ("mlp", "single", 5, 300),
        ("mlp", "single", 6, 300),
        
        ("mlp", "dual", 2, 300),
        ("mlp", "dual", 3, 300),
        ("mlp", "dual", 4, 300),
        ("mlp", "dual", 5, 300),
        ("mlp", "dual", 6, 300),
        
        ("lstm", "single", 3, 400),
        ("lstm", "single", 5, 400),
        
        ("xlstm", "single", 3, 400),
        ("xlstm", "single", 5, 400),
    ]
    
    # 记录结果
    results = []
    total_start_time = time.time()
    
    print(f"📋 计划训练 {len(configurations)} 个配置")
    print(f"🕐 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    for i, (network_type, action_mode, device_num, episodes) in enumerate(configurations):
        print(f"\n{'='*80}")
        print(f"📈 进度: {i+1}/{len(configurations)}")
        
        success = run_training(network_type, action_mode, device_num, episodes)
        
        results.append({
            'config': f"{network_type}-{action_mode}-{device_num}devices",
            'success': success,
            'episodes': episodes
        })
        
        # 短暂休息
        if i < len(configurations) - 1:
            print("😴 休息5秒...")
            time.sleep(5)
    
    # 总结结果
    total_time = time.time() - total_start_time
    successful_configs = sum(1 for r in results if r['success'])
    
    print(f"\n{'='*80}")
    print("🎉 批量训练完成!")
    print(f"⏱️  总用时: {total_time/3600:.1f}小时")
    print(f"✅ 成功配置: {successful_configs}/{len(configurations)}")
    print(f"🕐 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print(f"\n📊 详细结果:")
    for result in results:
        status = "✅" if result['success'] else "❌"
        print(f"  {status} {result['config']} ({result['episodes']} episodes)")
    
    # 保存结果摘要
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    summary_file = f"batch_training_summary_{timestamp}.txt"
    
    with open(summary_file, 'w') as f:
        f.write(f"批量训练结果摘要\n")
        f.write(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"总用时: {total_time/3600:.1f}小时\n")
        f.write(f"成功率: {successful_configs}/{len(configurations)}\n\n")
        
        f.write("详细结果:\n")
        for result in results:
            status = "SUCCESS" if result['success'] else "FAILED"
            f.write(f"{result['config']}: {status} ({result['episodes']} episodes)\n")
    
    print(f"\n💾 结果摘要已保存: {summary_file}")

if __name__ == "__main__":
    main()
