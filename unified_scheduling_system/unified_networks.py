#!/usr/bin/env python3
"""
统一网络架构
支持MLP、LSTM、xLSTM三种网络类型
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Tuple, Optional

class MLPEncoder(nn.Module):
    """MLP编码器"""

    def __init__(self, input_dim: int, hidden_dim: int = 256, num_layers: int = 3):
        super(MLPEncoder, self).__init__()

        layers = []
        current_dim = input_dim

        for i in range(num_layers):
            layers.append(nn.Linear(current_dim, hidden_dim))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(0.1))
            current_dim = hidden_dim

        self.network = nn.Sequential(*layers)
        self.output_dim = hidden_dim

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.network(x)

class LSTMEncoder(nn.Module):
    """LSTM编码器"""

    def __init__(self, input_dim: int, hidden_dim: int = 128, num_layers: int = 2):
        super(LSTMEncoder, self).__init__()

        self.hidden_dim = hidden_dim
        self.num_layers = num_layers

        self.lstm = nn.LSTM(
            input_size=input_dim,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            batch_first=True,
            dropout=0.1 if num_layers > 1 else 0
        )

        self.output_dim = hidden_dim

    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        batch_size = x.size(0)

        # 初始化隐藏状态
        h0 = torch.zeros(self.num_layers, batch_size, self.hidden_dim).to(x.device)
        c0 = torch.zeros(self.num_layers, batch_size, self.hidden_dim).to(x.device)

        # LSTM前向传播
        output, (hn, cn) = self.lstm(x, (h0, c0))

        if mask is not None:
            # 应用mask，只保留有效序列的输出
            mask = mask.unsqueeze(-1).expand_as(output)
            output = output * mask

            # 计算每个序列的有效长度
            seq_lengths = mask.sum(dim=1).squeeze(-1)

            # 获取每个序列最后一个有效位置的输出
            batch_indices = torch.arange(batch_size).to(x.device)
            last_indices = (seq_lengths - 1).long().clamp(min=0)
            last_output = output[batch_indices, last_indices]
        else:
            # 使用最后一个时间步的输出
            last_output = output[:, -1, :]

        return last_output

class xLSTMBlock(nn.Module):
    """简化的xLSTM块"""

    def __init__(self, input_dim: int, hidden_dim: int):
        super(xLSTMBlock, self).__init__()

        self.input_dim = input_dim
        self.hidden_dim = hidden_dim

        # 输入门、遗忘门、输出门、候选值
        self.W_i = nn.Linear(input_dim, hidden_dim)
        self.U_i = nn.Linear(hidden_dim, hidden_dim)
        self.W_f = nn.Linear(input_dim, hidden_dim)
        self.U_f = nn.Linear(hidden_dim, hidden_dim)
        self.W_o = nn.Linear(input_dim, hidden_dim)
        self.U_o = nn.Linear(hidden_dim, hidden_dim)
        self.W_c = nn.Linear(input_dim, hidden_dim)
        self.U_c = nn.Linear(hidden_dim, hidden_dim)

        # xLSTM的扩展：指数门控
        self.W_exp = nn.Linear(input_dim, hidden_dim)
        self.U_exp = nn.Linear(hidden_dim, hidden_dim)

        # 层归一化
        self.ln_c = nn.LayerNorm(hidden_dim)
        self.ln_h = nn.LayerNorm(hidden_dim)

    def forward(self, x: torch.Tensor, h_prev: torch.Tensor, c_prev: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        # 标准LSTM门
        i_t = torch.sigmoid(self.W_i(x) + self.U_i(h_prev))
        f_t = torch.sigmoid(self.W_f(x) + self.U_f(h_prev))
        o_t = torch.sigmoid(self.W_o(x) + self.U_o(h_prev))
        c_tilde = torch.tanh(self.W_c(x) + self.U_c(h_prev))

        # xLSTM的指数门控
        exp_gate = torch.exp(self.W_exp(x) + self.U_exp(h_prev))

        # 更新细胞状态（加入指数门控）
        c_t = f_t * c_prev + i_t * c_tilde * exp_gate
        c_t = self.ln_c(c_t)

        # 更新隐藏状态
        h_t = o_t * torch.tanh(c_t)
        h_t = self.ln_h(h_t)

        return h_t, c_t

class xLSTMEncoder(nn.Module):
    """xLSTM编码器"""

    def __init__(self, input_dim: int, hidden_dim: int = 128, num_layers: int = 2):
        super(xLSTMEncoder, self).__init__()

        self.hidden_dim = hidden_dim
        self.num_layers = num_layers

        # 构建xLSTM层
        self.xlstm_layers = nn.ModuleList()
        for i in range(num_layers):
            layer_input_dim = input_dim if i == 0 else hidden_dim
            self.xlstm_layers.append(xLSTMBlock(layer_input_dim, hidden_dim))

        self.output_dim = hidden_dim

    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        batch_size, seq_len, _ = x.size()

        # 初始化隐藏状态和细胞状态
        h = [torch.zeros(batch_size, self.hidden_dim).to(x.device) for _ in range(self.num_layers)]
        c = [torch.zeros(batch_size, self.hidden_dim).to(x.device) for _ in range(self.num_layers)]

        # 逐时间步处理
        outputs = []
        for t in range(seq_len):
            x_t = x[:, t, :]

            # 通过所有xLSTM层
            for layer_idx, xlstm_layer in enumerate(self.xlstm_layers):
                if layer_idx == 0:
                    h[layer_idx], c[layer_idx] = xlstm_layer(x_t, h[layer_idx], c[layer_idx])
                else:
                    h[layer_idx], c[layer_idx] = xlstm_layer(h[layer_idx-1], h[layer_idx], c[layer_idx])

            outputs.append(h[-1])

        # 堆叠输出
        output = torch.stack(outputs, dim=1)  # [batch_size, seq_len, hidden_dim]

        if mask is not None:
            # 应用mask
            mask = mask.unsqueeze(-1).expand_as(output)
            output = output * mask

            # 获取每个序列最后一个有效位置的输出
            seq_lengths = mask.sum(dim=1).squeeze(-1)
            batch_indices = torch.arange(batch_size).to(x.device)
            last_indices = (seq_lengths - 1).long().clamp(min=0)
            last_output = output[batch_indices, last_indices]
        else:
            last_output = output[:, -1, :]

        return last_output

class UnifiedActor(nn.Module):
    """统一Actor网络"""

    def __init__(self, config):
        super(UnifiedActor, self).__init__()

        self.config = config
        self.network_type = config.NETWORK_TYPE
        self.action_mode = config.ACTION_MODE

        # 选择编码器
        if self.network_type == "mlp":
            # MLP模式：直接处理全局状态
            self.encoder = MLPEncoder(config.ENHANCED_STATE_DIM, config.HIDDEN_DIM)
            self.use_sequence = False
        elif self.network_type == "lstm":
            # LSTM模式：处理任务序列
            self.encoder = LSTMEncoder(config.TASK_FEATURE_DIM_WITH_MASK, config.LSTM_HIDDEN_DIM)
            self.global_processor = MLPEncoder(config.ENHANCED_STATE_DIM, config.HIDDEN_DIM // 2)
            self.use_sequence = True
        elif self.network_type == "xlstm":
            # xLSTM模式：处理任务序列
            self.encoder = xLSTMEncoder(config.TASK_FEATURE_DIM_WITH_MASK, config.LSTM_HIDDEN_DIM, config.XLSTM_LAYERS)
            self.global_processor = MLPEncoder(config.ENHANCED_STATE_DIM, config.HIDDEN_DIM // 2)
            self.use_sequence = True
        else:
            raise ValueError(f"不支持的网络类型: {self.network_type}")

        # 计算特征维度
        if self.use_sequence:
            feature_dim = self.encoder.output_dim + self.global_processor.output_dim
        else:
            feature_dim = self.encoder.output_dim

        # 动作头
        if self.action_mode == "dual":
            # 双动作：任务选择 + 机器选择
            self.task_head = nn.Linear(feature_dim, config.TASK_ACTION_DIM)
            self.machine_head = nn.Linear(feature_dim, config.MACHINE_ACTION_DIM)
        else:
            # 单动作：只有机器选择
            self.machine_head = nn.Linear(feature_dim, config.MACHINE_ACTION_DIM)

        self._init_weights()

    def _init_weights(self):
        """初始化权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight, gain=0.1)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0.0)

    def forward(self, state_input, ready_mask=None) -> torch.Tensor:
        """前向传播"""
        if self.use_sequence:
            # 序列模式：分别处理全局状态和任务序列
            global_state, task_sequence = state_input

            # 处理全局状态
            global_features = self.global_processor(global_state)

            # 处理任务序列
            task_mask = task_sequence[:, :, -1]  # 最后一维是mask
            sequence_features = self.encoder(task_sequence, task_mask)

            # 合并特征
            combined_features = torch.cat([global_features, sequence_features], dim=-1)
        else:
            # MLP模式：只处理全局状态
            global_state = state_input
            combined_features = self.encoder(global_state)

        # 生成动作logits
        if self.action_mode == "dual":
            task_logits = self.task_head(combined_features)
            machine_logits = self.machine_head(combined_features)

            # 应用ready_mask到任务选择
            if ready_mask is not None:
                # 将非就绪任务的logits设为极小值
                mask_tensor = torch.FloatTensor(ready_mask).to(task_logits.device)
                task_logits = task_logits + (mask_tensor - 1) * 1e9  # 非就绪任务logits -> -1e9

            return task_logits, machine_logits
        else:
            machine_logits = self.machine_head(combined_features)
            return machine_logits

    def sample_action(self, state_input, ready_mask=None) -> Tuple[torch.Tensor, torch.Tensor]:
        """采样动作"""
        if self.action_mode == "dual":
            task_logits, machine_logits = self.forward(state_input, ready_mask)

            # 采样任务和机器
            task_dist = torch.distributions.Categorical(logits=task_logits)
            machine_dist = torch.distributions.Categorical(logits=machine_logits)

            task_action = task_dist.sample()
            machine_action = machine_dist.sample()

            # 计算log概率
            task_log_prob = task_dist.log_prob(task_action)
            machine_log_prob = machine_dist.log_prob(machine_action)

            return (task_action, machine_action), (task_log_prob, machine_log_prob)
        else:
            machine_logits = self.forward(state_input, ready_mask)

            machine_dist = torch.distributions.Categorical(logits=machine_logits)
            machine_action = machine_dist.sample()
            machine_log_prob = machine_dist.log_prob(machine_action)

            return machine_action, machine_log_prob

class UnifiedCritic(nn.Module):
    """统一Critic网络"""

    def __init__(self, config):
        super(UnifiedCritic, self).__init__()

        self.config = config
        self.network_type = config.NETWORK_TYPE
        self.action_mode = config.ACTION_MODE

        # 选择编码器（与Actor相同）
        if self.network_type == "mlp":
            self.encoder = MLPEncoder(config.ENHANCED_STATE_DIM, config.HIDDEN_DIM)
            self.use_sequence = False
        elif self.network_type == "lstm":
            self.encoder = LSTMEncoder(config.TASK_FEATURE_DIM_WITH_MASK, config.LSTM_HIDDEN_DIM)
            self.global_processor = MLPEncoder(config.ENHANCED_STATE_DIM, config.HIDDEN_DIM // 2)
            self.use_sequence = True
        elif self.network_type == "xlstm":
            self.encoder = xLSTMEncoder(config.TASK_FEATURE_DIM_WITH_MASK, config.LSTM_HIDDEN_DIM, config.XLSTM_LAYERS)
            self.global_processor = MLPEncoder(config.ENHANCED_STATE_DIM, config.HIDDEN_DIM // 2)
            self.use_sequence = True

        # 计算特征维度
        if self.use_sequence:
            feature_dim = self.encoder.output_dim + self.global_processor.output_dim
        else:
            feature_dim = self.encoder.output_dim

        # 动作维度
        if self.action_mode == "dual":
            action_dim = config.TASK_ACTION_DIM + config.MACHINE_ACTION_DIM
        else:
            action_dim = config.MACHINE_ACTION_DIM

        # Q值网络
        self.q_network = nn.Sequential(
            nn.Linear(feature_dim + action_dim, config.HIDDEN_DIM),
            nn.ReLU(),
            nn.Linear(config.HIDDEN_DIM, config.HIDDEN_DIM),
            nn.ReLU(),
            nn.Linear(config.HIDDEN_DIM, 1)
        )

        self._init_weights()

    def _init_weights(self):
        """初始化权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight, gain=0.1)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0.0)

    def forward(self, state_input, action) -> torch.Tensor:
        """前向传播"""
        # 编码状态
        if self.use_sequence:
            global_state, task_sequence = state_input
            global_features = self.global_processor(global_state)
            task_mask = task_sequence[:, :, -1]
            sequence_features = self.encoder(task_sequence, task_mask)
            state_features = torch.cat([global_features, sequence_features], dim=-1)
        else:
            global_state = state_input
            state_features = self.encoder(global_state)

        # 处理动作
        if isinstance(action, tuple):
            # 双动作模式
            task_action, machine_action = action
            task_one_hot = F.one_hot(task_action, self.config.TASK_ACTION_DIM).float()
            machine_one_hot = F.one_hot(machine_action, self.config.MACHINE_ACTION_DIM).float()
            action_features = torch.cat([task_one_hot, machine_one_hot], dim=-1)
        else:
            # 单动作模式
            action_features = F.one_hot(action, self.config.MACHINE_ACTION_DIM).float()

        # 合并状态和动作特征
        combined_features = torch.cat([state_features, action_features], dim=-1)

        # 计算Q值
        q_value = self.q_network(combined_features)

        return q_value

def create_networks(config):
    """创建网络的工厂函数"""
    actor = UnifiedActor(config)
    critic1 = UnifiedCritic(config)
    critic2 = UnifiedCritic(config)

    print(f"[UNIFIED_NETWORKS] 创建网络完成")
    print(f"  网络类型: {config.NETWORK_TYPE}")
    print(f"  动作模式: {config.ACTION_MODE}")

    if config.NETWORK_TYPE == "mlp":
        print(f"  MLP编码器维度: {config.ENHANCED_STATE_DIM} -> {config.HIDDEN_DIM}")
    else:
        print(f"  序列编码器维度: {config.TASK_FEATURE_DIM_WITH_MASK} -> {config.LSTM_HIDDEN_DIM}")
        print(f"  全局处理器维度: {config.ENHANCED_STATE_DIM} -> {config.HIDDEN_DIM // 2}")

    return actor, critic1, critic2
