#!/usr/bin/env python3
"""
统一调度环境
结合事件驱动和复杂状态表示
"""

import numpy as np
import networkx as nx
from typing import Dict, List, Tuple, Any, Optional
from collections import defaultdict, deque
import copy

class Task:
    """任务类"""
    def __init__(self, task_id: int, features: np.ndarray):
        self.task_id = task_id
        self.memory_req = features[0]  # 内存需求 (GB)
        self.predicted_tokens = features[1]  # 预测输出token数
        self.is_llm = bool(features[2])  # 是否LLM任务
        self.compute_intensity = features[3]  # 计算强度
        self.data_size = features[4]  # 预估运行时内存

        # 执行状态 - 使用复杂版本的状态定义
        self.status = "未就绪/等待输入"  # 初始状态
        self.assigned_machine = None
        self.start_time = None
        self.end_time = None
        self.execution_time = None

        # 依赖关系
        self.predecessors = set()
        self.successors = set()

    def is_ready(self, completed_tasks: set) -> bool:
        """检查任务是否就绪"""
        return self.status == "信息完整/就绪可调度"

    def update_status_based_on_dependencies(self, completed_tasks: set):
        """根据依赖关系更新任务状态"""
        if self.status == "未就绪/等待输入":
            # 检查前序依赖是否都完成
            if self.predecessors.issubset(completed_tasks):
                # 对于LLM任务，需要确保输入token已确定
                if self.is_llm:
                    # 假设预测器已经预估了输出token数，直接设为就绪
                    self.status = "信息完整/就绪可调度"
                else:
                    # 普通任务直接就绪
                    self.status = "信息完整/就绪可调度"

    def start_execution(self):
        """开始执行任务"""
        self.status = "运行中"

    def complete_execution(self):
        """完成任务执行"""
        self.status = "已完成/失败"

    def fail_execution(self):
        """任务执行失败"""
        self.status = "已完成/失败"

    def can_run_on_machine(self, machine_available_memory: float) -> bool:
        """检查任务是否可以在指定机器上运行"""
        return self.memory_req <= machine_available_memory

    def __repr__(self):
        return f"Task({self.task_id}, {self.status}, mem={self.memory_req:.1f}GB, llm={self.is_llm})"

class Machine:
    """机器类"""
    def __init__(self, machine_id: int, memory: float, cpu_freq: float, ttft: float, tpot: float, machine_type: str = "Unknown"):
        self.machine_id = machine_id
        self.total_memory = memory
        self.available_memory = memory
        self.cpu_freq = cpu_freq
        self.ttft = ttft  # Time To First Token
        self.tpot = tpot  # Time Per Output Token
        self.machine_type = machine_type

        # 运行状态
        self.running_tasks = []
        self.task_queue = deque()
        self.is_busy = False
        self.current_task_end_time = 0.0

    def can_accept_task(self, task: Task) -> bool:
        """检查机器是否可以接受任务"""
        return task.memory_req <= self.available_memory

    def assign_task(self, task: Task, current_time: float) -> float:
        """分配任务到机器"""
        if not self.can_accept_task(task):
            return float('inf')  # 无法分配

        # 计算执行时间
        if task.is_llm:
            # LLM任务执行时间 = TTFT + predicted_tokens * TPOT
            execution_time = self.ttft + task.predicted_tokens * self.tpot
        else:
            # 普通任务执行时间基于计算强度和CPU频率
            base_time = task.compute_intensity * 10.0  # 基础时间
            execution_time = base_time / self.cpu_freq

        # 考虑数据传输时间
        transfer_time = task.data_size * 2.0  # 上传+下载时间
        total_time = execution_time + transfer_time

        # 更新机器状态
        self.available_memory -= task.memory_req
        self.running_tasks.append(task)

        # 设置任务状态
        task.status = "running"
        task.assigned_machine = self.machine_id
        task.start_time = current_time
        task.execution_time = total_time
        task.end_time = current_time + total_time

        # 更新机器忙碌状态
        if not self.is_busy or current_time + total_time > self.current_task_end_time:
            self.is_busy = True
            self.current_task_end_time = current_time + total_time

        return total_time

    def complete_tasks(self, current_time: float) -> List[Task]:
        """完成到期的任务"""
        completed = []
        remaining_tasks = []

        for task in self.running_tasks:
            if task.end_time <= current_time:
                # 任务完成
                task.status = "completed"
                self.available_memory += task.memory_req
                completed.append(task)
            else:
                remaining_tasks.append(task)

        self.running_tasks = remaining_tasks

        # 更新机器忙碌状态
        if not self.running_tasks:
            self.is_busy = False
            self.current_task_end_time = current_time
        else:
            self.current_task_end_time = min(task.end_time for task in self.running_tasks)

        return completed

    def get_memory_utilization(self) -> float:
        """获取内存利用率"""
        return (self.total_memory - self.available_memory) / self.total_memory

    def __repr__(self):
        return f"Machine({self.machine_id}, {self.machine_type}, mem={self.available_memory:.1f}/{self.total_memory:.1f}GB, busy={self.is_busy})"

class UnifiedSchedulingEnvironment:
    """统一调度环境"""

    def __init__(self, config, data_loader):
        self.config = config
        self.data_loader = data_loader

        # 环境状态
        self.current_time = 0.0
        self.tasks = []
        self.machines = []
        self.completed_tasks = set()
        self.failed_tasks = set()

        # DAG信息
        self.dag_graph = None
        self.total_tasks = 0

        # 统计信息
        self.episode_stats = {
            'total_time': 0.0,
            'completion_rate': 0.0,
            'memory_efficiency': 0.0,
            'failed_count': 0
        }

        # 缓存机器资源和通信速度以提高性能
        self._cached_machine_resources = None
        self._cached_comm_speed = None

        print(f"[UNIFIED_ENV] 初始化完成")

    def reset(self, task_features: np.ndarray, adj_matrix: np.ndarray) -> np.ndarray:
        """重置环境"""
        self.current_time = 0.0
        self.completed_tasks = set()
        self.failed_tasks = set()

        # 创建任务
        self._create_tasks(task_features, adj_matrix)

        # 创建机器
        self._create_machines()

        # 构建DAG
        self._build_dag(adj_matrix)

        # 缓存机器资源和通信速度
        self._cached_machine_resources = self.data_loader.get_machine_resources()
        self._cached_comm_speed = self.data_loader.get_communication_speeds()

        # 初始化任务状态
        self._update_task_statuses()

        # 获取初始状态
        return self._get_current_state()

    def _create_tasks(self, task_features: np.ndarray, adj_matrix: np.ndarray):
        """创建任务"""
        self.total_tasks = task_features.shape[1]
        self.tasks = []

        for i in range(self.total_tasks):
            task = Task(i, task_features[:, i])
            self.tasks.append(task)

        # 设置依赖关系
        for i in range(self.total_tasks):
            for j in range(self.total_tasks):
                if adj_matrix[i, j] == 1:
                    self.tasks[i].successors.add(j)
                    self.tasks[j].predecessors.add(i)

    def _create_machines(self):
        """创建机器"""
        machine_resources = self.data_loader.get_machine_resources()
        self.machines = []

        for i in range(len(machine_resources['memory'])):
            machine = Machine(
                machine_id=i,
                memory=machine_resources['memory'][i],
                cpu_freq=machine_resources['cpu_freq'][i],
                ttft=machine_resources['ttft'][i],
                tpot=machine_resources['tpot'][i],
                machine_type=machine_resources['types'][i] if machine_resources['types'] is not None else f"Machine_{i}"
            )
            self.machines.append(machine)

    def _build_dag(self, adj_matrix: np.ndarray):
        """构建DAG图"""
        self.dag_graph = nx.DiGraph()

        # 添加节点
        for i in range(self.total_tasks):
            self.dag_graph.add_node(i)

        # 添加边
        for i in range(self.total_tasks):
            for j in range(self.total_tasks):
                if adj_matrix[i, j] == 1:
                    self.dag_graph.add_edge(i, j)

    def step(self, action) -> Tuple[np.ndarray, float, bool, Dict[str, Any]]:
        """执行一步"""
        # 解析动作
        if self.config.ACTION_MODE == "dual":
            # 双动作模式：action是(task_action, machine_action)元组
            if isinstance(action, tuple) and len(action) == 2:
                task_action, machine_action = action
            else:
                # 兼容性处理：如果是单个值，解析为复合动作
                task_action, machine_action = self._parse_dual_action(action)
        else:
            # 单动作模式：action是机器选择
            task_action, machine_action = self._parse_single_action(action)

        # 获取就绪任务
        ready_tasks = self._get_ready_tasks()

        if not ready_tasks:
            # 没有就绪任务，推进时间到下一个事件
            self._advance_to_next_event()
            return self._get_current_state(), 0.0, self._is_done(), self._get_info()

        # 调试信息
        if len(ready_tasks) == 0:
            print(f"[DEBUG] 时间 {self.current_time:.2f}: 没有就绪任务")
            print(f"[DEBUG] 任务状态: {[task.status for task in self.tasks[:5]]}")  # 只显示前5个任务

        # 选择任务
        if self.config.ACTION_MODE == "dual" and 0 <= task_action < len(ready_tasks):
            selected_task = ready_tasks[task_action]
        else:
            # 单动作模式或无效任务选择，选择第一个就绪任务
            selected_task = ready_tasks[0]

        # 选择机器
        if 0 <= machine_action < len(self.machines):
            selected_machine = self.machines[machine_action]
        else:
            # 无效机器选择，选择第一台机器
            selected_machine = self.machines[0]

        # 尝试分配任务
        reward = self._assign_task_to_machine(selected_task, selected_machine)

        # 推进时间到下一个事件
        self._advance_to_next_event()

        # 返回新状态
        next_state = self._get_current_state()
        done = self._is_done()
        info = self._get_info()

        return next_state, reward, done, info

    def _parse_dual_action(self, action: int) -> Tuple[int, int]:
        """解析双动作"""
        # 假设action是复合动作的索引
        # 这里需要根据具体的动作编码方式来实现
        task_action = action % self.config.TASK_ACTION_DIM
        machine_action = action // self.config.TASK_ACTION_DIM
        return task_action, machine_action

    def _parse_single_action(self, action: int) -> Tuple[int, int]:
        """解析单动作"""
        return 0, action  # 任务选择固定为0（第一个就绪任务）

    def _get_ready_tasks(self) -> List[Task]:
        """获取就绪任务"""
        ready_tasks = []
        for task in self.tasks:
            if task.is_ready(self.completed_tasks):
                ready_tasks.append(task)
        return ready_tasks

    def _assign_task_to_machine(self, task: Task, machine: Machine) -> float:
        """分配任务到机器"""
        if machine.can_accept_task(task):
            execution_time = machine.assign_task(task, self.current_time)
            # 更新任务状态为运行中
            task.start_execution()
            return 0.1  # 成功分配的小奖励
        else:
            # 分配失败，任务标记为失败
            task.fail_execution()
            self.failed_tasks.add(task.task_id)
            return -1.0  # 失败惩罚

    def _advance_to_next_event(self):
        """推进时间到下一个事件 - 修复版本"""
        # 先处理在当前时间点或之前就已完成的任务
        newly_completed = []
        for machine in self.machines:
            completed = machine.complete_tasks(self.current_time)
            for task in completed:
                if task.task_id not in self.completed_tasks and task.task_id not in self.failed_tasks:
                    self.completed_tasks.add(task.task_id)
                    task.complete_execution()
                    newly_completed.append(task)

        # 更新其他任务的状态
        if newly_completed:
            self._update_task_statuses()

        # 寻找下一个最早的未来任务完成时间
        min_future_end_time = float('inf')
        for machine in self.machines:
            for task in machine.running_tasks:
                if task.end_time > self.current_time:  # 只考虑未来的完成时间
                    min_future_end_time = min(min_future_end_time, task.end_time)

        # 如果没有正在运行的任务会在此之后结束
        if min_future_end_time == float('inf'):
            if not self._get_ready_tasks() and not any(m.running_tasks for m in self.machines):
                # 所有事情都做完了，或者卡住了，时间不再推进
                return
            # 否则可能只是当前没有任务在运行，但有就绪任务等待调度

        # 推进时间到下一个事件点（确保时间向前推进）
        if min_future_end_time > self.current_time:
            self.current_time = min_future_end_time

            # 再次处理在新当前时间点完成的任务
            newly_completed = []
            for machine in self.machines:
                completed = machine.complete_tasks(self.current_time)
                for task in completed:
                    if task.task_id not in self.completed_tasks and task.task_id not in self.failed_tasks:
                        self.completed_tasks.add(task.task_id)
                        task.complete_execution()
                        newly_completed.append(task)

            # 再次更新任务状态
            if newly_completed:
                self._update_task_statuses()

    def _update_task_statuses(self):
        """更新所有任务的状态"""
        for task in self.tasks:
            task.update_status_based_on_dependencies(self.completed_tasks)

    def _handle_task_completion(self, task: Task):
        """处理任务完成事件"""
        # 对于LLM任务，可能需要更新后续任务的输入token信息
        # 这里假设预测器已经预先计算了所有信息，所以不需要额外处理

        # 更新后续任务的状态
        for successor_id in task.successors:
            if successor_id < len(self.tasks):
                successor_task = self.tasks[successor_id]
                successor_task.update_status_based_on_dependencies(self.completed_tasks)

    def _get_current_state(self) -> Tuple[np.ndarray, np.ndarray]:
        """获取当前状态"""
        # 获取任务特征
        task_features = np.zeros((5, self.total_tasks))
        task_status_list = []

        for i, task in enumerate(self.tasks):
            task_features[0, i] = task.memory_req
            task_features[1, i] = task.predicted_tokens  # 预估输出token数
            task_features[2, i] = float(task.is_llm)
            task_features[3, i] = task.compute_intensity
            task_features[4, i] = task.data_size  # 预估运行时内存

            # 收集任务状态
            task_status_list.append(task.status)

        # 构建邻接矩阵
        adj_matrix = np.zeros((self.total_tasks, self.total_tasks))
        for i, task in enumerate(self.tasks):
            for successor_id in task.successors:
                if successor_id < self.total_tasks:
                    adj_matrix[i, successor_id] = 1

        # 使用缓存的机器资源和通信速度
        machine_resources = self._cached_machine_resources
        comm_speed = self._cached_comm_speed

        # 构建内存状态
        memory_status = np.array([m.available_memory for m in self.machines])

        # 处理为增强状态
        enhanced_state, ready_mask = self.data_loader.process_state_as_enhanced_sequence(
            task_features, adj_matrix, machine_resources, comm_speed, task_status_list, memory_status
        )

        return enhanced_state, ready_mask

    def _is_done(self) -> bool:
        """检查是否完成"""
        # 所有任务都完成或失败
        total_finished = len(self.completed_tasks) + len(self.failed_tasks)
        return total_finished >= self.total_tasks

    def _get_info(self) -> Dict[str, Any]:
        """获取信息"""
        completion_rate = len(self.completed_tasks) / self.total_tasks

        # 计算内存效率
        total_memory_utilization = sum(m.get_memory_utilization() for m in self.machines) / len(self.machines)

        return {
            'completion_rate': completion_rate,
            'makespan': self.current_time,
            'memory_efficiency': total_memory_utilization,
            'completed_tasks': len(self.completed_tasks),
            'failed_tasks': len(self.failed_tasks),
            'total_tasks': self.total_tasks,
            'current_time': self.current_time
        }
