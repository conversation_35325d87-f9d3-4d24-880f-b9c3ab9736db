#!/usr/bin/env python3
"""
统一调度系统配置
结合复杂版本的状态空间和简化版本的事件驱动
"""

import os
import numpy as np

class UnifiedConfig:
    """统一调度系统配置"""

    def __init__(self):
        # 基础配置
        self.NUM_EDGE_SERVERS = 5
        self.MAX_TASKS_NUM = 32

        # 复杂版本的状态空间配置（与enhanced_config.py完全一致）
        # 静态任务特征维度
        self.STATIC_TASK_DIM = 3  # CPU周期、数据传输、是否LLM

        # 任务状态定义（与复杂版本完全一致）
        self.TASK_STATUS_DIM = 4  # 任务状态的one-hot编码维度
        self.TASK_STATUS_NAMES = [
            "未就绪/等待输入",      # 0: 前序依赖未满足，或LLM任务的输入token尚未确定
            "信息完整/就绪可调度",   # 1: 所有依赖满足，LLM任务的输入输出已预估完成
            "运行中",              # 2: 任务正在执行
            "已完成/失败"          # 3: 任务已完成或失败
        ]

        # 动态特征维度
        self.DYNAMIC_FEATURES_DIM = (
            self.TASK_STATUS_DIM +  # 任务状态one-hot编码 (4维)
            1 +                     # 预估输出token数量（归一化）(1维)
            1                       # 预估运行时内存（归一化）(1维)
        )  # = 6

        # 状态维度配置（随设备数动态变化）
        self.STATE_COMM_DIM = 1  # 通信特征
        self.STATE_MEM_DIM = self.NUM_EDGE_SERVERS + 1  # 内存状态
        self.STATE_COMPUTE_DIM = self.NUM_EDGE_SERVERS + 1  # 计算能力
        self.STATE_TTFT_DIM = self.NUM_EDGE_SERVERS + 1  # TTFT
        self.STATE_TPOT_DIM = self.NUM_EDGE_SERVERS + 1  # TPOT

        # DAG结构信息
        self.MAX_PREDECESSORS = 3  # 每个任务最多考虑3个直接前继任务
        self.MAX_SUCCESSORS = 3  # 每个任务最多考虑3个直接后继任务
        self.STATE_DAG_DIM = self.MAX_PREDECESSORS + self.MAX_SUCCESSORS  # = 6

        # 计算增强状态维度（与复杂版本完全一致）
        self.ENHANCED_STATE_DIM = (
            self.STATIC_TASK_DIM +      # 静态任务特征 (3)
            self.DYNAMIC_FEATURES_DIM + # 动态特征 (6)
            self.STATE_COMM_DIM +       # 通信特征 (1)
            self.STATE_MEM_DIM +        # 内存状态 (NUM_EDGE_SERVERS+1)
            self.STATE_COMPUTE_DIM +    # 计算能力 (NUM_EDGE_SERVERS+1)
            self.STATE_TTFT_DIM +       # TTFT (NUM_EDGE_SERVERS+1)
            self.STATE_TPOT_DIM +       # TPOT (NUM_EDGE_SERVERS+1)
            self.STATE_DAG_DIM +        # DAG结构 (6)
            1                           # 有效任务掩码 (1)
        )

        # 兼容性别名
        self.STATE_DIM = self.ENHANCED_STATE_DIM

        # 序列模型配置
        self.SEQ_LEN = self.MAX_TASKS_NUM
        self.TASK_FEATURE_DIM = self.ENHANCED_STATE_DIM  # 任务特征维度等于增强状态维度
        self.TASK_FEATURE_DIM_WITH_MASK = self.TASK_FEATURE_DIM + 1  # 包含mask

        # 动作空间配置
        self.ACTION_MODE = "dual"  # "dual" 或 "single"
        self.TASK_ACTION_DIM = self.MAX_TASKS_NUM  # 任务选择动作维度
        self.MACHINE_ACTION_DIM = self.NUM_EDGE_SERVERS + 1  # 机器选择动作维度

        if self.ACTION_MODE == "dual":
            self.COMPOUND_ACTION_DIM = 2  # 双动作：任务选择 + 机器选择
            self.ACTION_DIM = self.TASK_ACTION_DIM + self.MACHINE_ACTION_DIM
        else:
            self.COMPOUND_ACTION_DIM = 1  # 单动作：只选择机器
            self.ACTION_DIM = self.MACHINE_ACTION_DIM

        # 网络架构配置
        self.NETWORK_TYPE = "mlp"  # "mlp", "lstm", "xlstm"
        self.HIDDEN_DIM = 256
        self.LSTM_HIDDEN_DIM = 128
        self.XLSTM_LAYERS = 2

        # 训练配置
        self.LEARNING_RATE = 3e-4
        self.BATCH_SIZE = 64
        self.REPLAY_BUFFER_SIZE = 100000
        self.GAMMA = 0.99
        self.TAU = 0.005

        # 奖励配置
        self.REWARD_TYPE = "sparse"  # "sparse" 或 "dense"
        self.COMPLETION_WEIGHT = 10.0
        self.MAKESPAN_WEIGHT = 5.0
        self.EFFICIENCY_WEIGHT = 2.0
        self.PENALTY_WEIGHT = 1.0

        # 数据路径配置
        self.TRAINING_DATA_PATH = "../enhanced_llm_scheduling/dataset/training/DAG_30_layered_fat0.4_density0.5_5devices"
        self.TESTING_DATA_PATH = "../dataset/testing"

        # 机器资源配置
        self.MACHINES_RESOURCE_PATH = None  # 将在set_data_path中设置
        self.COMMUNICATION_SPEED_PATH = None

        # LLM执行参数
        self.USER_DEVICE_TTFT = 0.23
        self.USER_DEVICE_TPOT = 0.013
        self.EDGE_DEVICE_TTFT = 0.046
        self.EDGE_DEVICE_TPOT = 0.1

        # 事件驱动配置
        self.USE_EVENT_DRIVEN = True
        self.CURRENT_TIME = 0.0

        # 奖励归一化
        self.USE_REWARD_NORMALIZATION = True
        self.REWARD_MIN = float('inf')
        self.REWARD_MAX = float('-inf')

        print(f"[UNIFIED_CONFIG] 初始化完成")
        print(f"  状态维度: {self.STATE_DIM}")
        print(f"  动作模式: {self.ACTION_MODE}")
        print(f"  网络类型: {self.NETWORK_TYPE}")
        print(f"  事件驱动: {self.USE_EVENT_DRIVEN}")

    def get_task_status_index(self, status_name):
        """获取任务状态的索引"""
        try:
            return self.TASK_STATUS_NAMES.index(status_name)
        except ValueError:
            return 0  # 默认返回"未就绪"状态

    def create_task_status_onehot(self, status_index):
        """创建任务状态的one-hot编码"""
        import numpy as np
        onehot = np.zeros(self.TASK_STATUS_DIM)
        if 0 <= status_index < self.TASK_STATUS_DIM:
            onehot[status_index] = 1.0
        return onehot

    def set_action_mode(self, mode):
        """设置动作模式"""
        self.ACTION_MODE = mode
        if mode == "dual":
            self.COMPOUND_ACTION_DIM = 2
            self.ACTION_DIM = self.TASK_ACTION_DIM + self.MACHINE_ACTION_DIM
        else:
            self.COMPOUND_ACTION_DIM = 1
            self.ACTION_DIM = self.MACHINE_ACTION_DIM
        print(f"[UNIFIED_CONFIG] 动作模式设置为: {mode}")

    def set_network_type(self, network_type):
        """设置网络类型"""
        self.NETWORK_TYPE = network_type
        print(f"[UNIFIED_CONFIG] 网络类型设置为: {network_type}")

    def set_data_path(self, path):
        """设置数据路径"""
        self.TRAINING_DATA_PATH = path
        self.MACHINES_RESOURCE_PATH = os.path.join(path, "machines_resource1.xlsx")
        self.COMMUNICATION_SPEED_PATH = os.path.join(path, "communication_speed.xlsx")
        print(f"[UNIFIED_CONFIG] 数据路径设置为: {path}")

    def update_edge_servers(self, num_servers):
        """更新边缘设备数量"""
        self.NUM_EDGE_SERVERS = num_servers
        self.MACHINE_ACTION_DIM = num_servers + 1

        # 重新计算状态维度（与复杂版本一致）
        self.STATE_MEM_DIM = self.NUM_EDGE_SERVERS + 1
        self.STATE_COMPUTE_DIM = self.NUM_EDGE_SERVERS + 1
        self.STATE_TTFT_DIM = self.NUM_EDGE_SERVERS + 1
        self.STATE_TPOT_DIM = self.NUM_EDGE_SERVERS + 1

        # 重新计算增强状态维度
        self.ENHANCED_STATE_DIM = (
            self.STATIC_TASK_DIM +      # 静态任务特征 (3)
            self.DYNAMIC_FEATURES_DIM + # 动态特征 (6)
            self.STATE_COMM_DIM +       # 通信特征 (1)
            self.STATE_MEM_DIM +        # 内存状态 (NUM_EDGE_SERVERS+1)
            self.STATE_COMPUTE_DIM +    # 计算能力 (NUM_EDGE_SERVERS+1)
            self.STATE_TTFT_DIM +       # TTFT (NUM_EDGE_SERVERS+1)
            self.STATE_TPOT_DIM +       # TPOT (NUM_EDGE_SERVERS+1)
            self.STATE_DAG_DIM +        # DAG结构 (6)
            1                           # 有效任务掩码 (1)
        )

        # 更新兼容性别名
        self.STATE_DIM = self.ENHANCED_STATE_DIM

        # 重新计算动作维度
        if self.ACTION_MODE == "dual":
            self.ACTION_DIM = self.TASK_ACTION_DIM + self.MACHINE_ACTION_DIM
        else:
            self.ACTION_DIM = self.MACHINE_ACTION_DIM

        print(f"[UNIFIED_CONFIG] 边缘设备数更新为: {num_servers}")
        print(f"[UNIFIED_CONFIG] 增强状态维度更新为: {self.ENHANCED_STATE_DIM}")

    def get_state_info(self):
        """获取状态信息"""
        return {
            'state_dim': self.STATE_DIM,
            'task_feature_dim': self.TASK_FEATURE_DIM,
            'machine_feature_dim': self.MACHINE_FEATURE_DIM,
            'enhanced_state_dim': self.ENHANCED_STATE_DIM,
            'seq_len': self.SEQ_LEN
        }

    def get_action_info(self):
        """获取动作信息"""
        return {
            'action_mode': self.ACTION_MODE,
            'action_dim': self.ACTION_DIM,
            'task_action_dim': self.TASK_ACTION_DIM,
            'machine_action_dim': self.MACHINE_ACTION_DIM,
            'compound_action_dim': self.COMPOUND_ACTION_DIM
        }

    def get_network_info(self):
        """获取网络信息"""
        return {
            'network_type': self.NETWORK_TYPE,
            'hidden_dim': self.HIDDEN_DIM,
            'lstm_hidden_dim': self.LSTM_HIDDEN_DIM,
            'xlstm_layers': self.XLSTM_LAYERS
        }

    def update_reward_bounds(self, reward_value):
        """更新奖励归一化边界"""
        if self.USE_REWARD_NORMALIZATION:
            self.REWARD_MIN = min(self.REWARD_MIN, reward_value)
            self.REWARD_MAX = max(self.REWARD_MAX, reward_value)

    def get_normalized_reward(self, reward_value):
        """获取归一化奖励"""
        if not self.USE_REWARD_NORMALIZATION:
            return reward_value

        if self.REWARD_MAX <= self.REWARD_MIN:
            return 0.0

        normalized = (reward_value - self.REWARD_MIN) / (self.REWARD_MAX - self.REWARD_MIN)
        return max(-1.0, min(1.0, normalized))

    def print_config(self):
        """打印配置信息"""
        print("\n" + "="*80)
        print("🎯 统一调度系统配置")
        print("="*80)

        print(f"📊 状态空间:")
        print(f"  总维度: {self.STATE_DIM}")
        print(f"  增强状态维度: {self.ENHANCED_STATE_DIM}")
        print(f"  任务特征维度: {self.TASK_FEATURE_DIM}")
        print(f"  序列长度: {self.SEQ_LEN}")

        print(f"\n🎮 动作空间:")
        print(f"  模式: {self.ACTION_MODE}")
        print(f"  总维度: {self.ACTION_DIM}")
        if self.ACTION_MODE == "dual":
            print(f"  任务选择: {self.TASK_ACTION_DIM}")
            print(f"  机器选择: {self.MACHINE_ACTION_DIM}")
        else:
            print(f"  机器选择: {self.MACHINE_ACTION_DIM}")

        print(f"\n🧠 网络架构:")
        print(f"  类型: {self.NETWORK_TYPE}")
        print(f"  隐藏维度: {self.HIDDEN_DIM}")
        if self.NETWORK_TYPE == "lstm":
            print(f"  LSTM隐藏维度: {self.LSTM_HIDDEN_DIM}")
        elif self.NETWORK_TYPE == "xlstm":
            print(f"  xLSTM层数: {self.XLSTM_LAYERS}")

        print(f"\n🏆 奖励配置:")
        print(f"  类型: {self.REWARD_TYPE}")
        print(f"  完成权重: {self.COMPLETION_WEIGHT}")
        print(f"  时间权重: {self.MAKESPAN_WEIGHT}")
        print(f"  效率权重: {self.EFFICIENCY_WEIGHT}")

        print(f"\n📁 数据配置:")
        print(f"  训练数据: {self.TRAINING_DATA_PATH}")
        print(f"  机器资源: {self.MACHINES_RESOURCE_PATH}")

        print("="*80)
