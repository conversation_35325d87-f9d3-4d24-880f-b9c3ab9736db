================================================================================
统一调度系统代码合集 - 供代码检查使用
================================================================================

本文件包含统一调度系统的核心代码，包括：
1. 配置文件 (unified_config.py)
2. 环境代码 (unified_environment.py)
3. SAC智能体 (unified_sac_agent.py)
4. 网络架构 (unified_networks.py)
5. 数据加载器 (unified_data_loader.py)

================================================================================
1. 配置文件 (unified_config.py)
================================================================================

#!/usr/bin/env python3
"""
统一调度系统配置
结合复杂版本的状态空间和简化版本的事件驱动
"""

import os
import numpy as np

class UnifiedConfig:
    """统一调度系统配置"""

    def __init__(self):
        # 基础配置
        self.NUM_EDGE_SERVERS = 5
        self.MAX_TASKS_NUM = 32

        # 复杂版本的状态空间配置（与enhanced_config.py完全一致）
        # 静态任务特征维度
        self.STATIC_TASK_DIM = 3  # CPU周期、数据传输、是否LLM

        # 任务状态定义（与复杂版本完全一致）
        self.TASK_STATUS_DIM = 4  # 任务状态的one-hot编码维度
        self.TASK_STATUS_NAMES = [
            "未就绪/等待输入",      # 0: 前序依赖未满足，或LLM任务的输入token尚未确定
            "信息完整/就绪可调度",   # 1: 所有依赖满足，LLM任务的输入输出已预估完成
            "运行中",              # 2: 任务正在执行
            "已完成/失败"          # 3: 任务已完成或失败
        ]

        # 动态特征维度
        self.DYNAMIC_FEATURES_DIM = (
            self.TASK_STATUS_DIM +  # 任务状态one-hot编码 (4维)
            1 +                     # 预估输出token数量（归一化）(1维)
            1                       # 预估运行时内存（归一化）(1维)
        )  # = 6

        # 状态维度配置（随设备数动态变化）
        self.STATE_COMM_DIM = 1  # 通信特征
        self.STATE_MEM_DIM = self.NUM_EDGE_SERVERS + 1  # 内存状态
        self.STATE_COMPUTE_DIM = self.NUM_EDGE_SERVERS + 1  # 计算能力
        self.STATE_TTFT_DIM = self.NUM_EDGE_SERVERS + 1  # TTFT
        self.STATE_TPOT_DIM = self.NUM_EDGE_SERVERS + 1  # TPOT

        # DAG结构信息
        self.MAX_PREDECESSORS = 3  # 每个任务最多考虑3个直接前继任务
        self.MAX_SUCCESSORS = 3  # 每个任务最多考虑3个直接后继任务
        self.STATE_DAG_DIM = self.MAX_PREDECESSORS + self.MAX_SUCCESSORS  # = 6

        # 计算增强状态维度（与复杂版本完全一致）
        self.ENHANCED_STATE_DIM = (
            self.STATIC_TASK_DIM +      # 静态任务特征 (3)
            self.DYNAMIC_FEATURES_DIM + # 动态特征 (6)
            self.STATE_COMM_DIM +       # 通信特征 (1)
            self.STATE_MEM_DIM +        # 内存状态 (NUM_EDGE_SERVERS+1)
            self.STATE_COMPUTE_DIM +    # 计算能力 (NUM_EDGE_SERVERS+1)
            self.STATE_TTFT_DIM +       # TTFT (NUM_EDGE_SERVERS+1)
            self.STATE_TPOT_DIM +       # TPOT (NUM_EDGE_SERVERS+1)
            self.STATE_DAG_DIM +        # DAG结构 (6)
            1                           # 有效任务掩码 (1)
        )

        # 兼容性别名
        self.STATE_DIM = self.ENHANCED_STATE_DIM

        # 序列模型配置
        self.SEQ_LEN = self.MAX_TASKS_NUM
        self.TASK_FEATURE_DIM = self.ENHANCED_STATE_DIM  # 任务特征维度等于增强状态维度
        self.TASK_FEATURE_DIM_WITH_MASK = self.TASK_FEATURE_DIM + 1  # 包含mask

        # 动作空间配置
        self.ACTION_MODE = "dual"  # "dual" 或 "single"
        self.TASK_ACTION_DIM = self.MAX_TASKS_NUM  # 任务选择动作维度
        self.MACHINE_ACTION_DIM = self.NUM_EDGE_SERVERS + 1  # 机器选择动作维度

        if self.ACTION_MODE == "dual":
            self.COMPOUND_ACTION_DIM = 2  # 双动作：任务选择 + 机器选择
            self.ACTION_DIM = self.TASK_ACTION_DIM + self.MACHINE_ACTION_DIM
        else:
            self.COMPOUND_ACTION_DIM = 1  # 单动作：只选择机器
            self.ACTION_DIM = self.MACHINE_ACTION_DIM

        # 网络架构配置
        self.NETWORK_TYPE = "mlp"  # "mlp", "lstm", "xlstm"
        self.HIDDEN_DIM = 256
        self.LSTM_HIDDEN_DIM = 128
        self.XLSTM_LAYERS = 2

        # 训练配置
        self.LEARNING_RATE = 3e-4
        self.BATCH_SIZE = 64
        self.REPLAY_BUFFER_SIZE = 100000
        self.GAMMA = 0.99
        self.TAU = 0.005

        # 奖励配置
        self.REWARD_TYPE = "sparse"  # "sparse" 或 "dense"
        self.COMPLETION_WEIGHT = 10.0
        self.MAKESPAN_WEIGHT = 5.0
        self.EFFICIENCY_WEIGHT = 2.0
        self.PENALTY_WEIGHT = 1.0

        # 数据路径配置
        self.TRAINING_DATA_PATH = "../enhanced_llm_scheduling/dataset/training/DAG_30_layered_fat0.4_density0.5_5devices"
        self.TESTING_DATA_PATH = "../dataset/testing"

        # 机器资源配置
        self.MACHINES_RESOURCE_PATH = None  # 将在set_data_path中设置
        self.COMMUNICATION_SPEED_PATH = None

        # LLM执行参数
        self.USER_DEVICE_TTFT = 0.23
        self.USER_DEVICE_TPOT = 0.013
        self.EDGE_DEVICE_TTFT = 0.046
        self.EDGE_DEVICE_TPOT = 0.1

        # 事件驱动配置
        self.USE_EVENT_DRIVEN = True
        self.CURRENT_TIME = 0.0

        # 奖励归一化
        self.USE_REWARD_NORMALIZATION = True
        self.REWARD_MIN = float('inf')
        self.REWARD_MAX = float('-inf')

        print(f"[UNIFIED_CONFIG] 初始化完成")
        print(f"  状态维度: {self.STATE_DIM}")
        print(f"  动作模式: {self.ACTION_MODE}")
        print(f"  网络类型: {self.NETWORK_TYPE}")
        print(f"  事件驱动: {self.USE_EVENT_DRIVEN}")

    def get_task_status_index(self, status_name):
        """获取任务状态的索引"""
        try:
            return self.TASK_STATUS_NAMES.index(status_name)
        except ValueError:
            return 0  # 默认返回"未就绪"状态

    def create_task_status_onehot(self, status_index):
        """创建任务状态的one-hot编码"""
        import numpy as np
        onehot = np.zeros(self.TASK_STATUS_DIM)
        if 0 <= status_index < self.TASK_STATUS_DIM:
            onehot[status_index] = 1.0
        return onehot

    def update_edge_servers(self, num_servers):
        """更新边缘设备数量"""
        self.NUM_EDGE_SERVERS = num_servers
        self.MACHINE_ACTION_DIM = num_servers + 1

        # 重新计算状态维度（与复杂版本一致）
        self.STATE_MEM_DIM = self.NUM_EDGE_SERVERS + 1
        self.STATE_COMPUTE_DIM = self.NUM_EDGE_SERVERS + 1
        self.STATE_TTFT_DIM = self.NUM_EDGE_SERVERS + 1
        self.STATE_TPOT_DIM = self.NUM_EDGE_SERVERS + 1

        # 重新计算增强状态维度
        self.ENHANCED_STATE_DIM = (
            self.STATIC_TASK_DIM +      # 静态任务特征 (3)
            self.DYNAMIC_FEATURES_DIM + # 动态特征 (6)
            self.STATE_COMM_DIM +       # 通信特征 (1)
            self.STATE_MEM_DIM +        # 内存状态 (NUM_EDGE_SERVERS+1)
            self.STATE_COMPUTE_DIM +    # 计算能力 (NUM_EDGE_SERVERS+1)
            self.STATE_TTFT_DIM +       # TTFT (NUM_EDGE_SERVERS+1)
            self.STATE_TPOT_DIM +       # TPOT (NUM_EDGE_SERVERS+1)
            self.STATE_DAG_DIM +        # DAG结构 (6)
            1                           # 有效任务掩码 (1)
        )

        # 更新兼容性别名
        self.STATE_DIM = self.ENHANCED_STATE_DIM

        # 重新计算动作维度
        if self.ACTION_MODE == "dual":
            self.ACTION_DIM = self.TASK_ACTION_DIM + self.MACHINE_ACTION_DIM
        else:
            self.ACTION_DIM = self.MACHINE_ACTION_DIM

        print(f"[UNIFIED_CONFIG] 边缘设备数更新为: {num_servers}")
        print(f"[UNIFIED_CONFIG] 增强状态维度更新为: {self.ENHANCED_STATE_DIM}")

================================================================================
2. 环境代码 (unified_environment.py) - 核心Step函数
================================================================================

class Task:
    """任务类"""
    def __init__(self, task_id: int, features: np.ndarray):
        self.task_id = task_id
        self.memory_req = features[0]  # 内存需求 (GB)
        self.predicted_tokens = features[1]  # 预测输出token数
        self.is_llm = bool(features[2])  # 是否LLM任务
        self.compute_intensity = features[3]  # 计算强度
        self.data_size = features[4]  # 数据大小

        # 执行状态
        self.status = "pending"  # pending, ready, running, completed, failed
        self.assigned_machine = None
        self.start_time = None
        self.end_time = None
        self.execution_time = None

        # 依赖关系
        self.predecessors = set()
        self.successors = set()

    def is_ready(self, completed_tasks: set) -> bool:
        """检查任务是否就绪"""
        return self.status == "pending" and self.predecessors.issubset(completed_tasks)

class Machine:
    """机器类"""
    def __init__(self, machine_id: int, memory: float, cpu_freq: float, ttft: float, tpot: float, machine_type: str = "Unknown"):
        self.machine_id = machine_id
        self.total_memory = memory
        self.available_memory = memory
        self.cpu_freq = cpu_freq
        self.ttft = ttft  # Time To First Token
        self.tpot = tpot  # Time Per Output Token
        self.machine_type = machine_type

        # 运行状态
        self.running_tasks = []
        self.task_queue = deque()
        self.is_busy = False
        self.current_task_end_time = 0.0

    def assign_task(self, task: Task, current_time: float) -> float:
        """分配任务到机器"""
        if not self.can_accept_task(task):
            return float('inf')  # 无法分配

        # 计算执行时间
        if task.is_llm:
            # LLM任务执行时间 = TTFT + predicted_tokens * TPOT
            execution_time = self.ttft + task.predicted_tokens * self.tpot
        else:
            # 普通任务执行时间基于计算强度和CPU频率
            base_time = task.compute_intensity * 10.0  # 基础时间
            execution_time = base_time / self.cpu_freq

        # 考虑数据传输时间
        transfer_time = task.data_size * 2.0  # 上传+下载时间
        total_time = execution_time + transfer_time

        # 更新机器状态
        self.available_memory -= task.memory_req
        self.running_tasks.append(task)

        # 设置任务状态
        task.status = "running"
        task.assigned_machine = self.machine_id
        task.start_time = current_time
        task.execution_time = total_time
        task.end_time = current_time + total_time

        return total_time

class UnifiedSchedulingEnvironment:
    """统一调度环境"""

    def __init__(self, config, data_loader):
        self.config = config
        self.data_loader = data_loader

        # 环境状态
        self.current_time = 0.0
        self.tasks = []
        self.machines = []
        self.completed_tasks = set()
        self.failed_tasks = set()

        # DAG信息
        self.dag_graph = None
        self.total_tasks = 0

    def step(self, action: int) -> Tuple[np.ndarray, float, bool, Dict[str, Any]]:
        """执行一步 - 核心调度逻辑"""
        # 解析动作
        if self.config.ACTION_MODE == "dual":
            task_action, machine_action = self._parse_dual_action(action)
        else:
            task_action, machine_action = self._parse_single_action(action)

        # 获取就绪任务
        ready_tasks = self._get_ready_tasks()

        if not ready_tasks:
            # 没有就绪任务，推进时间到下一个事件
            self._advance_to_next_event()
            return self._get_current_state(), 0.0, self._is_done(), self._get_info()

        # 选择任务
        if self.config.ACTION_MODE == "dual" and task_action < len(ready_tasks):
            selected_task = ready_tasks[task_action]
        else:
            # 单动作模式或无效任务选择，选择第一个就绪任务
            selected_task = ready_tasks[0]

        # 选择机器
        if machine_action < len(self.machines):
            selected_machine = self.machines[machine_action]
        else:
            # 无效机器选择，选择第一台机器
            selected_machine = self.machines[0]

        # 尝试分配任务
        reward = self._assign_task_to_machine(selected_task, selected_machine)

        # 推进时间到下一个事件
        self._advance_to_next_event()

        # 返回新状态
        next_state = self._get_current_state()
        done = self._is_done()
        info = self._get_info()

        return next_state, reward, done, info

    def _advance_to_next_event(self):
        """推进时间到下一个事件 - 事件驱动核心"""
        # 完成到期的任务
        for machine in self.machines:
            completed = machine.complete_tasks(self.current_time)
            for task in completed:
                self.completed_tasks.add(task.task_id)

        # 找到下一个事件时间
        next_event_time = float('inf')
        for machine in self.machines:
            if machine.running_tasks:
                next_event_time = min(next_event_time, machine.current_task_end_time)

        # 推进时间
        if next_event_time != float('inf'):
            self.current_time = next_event_time

            # 再次完成到期的任务
            for machine in self.machines:
                completed = machine.complete_tasks(self.current_time)
                for task in completed:
                    self.completed_tasks.add(task.task_id)

    def _assign_task_to_machine(self, task: Task, machine: Machine) -> float:
        """分配任务到机器"""
        if machine.can_accept_task(task):
            execution_time = machine.assign_task(task, self.current_time)
            return 0.1  # 成功分配的小奖励
        else:
            # 分配失败，任务标记为失败
            task.status = "failed"
            self.failed_tasks.add(task.task_id)
            return -1.0  # 失败惩罚

================================================================================
3. SAC智能体代码 (unified_sac_agent.py)
================================================================================

class UnifiedSACAgent:
    """统一SAC智能体"""

    def __init__(self, config, device='cuda'):
        self.config = config
        self.device = device

        # 创建网络
        self.actor, self.critic1, self.critic2 = create_networks(config)
        self.target_critic1, self.target_critic2 = create_networks(config)[1:]

        # 移动到设备
        self.actor.to(device)
        self.critic1.to(device)
        self.critic2.to(device)
        self.target_critic1.to(device)
        self.target_critic2.to(device)

        # 初始化目标网络
        self._hard_update(self.target_critic1, self.critic1)
        self._hard_update(self.target_critic2, self.critic2)

        # 优化器
        self.actor_optimizer = optim.Adam(self.actor.parameters(), lr=config.LEARNING_RATE)
        self.critic1_optimizer = optim.Adam(self.critic1.parameters(), lr=config.LEARNING_RATE)
        self.critic2_optimizer = optim.Adam(self.critic2.parameters(), lr=config.LEARNING_RATE)

        # SAC参数
        self.gamma = config.GAMMA
        self.tau = config.TAU
        self.alpha = 0.2  # 熵正则化系数

        # 经验回放
        self.replay_buffer = ReplayBuffer(config.REPLAY_BUFFER_SIZE)
        self.batch_size = config.BATCH_SIZE

    def select_action(self, state, deterministic=False):
        """选择动作"""
        self.actor.eval()

        with torch.no_grad():
            # 准备输入
            if isinstance(state, tuple):
                state_input = self._prepare_state_input(state)
                if self.config.NETWORK_TYPE != "mlp":
                    # 添加batch维度
                    global_state, task_sequence = state_input
                    state_input = (global_state.unsqueeze(0), task_sequence.unsqueeze(0))
                else:
                    state_input = state_input.unsqueeze(0)
            else:
                state_input = self._prepare_state_input(state).unsqueeze(0)

            if deterministic:
                # 确定性动作选择
                if self.config.ACTION_MODE == "dual":
                    task_logits, machine_logits = self.actor(state_input)
                    task_action = torch.argmax(task_logits, dim=-1)
                    machine_action = torch.argmax(machine_logits, dim=-1)
                    return task_action.cpu().numpy()[0], machine_action.cpu().numpy()[0]
                else:
                    machine_logits = self.actor(state_input)
                    machine_action = torch.argmax(machine_logits, dim=-1)
                    return machine_action.cpu().numpy()[0]
            else:
                # 随机动作选择
                if self.config.ACTION_MODE == "dual":
                    (task_action, machine_action), _ = self.actor.sample_action(state_input)
                    return task_action.cpu().numpy()[0], machine_action.cpu().numpy()[0]
                else:
                    machine_action, _ = self.actor.sample_action(state_input)
                    return machine_action.cpu().numpy()[0]

        self.actor.train()

    def update(self):
        """更新网络"""
        if len(self.replay_buffer) < self.batch_size:
            return

        # 采样批次
        states, actions, rewards, next_states, dones = self.replay_buffer.sample(self.batch_size)

        # 转换为张量
        rewards = torch.FloatTensor(rewards).to(self.device)
        dones = torch.BoolTensor(dones).to(self.device)

        # 准备状态输入
        state_inputs = []
        next_state_inputs = []

        for i in range(self.batch_size):
            state_input = self._prepare_state_input(states[i])
            next_state_input = self._prepare_state_input(next_states[i])

            state_inputs.append(state_input)
            next_state_inputs.append(next_state_input)

        # 批处理状态
        if self.config.NETWORK_TYPE == "mlp":
            batch_states = torch.stack(state_inputs)
            batch_next_states = torch.stack(next_state_inputs)
        else:
            batch_global_states = torch.stack([s[0] for s in state_inputs])
            batch_task_sequences = torch.stack([s[1] for s in state_inputs])
            batch_next_global_states = torch.stack([s[0] for s in next_state_inputs])
            batch_next_task_sequences = torch.stack([s[1] for s in next_state_inputs])

            batch_states = (batch_global_states, batch_task_sequences)
            batch_next_states = (batch_next_global_states, batch_next_task_sequences)

        # 处理动作
        if self.config.ACTION_MODE == "dual":
            task_actions = torch.LongTensor([a[0] for a in actions]).to(self.device)
            machine_actions = torch.LongTensor([a[1] for a in actions]).to(self.device)
            batch_actions = (task_actions, machine_actions)
        else:
            batch_actions = torch.LongTensor(actions).to(self.device)

        # 更新Critic
        critic_loss = self._update_critics(batch_states, batch_actions, rewards, batch_next_states, dones)

        # 更新Actor
        actor_loss = self._update_actor(batch_states)

        # 软更新目标网络
        self._soft_update(self.target_critic1, self.critic1)
        self._soft_update(self.target_critic2, self.critic2)

    def _update_critics(self, states, actions, rewards, next_states, dones):
        """更新Critic网络"""
        with torch.no_grad():
            # 计算目标Q值
            if self.config.ACTION_MODE == "dual":
                (next_task_actions, next_machine_actions), (next_task_log_probs, next_machine_log_probs) = self.actor.sample_action(next_states)
                next_actions = (next_task_actions, next_machine_actions)
                next_log_probs = next_task_log_probs + next_machine_log_probs
            else:
                next_actions, next_log_probs = self.actor.sample_action(next_states)

            target_q1 = self.target_critic1(next_states, next_actions)
            target_q2 = self.target_critic2(next_states, next_actions)
            target_q = torch.min(target_q1, target_q2) - self.alpha * next_log_probs.unsqueeze(-1)

            target_q = rewards.unsqueeze(-1) + (1 - dones.unsqueeze(-1).float()) * self.gamma * target_q

        # 当前Q值
        current_q1 = self.critic1(states, actions)
        current_q2 = self.critic2(states, actions)

        # Critic损失
        critic1_loss = F.mse_loss(current_q1, target_q)
        critic2_loss = F.mse_loss(current_q2, target_q)

        # 更新Critic1
        self.critic1_optimizer.zero_grad()
        critic1_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.critic1.parameters(), 1.0)
        self.critic1_optimizer.step()

        # 更新Critic2
        self.critic2_optimizer.zero_grad()
        critic2_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.critic2.parameters(), 1.0)
        self.critic2_optimizer.step()

        return (critic1_loss + critic2_loss).item() / 2

    def _update_actor(self, states):
        """更新Actor网络"""
        # 采样动作
        if self.config.ACTION_MODE == "dual":
            (task_actions, machine_actions), (task_log_probs, machine_log_probs) = self.actor.sample_action(states)
            actions = (task_actions, machine_actions)
            log_probs = task_log_probs + machine_log_probs
        else:
            actions, log_probs = self.actor.sample_action(states)

        # 计算Q值
        q1 = self.critic1(states, actions)
        q2 = self.critic2(states, actions)
        q = torch.min(q1, q2)

        # Actor损失
        actor_loss = (self.alpha * log_probs.unsqueeze(-1) - q).mean()

        # 更新Actor
        self.actor_optimizer.zero_grad()
        actor_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.actor.parameters(), 1.0)
        self.actor_optimizer.step()

        return actor_loss.item()

================================================================================
4. 网络架构代码 (unified_networks.py) - 核心网络结构
================================================================================

class UnifiedActor(nn.Module):
    """统一Actor网络"""

    def __init__(self, config):
        super(UnifiedActor, self).__init__()

        self.config = config
        self.network_type = config.NETWORK_TYPE
        self.action_mode = config.ACTION_MODE

        # 选择编码器
        if self.network_type == "mlp":
            # MLP模式：直接处理全局状态
            self.encoder = MLPEncoder(config.ENHANCED_STATE_DIM, config.HIDDEN_DIM)
            self.use_sequence = False
        elif self.network_type == "lstm":
            # LSTM模式：处理任务序列
            self.encoder = LSTMEncoder(config.TASK_FEATURE_DIM_WITH_MASK, config.LSTM_HIDDEN_DIM)
            self.global_processor = MLPEncoder(config.ENHANCED_STATE_DIM, config.HIDDEN_DIM // 2)
            self.use_sequence = True
        elif self.network_type == "xlstm":
            # xLSTM模式：处理任务序列
            self.encoder = xLSTMEncoder(config.TASK_FEATURE_DIM_WITH_MASK, config.LSTM_HIDDEN_DIM, config.XLSTM_LAYERS)
            self.global_processor = MLPEncoder(config.ENHANCED_STATE_DIM, config.HIDDEN_DIM // 2)
            self.use_sequence = True

        # 计算特征维度
        if self.use_sequence:
            feature_dim = self.encoder.output_dim + self.global_processor.output_dim
        else:
            feature_dim = self.encoder.output_dim

        # 动作头
        if self.action_mode == "dual":
            # 双动作：任务选择 + 机器选择
            self.task_head = nn.Linear(feature_dim, config.TASK_ACTION_DIM)
            self.machine_head = nn.Linear(feature_dim, config.MACHINE_ACTION_DIM)
        else:
            # 单动作：只有机器选择
            self.machine_head = nn.Linear(feature_dim, config.MACHINE_ACTION_DIM)

    def forward(self, state_input) -> torch.Tensor:
        """前向传播"""
        if self.use_sequence:
            # 序列模式：分别处理全局状态和任务序列
            global_state, task_sequence = state_input

            # 处理全局状态
            global_features = self.global_processor(global_state)

            # 处理任务序列
            task_mask = task_sequence[:, :, -1]  # 最后一维是mask
            sequence_features = self.encoder(task_sequence, task_mask)

            # 合并特征
            combined_features = torch.cat([global_features, sequence_features], dim=-1)
        else:
            # MLP模式：只处理全局状态
            global_state = state_input
            combined_features = self.encoder(global_state)

        # 生成动作logits
        if self.action_mode == "dual":
            task_logits = self.task_head(combined_features)
            machine_logits = self.machine_head(combined_features)
            return task_logits, machine_logits
        else:
            machine_logits = self.machine_head(combined_features)
            return machine_logits

    def sample_action(self, state_input) -> Tuple[torch.Tensor, torch.Tensor]:
        """采样动作"""
        if self.action_mode == "dual":
            task_logits, machine_logits = self.forward(state_input)

            # 采样任务和机器
            task_dist = torch.distributions.Categorical(logits=task_logits)
            machine_dist = torch.distributions.Categorical(logits=machine_logits)

            task_action = task_dist.sample()
            machine_action = machine_dist.sample()

            # 计算log概率
            task_log_prob = task_dist.log_prob(task_action)
            machine_log_prob = machine_dist.log_prob(machine_action)

            return (task_action, machine_action), (task_log_prob, machine_log_prob)
        else:
            machine_logits = self.forward(state_input)

            machine_dist = torch.distributions.Categorical(logits=machine_logits)
            machine_action = machine_dist.sample()
            machine_log_prob = machine_dist.log_prob(machine_action)

            return machine_action, machine_log_prob

class UnifiedCritic(nn.Module):
    """统一Critic网络"""

    def __init__(self, config):
        super(UnifiedCritic, self).__init__()

        self.config = config
        self.network_type = config.NETWORK_TYPE
        self.action_mode = config.ACTION_MODE

        # 选择编码器（与Actor相同）
        if self.network_type == "mlp":
            self.encoder = MLPEncoder(config.ENHANCED_STATE_DIM, config.HIDDEN_DIM)
            self.use_sequence = False
        elif self.network_type == "lstm":
            self.encoder = LSTMEncoder(config.TASK_FEATURE_DIM_WITH_MASK, config.LSTM_HIDDEN_DIM)
            self.global_processor = MLPEncoder(config.ENHANCED_STATE_DIM, config.HIDDEN_DIM // 2)
            self.use_sequence = True
        elif self.network_type == "xlstm":
            self.encoder = xLSTMEncoder(config.TASK_FEATURE_DIM_WITH_MASK, config.LSTM_HIDDEN_DIM, config.XLSTM_LAYERS)
            self.global_processor = MLPEncoder(config.ENHANCED_STATE_DIM, config.HIDDEN_DIM // 2)
            self.use_sequence = True

        # 计算特征维度
        if self.use_sequence:
            feature_dim = self.encoder.output_dim + self.global_processor.output_dim
        else:
            feature_dim = self.encoder.output_dim

        # 动作维度
        if self.action_mode == "dual":
            action_dim = config.TASK_ACTION_DIM + config.MACHINE_ACTION_DIM
        else:
            action_dim = config.MACHINE_ACTION_DIM

        # Q值网络
        self.q_network = nn.Sequential(
            nn.Linear(feature_dim + action_dim, config.HIDDEN_DIM),
            nn.ReLU(),
            nn.Linear(config.HIDDEN_DIM, config.HIDDEN_DIM),
            nn.ReLU(),
            nn.Linear(config.HIDDEN_DIM, 1)
        )

    def forward(self, state_input, action) -> torch.Tensor:
        """前向传播"""
        # 编码状态
        if self.use_sequence:
            global_state, task_sequence = state_input
            global_features = self.global_processor(global_state)
            task_mask = task_sequence[:, :, -1]
            sequence_features = self.encoder(task_sequence, task_mask)
            state_features = torch.cat([global_features, sequence_features], dim=-1)
        else:
            global_state = state_input
            state_features = self.encoder(global_state)

        # 处理动作
        if isinstance(action, tuple):
            # 双动作模式
            task_action, machine_action = action
            task_one_hot = F.one_hot(task_action, self.config.TASK_ACTION_DIM).float()
            machine_one_hot = F.one_hot(machine_action, self.config.MACHINE_ACTION_DIM).float()
            action_features = torch.cat([task_one_hot, machine_one_hot], dim=-1)
        else:
            # 单动作模式
            action_features = F.one_hot(action, self.config.MACHINE_ACTION_DIM).float()

        # 合并状态和动作特征
        combined_features = torch.cat([state_features, action_features], dim=-1)

        # 计算Q值
        q_value = self.q_network(combined_features)

        return q_value

================================================================================
5. 数据加载器代码 (unified_data_loader.py) - 状态处理核心
================================================================================

def process_state_as_enhanced_sequence(self, task_features: np.ndarray,
                                     adj_matrix: np.ndarray,
                                     machine_resources: Dict[str, np.ndarray],
                                     comm_speed: np.ndarray,
                                     memory_status: Optional[np.ndarray] = None) -> np.ndarray:
    """
    处理增强的序列化状态，与复杂版本完全一致

    返回:
        增强的序列化状态 [序列长度, 增强特征维度]
    """
    num_tasks = adj_matrix.shape[0]
    num_machines = len(machine_resources['memory'])

    if memory_status is None:
        memory_status = machine_resources['memory'].copy()

    # 计算前继和后继任务
    predecessors = [[] for _ in range(num_tasks)]
    successors = [[] for _ in range(num_tasks)]

    for i in range(num_tasks):
        for j in range(num_tasks):
            if adj_matrix[i, j] == 1:
                successors[i].append(j)
                predecessors[j].append(i)

    # 归一化处理
    max_cpu = np.max(task_features[0]) if np.max(task_features[0]) > 0 else 1.0
    max_transfer = np.max(task_features[1]) if np.max(task_features[1]) > 0 else 1.0
    max_memory_req = np.max(task_features[4]) if np.max(task_features[4]) > 0 else 1.0
    max_comm = np.max(comm_speed) if np.max(comm_speed) > 0 else 1.0

    normalized_cpu = task_features[0] / max_cpu
    normalized_transfer = task_features[1] / max_transfer
    normalized_memory_req = task_features[4] / max_memory_req
    normalized_comm = comm_speed / max_comm
    normalized_memory = memory_status / np.max(machine_resources['memory'])

    # 归一化机器资源
    normalized_cpu_freq = machine_resources['cpu_freq'] / np.max(machine_resources['cpu_freq'])
    normalized_ttft = machine_resources['ttft'] / np.max(machine_resources['ttft'])
    normalized_tpot = machine_resources['tpot'] / np.max(machine_resources['tpot'])

    # 构建序列状态
    sequence_length = self.config.SEQ_LEN
    feature_dim = self.config.ENHANCED_STATE_DIM
    sequence_state = np.zeros((sequence_length, feature_dim))

    for i in range(min(num_tasks, sequence_length)):
        idx = 0

        # 静态任务特征 (3维)
        sequence_state[i, idx] = normalized_cpu[i]
        sequence_state[i, idx + 1] = normalized_transfer[i]
        sequence_state[i, idx + 2] = task_features[2, i]  # 是否LLM任务
        idx += 3

        # 动态特征 (6维)
        # 任务状态 (4维) - 简化为就绪状态
        sequence_state[i, idx + 1] = 1.0  # 假设任务就绪
        idx += 4

        # 预估输出token数 (1维)
        sequence_state[i, idx] = min(task_features[1, i] / 2000.0, 1.0)
        idx += 1

        # 预估运行时内存 (1维)
        sequence_state[i, idx] = normalized_memory_req[i]
        idx += 1

        # 通信特征 (1维)
        sequence_state[i, idx] = np.mean(normalized_comm)
        idx += 1

        # 内存状态 (num_machines维)
        mem_end = min(idx + num_machines, feature_dim - 1)
        sequence_state[i, idx:mem_end] = normalized_memory[:mem_end-idx]
        idx = mem_end

        # 计算能力 (num_machines维)
        compute_end = min(idx + num_machines, feature_dim - 1)
        sequence_state[i, idx:compute_end] = normalized_cpu_freq[:compute_end-idx]
        idx = compute_end

        # TTFT (num_machines维)
        ttft_end = min(idx + num_machines, feature_dim - 1)
        sequence_state[i, idx:ttft_end] = normalized_ttft[:ttft_end-idx]
        idx = ttft_end

        # TPOT (num_machines维)
        tpot_end = min(idx + num_machines, feature_dim - 1)
        sequence_state[i, idx:tpot_end] = normalized_tpot[:tpot_end-idx]
        idx = tpot_end

        # DAG结构信息 (6维)
        max_preds = min(3, len(predecessors[i]))
        for j in range(3):
            if idx < feature_dim - 1:
                if j < max_preds:
                    sequence_state[i, idx] = predecessors[i][j] / num_tasks
                idx += 1

        max_succs = min(3, len(successors[i]))
        for j in range(3):
            if idx < feature_dim - 1:
                if j < max_succs:
                    sequence_state[i, idx] = successors[i][j] / num_tasks
                idx += 1

        # 有效任务掩码 (1维)
        if idx < feature_dim:
            sequence_state[i, -1] = 1.0

    return sequence_state

================================================================================
6. 关键设计特点总结
================================================================================

### 状态空间设计 (与复杂版本完全一致)
- 总维度: 29-45维 (随设备数动态变化)
- 静态任务特征: 3维 (CPU周期、数据传输、是否LLM)
- 动态特征: 6维 (任务状态4维one-hot + 预测token数1维 + 预测内存1维)
- 机器相关特征: 4×(设备数+1)维 (内存、计算、TTFT、TPOT)
- DAG结构: 6维 (前继3维 + 后继3维)
- 通信和掩码: 2维

### 动作空间设计
- 单动作模式: 只选择机器 (设备数+1维)
- 双动作模式: 选择任务+机器 (任务维度+机器维度)

### SAC算法特点
- 离散动作空间的SAC实现
- 双Critic网络 + 目标网络
- 熵正则化 (alpha=0.2)
- 梯度裁剪 (max_norm=1.0)
- 经验回放缓冲区

### 事件驱动调度
- 真实时间推进机制
- 任务完成事件处理
- 机器状态动态更新
- LLM任务的TTFT+TPOT执行时间计算

### 网络架构支持
- MLP: 直接处理全局状态
- LSTM: 序列建模 + 全局状态融合
- xLSTM: 增强序列建模 + 指数门控

================================================================================
代码检查要点
================================================================================

1. 状态维度是否与复杂版本一致 (29-45维动态变化)
2. 任务状态4维one-hot编码是否正确实现
3. 事件驱动的step函数逻辑是否合理
4. SAC算法的离散动作处理是否正确
5. 双动作模式的动作解析和网络结构是否匹配
6. 序列模型的mask处理是否正确
7. 机器资源的动态更新是否符合预期

================================================================================
文件结束
================================================================================
