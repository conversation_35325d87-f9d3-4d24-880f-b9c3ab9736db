#!/usr/bin/env python3
"""
统一调度系统测试脚本
支持多种算法对比测试
"""

import os
import sys
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
import argparse
from datetime import datetime
from collections import defaultdict

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 添加路径
sys.path.append('.')

from unified_config import UnifiedConfig
from unified_data_loader import UnifiedDataLoader
from unified_environment import UnifiedSchedulingEnvironment
from unified_sac_agent import UnifiedSACAgent

class UnifiedTester:
    """统一测试器"""
    
    def __init__(self, config, model_paths):
        self.config = config
        self.model_paths = model_paths
        
        # 初始化组件
        self.data_loader = UnifiedDataLoader(config)
        self.env = UnifiedSchedulingEnvironment(config, self.data_loader)
        
        # 加载智能体
        self.agents = {}
        for name, path in model_paths.items():
            if os.path.exists(path):
                agent = UnifiedSACAgent(config, device='cuda' if torch.cuda.is_available() else 'cpu')
                agent.load_models(path)
                self.agents[name] = agent
                print(f"[UNIFIED_TESTER] 成功加载模型: {name}")
            else:
                print(f"[UNIFIED_TESTER] 警告: 模型文件不存在: {path}")
        
        # 测试结果
        self.test_results = defaultdict(list)
        
        print(f"[UNIFIED_TESTER] 初始化完成")
        print(f"  加载的模型数: {len(self.agents)}")
        print(f"  测试数据集: {self.data_loader.num_dags} 个DAG")
    
    def test_all_algorithms(self, test_data_path=None, num_test_dags=None):
        """测试所有算法"""
        print("\n" + "="*80)
        print("🧪 开始统一调度系统测试")
        print("="*80)
        
        # 设置测试数据路径
        if test_data_path:
            original_path = self.config.TRAINING_DATA_PATH
            self.config.set_data_path(test_data_path)
            self.data_loader = UnifiedDataLoader(self.config)
            print(f"📁 使用测试数据集: {test_data_path}")
        
        # 确定测试DAG数量
        if num_test_dags is None:
            num_test_dags = min(self.data_loader.num_dags, 200)
        
        print(f"📊 测试配置:")
        print(f"  测试DAG数: {num_test_dags}")
        print(f"  算法数量: {len(self.agents)}")
        print(f"  网络类型: {self.config.NETWORK_TYPE}")
        print(f"  动作模式: {self.config.ACTION_MODE}")
        
        # 测试每个算法
        for agent_name, agent in self.agents.items():
            print(f"\n🔬 测试算法: {agent_name}")
            self._test_single_algorithm(agent, agent_name, num_test_dags)
        
        # 分析和可视化结果
        self._analyze_and_visualize_results()
        
        # 恢复原始数据路径
        if test_data_path:
            self.config.set_data_path(original_path)
        
        return self.test_results
    
    def _test_single_algorithm(self, agent, agent_name, num_test_dags):
        """测试单个算法"""
        results = {
            'completion_rates': [],
            'makespans': [],
            'memory_efficiency': [],
            'execution_times': [],
            'upload_times': [],
            'download_times': [],
            'failed_dags': []
        }
        
        start_time = time.time()
        
        for dag_idx in range(num_test_dags):
            try:
                # 加载数据
                task_features = self.data_loader.load_task_features(dag_idx)
                adj_matrix = self.data_loader.load_adjacency_matrix(dag_idx)
                
                # 重置环境
                state = self.env.reset(task_features, adj_matrix)
                
                step_count = 0
                max_steps = task_features.shape[1] * 3
                
                # 执行测试
                while step_count < max_steps:
                    step_count += 1
                    
                    # 选择动作（确定性）
                    if self.config.ACTION_MODE == "dual":
                        task_action, machine_action = agent.select_action(state, deterministic=True)
                        action = task_action * self.config.MACHINE_ACTION_DIM + machine_action
                    else:
                        action = agent.select_action(state, deterministic=True)
                    
                    # 执行动作
                    next_state, _, done, info = self.env.step(action)
                    state = next_state
                    
                    if done:
                        break
                
                # 记录结果
                completion_rate = info.get('completion_rate', 0.0)
                makespan = info.get('makespan', 1000.0)
                memory_efficiency = info.get('memory_efficiency', 0.0)
                
                results['completion_rates'].append(completion_rate)
                results['makespans'].append(makespan)
                results['memory_efficiency'].append(memory_efficiency)
                
                # 计算时间分解（简化版本）
                execution_time = makespan * 0.7  # 假设70%是执行时间
                upload_time = makespan * 0.15    # 假设15%是上传时间
                download_time = makespan * 0.15  # 假设15%是下载时间
                
                results['execution_times'].append(execution_time)
                results['upload_times'].append(upload_time)
                results['download_times'].append(download_time)
                
                # 记录失败的DAG
                if completion_rate < 1.0:
                    results['failed_dags'].append({
                        'dag_idx': dag_idx,
                        'completion_rate': completion_rate,
                        'failed_tasks': info.get('failed_tasks', 0)
                    })
                
                # 打印进度
                if (dag_idx + 1) % 50 == 0:
                    avg_completion = np.mean(results['completion_rates'])
                    avg_makespan = np.mean([m for m in results['makespans'] if m < 1000])
                    print(f"  进度: {dag_idx+1}/{num_test_dags} | "
                          f"平均完成率: {avg_completion:.1%} | "
                          f"平均Makespan: {avg_makespan:.2f}s")
                
            except Exception as e:
                print(f"  ❌ DAG {dag_idx} 测试失败: {e}")
                # 记录失败
                results['completion_rates'].append(0.0)
                results['makespans'].append(1000.0)
                results['memory_efficiency'].append(0.0)
                results['execution_times'].append(1000.0)
                results['upload_times'].append(0.0)
                results['download_times'].append(0.0)
                results['failed_dags'].append({
                    'dag_idx': dag_idx,
                    'completion_rate': 0.0,
                    'failed_tasks': task_features.shape[1] if 'task_features' in locals() else 30
                })
        
        test_time = time.time() - start_time
        
        # 计算统计信息
        successful_dags = sum(1 for cr in results['completion_rates'] if cr >= 1.0)
        success_rate = successful_dags / num_test_dags
        
        successful_makespans = [m for m, cr in zip(results['makespans'], results['completion_rates']) 
                               if cr >= 1.0 and m < 1000]
        
        print(f"\n📈 {agent_name} 测试结果:")
        print(f"  成功率: {success_rate:.1%} ({successful_dags}/{num_test_dags})")
        print(f"  平均完成率: {np.mean(results['completion_rates']):.1%}")
        
        if successful_makespans:
            print(f"  最佳Makespan: {min(successful_makespans):.2f}s")
            print(f"  平均Makespan: {np.mean(successful_makespans):.2f}s")
            print(f"  Makespan标准差: {np.std(successful_makespans):.2f}s")
            
            # 时间分解
            successful_indices = [i for i, cr in enumerate(results['completion_rates']) if cr >= 1.0]
            if successful_indices:
                avg_execution = np.mean([results['execution_times'][i] for i in successful_indices])
                avg_upload = np.mean([results['upload_times'][i] for i in successful_indices])
                avg_download = np.mean([results['download_times'][i] for i in successful_indices])
                
                print(f"  平均执行时间: {avg_execution:.2f}s")
                print(f"  平均上传时间: {avg_upload:.2f}s")
                print(f"  平均下载时间: {avg_download:.2f}s")
        
        print(f"  平均内存效率: {np.mean(results['memory_efficiency']):.3f}")
        print(f"  失败DAG数: {len(results['failed_dags'])}")
        print(f"  测试时间: {test_time:.1f}s")
        
        # 保存结果
        self.test_results[agent_name] = results
    
    def _analyze_and_visualize_results(self):
        """分析和可视化结果"""
        if not self.test_results:
            print("❌ 没有测试结果可分析")
            return
        
        print(f"\n📊 综合测试结果分析:")
        print("="*80)
        
        # 创建结果表格
        summary_data = []
        for agent_name, results in self.test_results.items():
            successful_makespans = [m for m, cr in zip(results['makespans'], results['completion_rates']) 
                                   if cr >= 1.0 and m < 1000]
            
            summary_data.append({
                '算法': agent_name,
                '成功率': f"{sum(1 for cr in results['completion_rates'] if cr >= 1.0) / len(results['completion_rates']):.1%}",
                '平均完成率': f"{np.mean(results['completion_rates']):.1%}",
                '最佳Makespan': f"{min(successful_makespans):.2f}s" if successful_makespans else "N/A",
                '平均Makespan': f"{np.mean(successful_makespans):.2f}s" if successful_makespans else "N/A",
                'Makespan标准差': f"{np.std(successful_makespans):.2f}s" if successful_makespans else "N/A",
                '平均内存效率': f"{np.mean(results['memory_efficiency']):.3f}",
                '失败DAG数': len(results['failed_dags'])
            })
        
        summary_df = pd.DataFrame(summary_data)
        print(summary_df.to_string(index=False))
        
        # 找出100%完成的DAG
        all_successful_dags = set(range(len(list(self.test_results.values())[0]['completion_rates'])))
        for results in self.test_results.values():
            successful_dags = {i for i, cr in enumerate(results['completion_rates']) if cr >= 1.0}
            all_successful_dags &= successful_dags
        
        print(f"\n🎯 所有算法都100%完成的DAG数: {len(all_successful_dags)}")
        if len(all_successful_dags) > 0:
            print(f"  DAG索引: {sorted(list(all_successful_dags))[:10]}{'...' if len(all_successful_dags) > 10 else ''}")
        
        # 创建可视化
        self._create_comparison_plots()
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_dir = f"test_results_{timestamp}"
        os.makedirs(results_dir, exist_ok=True)
        
        # 保存汇总表格
        summary_df.to_csv(f'{results_dir}/summary.csv', index=False)
        
        # 保存详细结果
        for agent_name, results in self.test_results.items():
            detailed_df = pd.DataFrame({
                'DAG_Index': range(len(results['completion_rates'])),
                'Completion_Rate': results['completion_rates'],
                'Makespan': results['makespans'],
                'Memory_Efficiency': results['memory_efficiency'],
                'Execution_Time': results['execution_times'],
                'Upload_Time': results['upload_times'],
                'Download_Time': results['download_times']
            })
            detailed_df.to_csv(f'{results_dir}/{agent_name}_detailed.csv', index=False)
        
        print(f"\n💾 测试结果已保存到: {results_dir}")
    
    def _create_comparison_plots(self):
        """创建对比图表"""
        if len(self.test_results) < 2:
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('统一调度系统算法对比', fontsize=16, fontweight='bold')
        
        # 完成率对比
        for agent_name, results in self.test_results.items():
            axes[0, 0].plot(results['completion_rates'], label=agent_name, alpha=0.7)
        axes[0, 0].set_title('DAG完成率对比')
        axes[0, 0].set_xlabel('DAG索引')
        axes[0, 0].set_ylabel('完成率')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # Makespan对比（只显示成功的）
        for agent_name, results in self.test_results.items():
            successful_makespans = [m if cr >= 1.0 else np.nan 
                                   for m, cr in zip(results['makespans'], results['completion_rates'])]
            axes[0, 1].plot(successful_makespans, label=agent_name, alpha=0.7, marker='o', markersize=2)
        axes[0, 1].set_title('Makespan对比 (仅成功DAG)')
        axes[0, 1].set_xlabel('DAG索引')
        axes[0, 1].set_ylabel('Makespan (秒)')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 成功率统计
        agent_names = list(self.test_results.keys())
        success_rates = []
        for results in self.test_results.values():
            success_rate = sum(1 for cr in results['completion_rates'] if cr >= 1.0) / len(results['completion_rates'])
            success_rates.append(success_rate)
        
        axes[1, 0].bar(agent_names, success_rates, alpha=0.7)
        axes[1, 0].set_title('算法成功率对比')
        axes[1, 0].set_ylabel('成功率')
        axes[1, 0].set_ylim(0, 1.1)
        for i, v in enumerate(success_rates):
            axes[1, 0].text(i, v + 0.01, f'{v:.1%}', ha='center')
        
        # 平均Makespan对比
        avg_makespans = []
        for results in self.test_results.values():
            successful_makespans = [m for m, cr in zip(results['makespans'], results['completion_rates']) 
                                   if cr >= 1.0 and m < 1000]
            avg_makespan = np.mean(successful_makespans) if successful_makespans else 0
            avg_makespans.append(avg_makespan)
        
        axes[1, 1].bar(agent_names, avg_makespans, alpha=0.7)
        axes[1, 1].set_title('平均Makespan对比')
        axes[1, 1].set_ylabel('平均Makespan (秒)')
        for i, v in enumerate(avg_makespans):
            if v > 0:
                axes[1, 1].text(i, v + max(avg_makespans) * 0.01, f'{v:.1f}s', ha='center')
        
        plt.tight_layout()
        
        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        plt.savefig(f'algorithm_comparison_{timestamp}.png', dpi=300, bbox_inches='tight')
        plt.close()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='统一调度系统测试')
    parser.add_argument('--network_type', type=str, default='mlp', 
                       choices=['mlp', 'lstm', 'xlstm'], help='网络类型')
    parser.add_argument('--action_mode', type=str, default='single', 
                       choices=['single', 'dual'], help='动作模式')
    parser.add_argument('--device_num', type=int, default=5, help='边缘设备数量')
    parser.add_argument('--test_data_path', type=str, 
                       default='../dataset/testing/DAG_30_edges_5_mem_57-64GB_density_0.2_0.6',
                       help='测试数据路径')
    parser.add_argument('--model_dir', type=str, required=True, help='模型目录')
    parser.add_argument('--num_test_dags', type=int, default=200, help='测试DAG数量')

    args = parser.parse_args()

    # 创建配置
    config = UnifiedConfig()
    config.set_network_type(args.network_type)
    config.set_action_mode(args.action_mode)
    config.update_edge_servers(args.device_num)

    # 设置模型路径
    model_paths = {
        f'{args.network_type.upper()}-{args.action_mode.upper()}': f'{args.model_dir}/final/agent.pth'
    }

    print(f"🧪 统一调度系统测试参数:")
    print(f"  网络类型: {args.network_type}")
    print(f"  动作模式: {args.action_mode}")
    print(f"  设备数: {args.device_num}")
    print(f"  测试数据: {args.test_data_path}")
    print(f"  模型目录: {args.model_dir}")
    print(f"  测试DAG数: {args.num_test_dags}")

    # 开始测试
    tester = UnifiedTester(config, model_paths)
    results = tester.test_all_algorithms(
        test_data_path=args.test_data_path,
        num_test_dags=args.num_test_dags
    )

    print(f"\n🎉 测试完成!")

if __name__ == "__main__":
    main()
