#!/usr/bin/env python3
"""
统一数据加载器
支持复杂版本的状态表示和真实DAG数据集
"""

import os
import numpy as np
import pandas as pd
from typing import Tuple, Dict, Any, Optional, List

class UnifiedDataLoader:
    """统一数据加载器"""

    def __init__(self, config):
        self.config = config
        self.data_path = config.TRAINING_DATA_PATH

        # 加载机器资源配置
        self._load_machine_resources()

        # 加载通信速度配置
        self._load_communication_speeds()

        # 数据集信息
        self.num_dags = 200  # 默认DAG数量
        self._check_dataset_size()

        print(f"[UNIFIED_LOADER] 初始化完成")
        print(f"  数据路径: {self.data_path}")
        print(f"  DAG数量: {self.num_dags}")
        print(f"  机器数量: {len(self.machine_memory)}")

    def _load_machine_resources(self):
        """加载机器资源配置"""
        try:
            machine_file = self.config.MACHINES_RESOURCE_PATH
            if os.path.exists(machine_file):
                df = pd.read_excel(machine_file)

                # 提取机器配置
                self.machine_memory = df['Memory(GB)'].values
                self.machine_cpu_freq = df['CPU_Frequency(GHz)'].values
                self.machine_ttft = df['TTFT(s)'].values
                self.machine_tpot = df['TPOT(s)'].values
                self.machine_types = df['Type'].values if 'Type' in df.columns else None

                print(f"[UNIFIED_LOADER] 成功加载机器配置: {len(self.machine_memory)} 台机器")

                # 打印机器类型分布
                if self.machine_types is not None:
                    type_counts = pd.Series(self.machine_types).value_counts()
                    print(f"  机器类型分布: {dict(type_counts)}")

            else:
                print(f"[UNIFIED_LOADER] 警告: 机器配置文件不存在，使用默认配置")
                self._create_default_machine_config()

        except Exception as e:
            print(f"[UNIFIED_LOADER] 错误: 无法加载机器配置: {e}")
            self._create_default_machine_config()

    def _create_default_machine_config(self):
        """创建默认机器配置"""
        num_machines = self.config.NUM_EDGE_SERVERS + 1

        # 用户设备 + 边缘设备
        self.machine_memory = np.array([16.0] + [28.0] * self.config.NUM_EDGE_SERVERS)
        self.machine_cpu_freq = np.array([2.4] + [12.0] * self.config.NUM_EDGE_SERVERS)
        self.machine_ttft = np.array([0.23] + [0.046] * self.config.NUM_EDGE_SERVERS)
        self.machine_tpot = np.array([0.013] + [0.1] * self.config.NUM_EDGE_SERVERS)
        self.machine_types = np.array(['User Device'] + ['Edge Device'] * self.config.NUM_EDGE_SERVERS)

    def _load_communication_speeds(self):
        """加载通信速度配置"""
        try:
            comm_file = self.config.COMMUNICATION_SPEED_PATH
            if os.path.exists(comm_file):
                df = pd.read_excel(comm_file, header=None)
                self.communication_speeds = df.values
                print(f"[UNIFIED_LOADER] 成功加载通信速度配置: {self.communication_speeds.shape}")
            else:
                print(f"[UNIFIED_LOADER] 警告: 通信速度文件不存在，使用默认配置")
                self._create_default_communication_speeds()
        except Exception as e:
            print(f"[UNIFIED_LOADER] 错误: 无法加载通信速度: {e}")
            self._create_default_communication_speeds()

    def _create_default_communication_speeds(self):
        """创建默认通信速度配置"""
        num_machines = len(self.machine_memory)
        self.communication_speeds = np.ones((num_machines, num_machines)) * 100.0  # 100 Mbps
        np.fill_diagonal(self.communication_speeds, 0.0)  # 自己到自己的通信速度为0

    def _check_dataset_size(self):
        """检查数据集大小"""
        try:
            # 检查任务特征文件数量
            task_files = [f for f in os.listdir(self.data_path) if f.startswith('task_feature_') and f.endswith('.npy')]
            self.num_dags = len(task_files)
            print(f"[UNIFIED_LOADER] 检测到 {self.num_dags} 个DAG")
        except Exception as e:
            print(f"[UNIFIED_LOADER] 警告: 无法检测数据集大小: {e}")
            self.num_dags = 200

    def load_task_features(self, dag_idx: int) -> np.ndarray:
        """
        加载任务特征
        返回: [5, num_tasks] 数组
        """
        try:
            file_path = os.path.join(self.data_path, f'task_feature_{dag_idx}.npy')
            task_features = np.load(file_path)

            # 确保形状正确
            if task_features.shape[0] != 5:
                raise ValueError(f"任务特征维度错误: {task_features.shape}")

            return task_features

        except Exception as e:
            print(f"[UNIFIED_LOADER] 错误: 无法加载任务特征 {dag_idx}: {e}")
            # 返回默认任务特征
            return self._create_default_task_features()

    def load_adjacency_matrix(self, dag_idx: int) -> np.ndarray:
        """
        加载邻接矩阵
        返回: [num_tasks, num_tasks] 数组
        """
        try:
            file_path = os.path.join(self.data_path, f'adj_matrix_{dag_idx}.npy')
            adj_matrix = np.load(file_path)
            return adj_matrix

        except Exception as e:
            print(f"[UNIFIED_LOADER] 错误: 无法加载邻接矩阵 {dag_idx}: {e}")
            # 返回默认邻接矩阵
            return self._create_default_adjacency_matrix()

    def _create_default_task_features(self) -> np.ndarray:
        """创建默认任务特征"""
        num_tasks = 30
        task_features = np.zeros((5, num_tasks))

        # 内存需求 (GB)
        task_features[0] = np.random.uniform(1.0, 15.0, num_tasks)
        # 预测输出token数
        task_features[1] = np.random.randint(0, 2000, num_tasks)
        # 是否LLM任务
        task_features[2] = np.random.choice([0, 1], num_tasks, p=[0.6, 0.4])
        # 计算强度
        task_features[3] = np.random.uniform(0.1, 1.0, num_tasks)
        # 数据大小
        task_features[4] = np.random.uniform(0.1, 1.0, num_tasks)

        return task_features

    def _create_default_adjacency_matrix(self) -> np.ndarray:
        """创建默认邻接矩阵"""
        num_tasks = 30
        adj_matrix = np.zeros((num_tasks, num_tasks))

        # 创建简单的链式依赖
        for i in range(num_tasks - 1):
            adj_matrix[i, i + 1] = 1

        return adj_matrix

    def get_machine_resources(self) -> Dict[str, np.ndarray]:
        """获取机器资源信息"""
        return {
            'memory': self.machine_memory,
            'cpu_freq': self.machine_cpu_freq,
            'ttft': self.machine_ttft,
            'tpot': self.machine_tpot,
            'types': self.machine_types
        }

    def get_communication_speeds(self) -> np.ndarray:
        """获取通信速度矩阵"""
        return self.communication_speeds

    def process_state_as_enhanced_sequence(self, task_features: np.ndarray,
                                         adj_matrix: np.ndarray,
                                         machine_resources: Dict[str, np.ndarray],
                                         comm_speed: np.ndarray,
                                         task_status_list: List[str],
                                         memory_status: Optional[np.ndarray] = None) -> Tuple[np.ndarray, np.ndarray]:
        """
        处理增强的序列化状态，与复杂版本完全一致

        参数:
            task_features: 任务特征 [5, num_tasks] (包含预估token数和内存)
            adj_matrix: 邻接矩阵
            machine_resources: 机器资源
            comm_speed: 通信速度
            task_status_list: 每个任务的当前状态字符串列表
            memory_status: 当前内存状态

        返回:
            enhanced_state: 增强的序列化状态 [序列长度, 增强特征维度]
            ready_mask: 就绪任务掩码 [序列长度]
        """
        num_tasks = adj_matrix.shape[0]
        num_machines = len(machine_resources['memory'])

        if memory_status is None:
            memory_status = machine_resources['memory'].copy()

        # 计算前继和后继任务
        predecessors = [[] for _ in range(num_tasks)]
        successors = [[] for _ in range(num_tasks)]

        for i in range(num_tasks):
            for j in range(num_tasks):
                if adj_matrix[i, j] == 1:
                    successors[i].append(j)
                    predecessors[j].append(i)

        # 归一化处理
        max_cpu = np.max(task_features[0]) if np.max(task_features[0]) > 0 else 1.0
        max_transfer = np.max(task_features[1]) if np.max(task_features[1]) > 0 else 1.0
        max_memory_req = np.max(task_features[4]) if np.max(task_features[4]) > 0 else 1.0
        max_comm = np.max(comm_speed) if np.max(comm_speed) > 0 else 1.0

        normalized_cpu = task_features[0] / max_cpu
        normalized_transfer = task_features[1] / max_transfer
        normalized_memory_req = task_features[4] / max_memory_req
        normalized_comm = comm_speed / max_comm
        normalized_memory = memory_status / np.max(machine_resources['memory'])

        # 归一化机器资源
        normalized_cpu_freq = machine_resources['cpu_freq'] / np.max(machine_resources['cpu_freq'])
        normalized_ttft = machine_resources['ttft'] / np.max(machine_resources['ttft'])
        normalized_tpot = machine_resources['tpot'] / np.max(machine_resources['tpot'])

        # 构建序列状态和就绪掩码
        sequence_length = self.config.SEQ_LEN
        feature_dim = self.config.ENHANCED_STATE_DIM
        sequence_state = np.zeros((sequence_length, feature_dim))
        ready_mask = np.zeros(sequence_length)  # 就绪任务掩码

        for i in range(min(num_tasks, sequence_length)):
            idx = 0

            # 静态任务特征 (3维)
            sequence_state[i, idx] = normalized_cpu[i]
            sequence_state[i, idx + 1] = normalized_transfer[i]
            sequence_state[i, idx + 2] = task_features[2, i]  # 是否LLM任务
            idx += 3

            # 动态特征 (6维)
            # 任务状态 (4维) - 从真实状态获取
            current_status = task_status_list[i] if i < len(task_status_list) else "未就绪/等待输入"
            status_index = self.config.get_task_status_index(current_status)
            status_onehot = self.config.create_task_status_onehot(status_index)
            sequence_state[i, idx:idx+4] = status_onehot
            idx += 4

            # 更新就绪掩码
            if current_status == "信息完整/就绪可调度":
                ready_mask[i] = 1.0

            # 预估输出token数 (1维) - 从task_features[1]读取预估值
            sequence_state[i, idx] = min(task_features[1, i] / 2000.0, 1.0)
            idx += 1

            # 预估运行时内存 (1维) - 从task_features[4]读取预估值
            sequence_state[i, idx] = normalized_memory_req[i]
            idx += 1

            # 通信特征 (1维)
            sequence_state[i, idx] = np.mean(normalized_comm)
            idx += 1

            # 内存状态 (num_machines维)
            mem_end = min(idx + num_machines, feature_dim - 1)
            sequence_state[i, idx:mem_end] = normalized_memory[:mem_end-idx]
            idx = mem_end

            # 计算能力 (num_machines维)
            compute_end = min(idx + num_machines, feature_dim - 1)
            sequence_state[i, idx:compute_end] = normalized_cpu_freq[:compute_end-idx]
            idx = compute_end

            # TTFT (num_machines维)
            ttft_end = min(idx + num_machines, feature_dim - 1)
            sequence_state[i, idx:ttft_end] = normalized_ttft[:ttft_end-idx]
            idx = ttft_end

            # TPOT (num_machines维)
            tpot_end = min(idx + num_machines, feature_dim - 1)
            sequence_state[i, idx:tpot_end] = normalized_tpot[:tpot_end-idx]
            idx = tpot_end

            # DAG结构信息 (6维)
            max_preds = min(3, len(predecessors[i]))
            for j in range(3):
                if idx < feature_dim - 1:
                    if j < max_preds:
                        sequence_state[i, idx] = predecessors[i][j] / num_tasks
                    idx += 1

            max_succs = min(3, len(successors[i]))
            for j in range(3):
                if idx < feature_dim - 1:
                    if j < max_succs:
                        sequence_state[i, idx] = successors[i][j] / num_tasks
                    idx += 1

            # 有效任务掩码 (1维)
            if idx < feature_dim:
                sequence_state[i, -1] = 1.0

        return sequence_state, ready_mask

    def normalize_features(self, features: np.ndarray) -> np.ndarray:
        """归一化特征"""
        normalized = features.copy()

        # 内存需求归一化 (0-50GB)
        normalized[0] = np.clip(normalized[0] / 50.0, 0, 1)

        # Token数归一化 (0-2000)
        normalized[1] = np.clip(normalized[1] / 2000.0, 0, 1)

        # LLM任务标志保持不变 (0/1)
        # normalized[2] = normalized[2]

        # 计算强度保持不变 (0-1)
        # normalized[3] = normalized[3]

        # 数据大小保持不变 (0-1)
        # normalized[4] = normalized[4]

        return normalized

    def get_dataset_info(self) -> Dict[str, Any]:
        """获取数据集信息"""
        return {
            'num_dags': self.num_dags,
            'data_path': self.data_path,
            'num_machines': len(self.machine_memory),
            'machine_types': dict(pd.Series(self.machine_types).value_counts()) if self.machine_types is not None else None
        }
