# 🎯 统一调度系统训练和测试指南

## 📋 系统概述

统一调度系统结合了复杂版本的状态空间和简化版本的事件驱动机制，支持多种配置：

### ✅ 支持的配置
- **网络架构**: MLP、LSTM、xLSTM
- **动作模式**: 单动作（只选机器）、双动作（选任务+机器）
- **设备数量**: 2、3、4、5、6台边缘设备
- **状态维度**: 动态变化（27-45维）

### 📊 状态空间详解

**状态维度计算公式**:
```
总维度 = 3 + 6 + 1 + (设备数+1) + (设备数+1) + (设备数+1) + (设备数+1) + 6 + 1
       = 18 + 4×(设备数+1)
```

**各设备数对应的状态维度**:
- 2台设备: 30维
- 3台设备: 34维  
- 4台设备: 38维
- 5台设备: 42维
- 6台设备: 46维

**状态组成详解**:
```
1. 静态任务特征 (3维)
   - CPU周期需求
   - 数据传输量
   - 是否LLM任务

2. 动态特征 (6维)
   - 任务状态 (4维 one-hot编码)
     * 未就绪/等待输入
     * 信息完整/就绪可调度
     * 运行中
     * 已完成/失败
   - 预测输出token数 (1维，归一化)
   - 预测运行时内存 (1维，归一化)

3. 通信特征 (1维)
   - 平均通信速度

4. 内存状态 (设备数+1维)
   - 各设备的可用内存

5. 计算能力 (设备数+1维)
   - 各设备的CPU频率

6. TTFT (设备数+1维)
   - 各设备的首token时间

7. TPOT (设备数+1维)
   - 各设备的每token时间

8. DAG结构 (6维)
   - 前继任务信息 (3维)
   - 后继任务信息 (3维)

9. 掩码 (1维)
   - 有效任务标识
```

## 🚀 单独训练

### 1. MLP单动作模式
```bash
python3 train_unified.py \
    --episodes 1000 \
    --network_type mlp \
    --action_mode single \
    --device_num 5 \
    --data_path ../enhanced_llm_scheduling/dataset/training/DAG_30_layered_fat0.4_density0.5_5devices \
    --save_interval 250
```

### 2. MLP双动作模式
```bash
python3 train_unified.py \
    --episodes 1000 \
    --network_type mlp \
    --action_mode dual \
    --device_num 5 \
    --data_path ../enhanced_llm_scheduling/dataset/training/DAG_30_layered_fat0.4_density0.5_5devices \
    --save_interval 250
```

### 3. LSTM单动作模式
```bash
python3 train_unified.py \
    --episodes 1200 \
    --network_type lstm \
    --action_mode single \
    --device_num 5 \
    --data_path ../enhanced_llm_scheduling/dataset/training/DAG_30_layered_fat0.4_density0.5_5devices \
    --save_interval 300
```

### 4. xLSTM双动作模式
```bash
python3 train_unified.py \
    --episodes 1500 \
    --network_type xlstm \
    --action_mode dual \
    --device_num 5 \
    --data_path ../enhanced_llm_scheduling/dataset/training/DAG_30_layered_fat0.4_density0.5_5devices \
    --save_interval 375
```

### 5. 不同设备数训练
```bash
# 2台设备
python3 train_unified.py --episodes 800 --network_type mlp --action_mode single --device_num 2

# 3台设备  
python3 train_unified.py --episodes 800 --network_type mlp --action_mode single --device_num 3

# 4台设备
python3 train_unified.py --episodes 800 --network_type mlp --action_mode single --device_num 4

# 6台设备
python3 train_unified.py --episodes 800 --network_type mlp --action_mode single --device_num 6
```

## 🔄 批量训练

### 1. 运行预定义的批量训练
```bash
python3 batch_train.py
```

### 2. 自定义批量训练
编辑 `batch_train.py` 中的 `configurations` 列表来自定义训练配置。

## 🧪 测试

### 1. 单模型测试
```bash
python3 test_unified.py \
    --network_type mlp \
    --action_mode single \
    --device_num 5 \
    --test_data_path ../dataset/testing/DAG_30_edges_5_mem_57-64GB_density_0.2_0.6 \
    --model_dir models/unified_mlp_single_20250529_161948 \
    --num_test_dags 200
```

### 2. 多模型对比测试
```bash
# 创建测试脚本来对比多个模型
python3 -c "
from test_unified import UnifiedTester
from unified_config import UnifiedConfig

config = UnifiedConfig()
config.set_network_type('mlp')
config.set_action_mode('single')
config.update_edge_servers(5)

model_paths = {
    'MLP-Single': 'models/unified_mlp_single_xxx/final/agent.pth',
    'MLP-Dual': 'models/unified_mlp_dual_xxx/final/agent.pth',
    'LSTM-Single': 'models/unified_lstm_single_xxx/final/agent.pth',
    'xLSTM-Dual': 'models/unified_xlstm_dual_xxx/final/agent.pth'
}

tester = UnifiedTester(config, model_paths)
results = tester.test_all_algorithms()
"
```

## ⚙️ 动作模式切换

### 单动作模式 (Single Action)
- **特点**: 只选择执行机器，任务选择固定为第一个就绪任务
- **动作空间**: `设备数+1` 维（包含用户设备）
- **适用场景**: 简化决策，专注于机器选择优化
- **参数**: `--action_mode single`

### 双动作模式 (Dual Action)  
- **特点**: 同时选择任务和执行机器
- **动作空间**: `任务选择维度 + 机器选择维度`
- **适用场景**: 完整的调度决策，任务和机器联合优化
- **参数**: `--action_mode dual`

## 📁 数据集要求

### 训练数据集结构
```
dataset/training/DAG_30_layered_fat0.4_density0.5_5devices/
├── task_feature_0.npy          # 任务特征文件
├── task_feature_1.npy
├── ...
├── adj_matrix_0.npy            # 邻接矩阵文件
├── adj_matrix_1.npy
├── ...
├── machines_resource1.xlsx     # 机器资源配置
└── communication_speed.xlsx    # 通信速度矩阵
```

### 测试数据集结构
```
dataset/testing/DAG_30_edges_5_mem_57-64GB_density_0.2_0.6/
├── task_feature_0.npy
├── adj_matrix_0.npy
├── ...
└── machines_resource1.xlsx
```

## 📊 训练监控

### 关键指标
- **完成率**: DAG任务完成百分比
- **Makespan**: 总执行时间
- **内存效率**: 内存利用率
- **成功率**: 100%完成的DAG比例

### 训练输出示例
```
Episode  100 | DAG 15 | 完成率: 85.0% (平均: 78.5%) | 
Makespan: 1234.5s (平均: 1456.7s) | 奖励: 2.50 (平均: 1.85) | 成功率: 65.0%
```

## 🎯 最佳实践

### 1. 训练建议
- **MLP**: 适合快速训练和基线对比
- **LSTM**: 适合序列建模，需要更多训练回合
- **xLSTM**: 最新架构，可能需要调参

### 2. 设备数选择
- **2-3台**: 适合快速验证
- **4-5台**: 平衡复杂度和性能
- **6台**: 最大复杂度，需要更多训练

### 3. 动作模式选择
- **单动作**: 简化学习，收敛更快
- **双动作**: 完整决策，性能可能更好

### 4. 超参数调优
- 学习率: 3e-4 (默认)
- 批大小: 64 (默认)
- 熵系数: 0.2 (默认)
- 更新频率: 每步更新

## 🔧 故障排除

### 常见问题
1. **状态维度不匹配**: 检查设备数配置
2. **数据集路径错误**: 确认数据集存在
3. **内存不足**: 减少批大小或序列长度
4. **训练不收敛**: 调整学习率或网络架构

### 调试命令
```bash
# 检查状态维度
python3 test_dimensions.py

# 验证数据加载
python3 -c "
from unified_data_loader import UnifiedDataLoader
from unified_config import UnifiedConfig
config = UnifiedConfig()
config.update_edge_servers(5)
loader = UnifiedDataLoader(config)
print('数据集信息:', loader.get_dataset_info())
"
```
