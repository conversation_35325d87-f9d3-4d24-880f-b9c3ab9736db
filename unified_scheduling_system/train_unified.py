#!/usr/bin/env python3
"""
统一调度系统训练脚本
支持MLP、LSTM、xLSTM和双动作/单动作模式
"""

import os
import sys
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
import argparse
import random
from datetime import datetime
from collections import defaultdict, deque

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 添加路径
sys.path.append('.')

from unified_config import UnifiedConfig
from unified_data_loader import UnifiedDataLoader
from unified_environment import UnifiedSchedulingEnvironment
from unified_sac_agent import UnifiedSACAgent

class UnifiedTrainer:
    """统一训练器"""

    def __init__(self, config):
        self.config = config

        # 初始化组件
        self.data_loader = UnifiedDataLoader(config)
        self.env = UnifiedSchedulingEnvironment(config, self.data_loader)
        self.agent = UnifiedSACAgent(config, device='cuda' if torch.cuda.is_available() else 'cpu')

        # 训练统计
        self.episode_rewards = []
        self.episode_completion_rates = []
        self.episode_makespans = []
        self.episode_memory_efficiency = []
        self.failed_dags = []

        print(f"[UNIFIED_TRAINER] 初始化完成")
        print(f"  数据集: {self.data_loader.num_dags} 个DAG")
        print(f"  机器数: {len(self.data_loader.machine_memory)}")
        print(f"  网络类型: {config.NETWORK_TYPE}")
        print(f"  动作模式: {config.ACTION_MODE}")

    def train(self, num_episodes=1000, save_interval=250):
        """训练主循环"""
        print("\n" + "="*80)
        print("🚀 开始统一调度系统训练")
        print("="*80)

        # 打印配置
        self.config.print_config()

        # 创建保存目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_dir = f"models/unified_{self.config.NETWORK_TYPE}_{self.config.ACTION_MODE}_{timestamp}"
        os.makedirs(model_dir, exist_ok=True)

        start_time = time.time()
        successful_episodes = 0

        for episode in range(num_episodes):
            try:
                # 随机选择DAG
                dag_idx = np.random.randint(0, self.data_loader.num_dags)

                # 加载数据
                task_features = self.data_loader.load_task_features(dag_idx)
                adj_matrix = self.data_loader.load_adjacency_matrix(dag_idx)

                # 重置环境
                state = self.env.reset(task_features, adj_matrix)

                episode_reward = 0
                step_count = 0
                max_steps = task_features.shape[1] * 3  # 最大步数

                # 执行episode
                while step_count < max_steps:
                    step_count += 1

                    # 选择动作
                    if self.config.ACTION_MODE == "dual":
                        task_action, machine_action = self.agent.select_action(state, deterministic=False)
                        # 将双动作编码为单一动作索引
                        action = task_action * self.config.MACHINE_ACTION_DIM + machine_action
                    else:
                        action = self.agent.select_action(state, deterministic=False)

                    # 执行动作
                    next_state, reward, done, info = self.env.step(action)

                    # 存储经验
                    if self.config.ACTION_MODE == "dual":
                        stored_action = (task_action, machine_action)
                    else:
                        stored_action = action

                    self.agent.store_transition(state, stored_action, reward, next_state, done)

                    # 更新状态和奖励
                    state = next_state
                    episode_reward += reward

                    # 训练智能体
                    if len(self.agent.replay_buffer) > self.agent.batch_size:
                        self.agent.update()

                    if done:
                        break

                # 记录结果
                completion_rate = info.get('completion_rate', 0.0)
                makespan = info.get('makespan', 1000.0)
                memory_efficiency = info.get('memory_efficiency', 0.0)

                self.episode_rewards.append(episode_reward)
                self.episode_completion_rates.append(completion_rate)
                self.episode_makespans.append(makespan)
                self.episode_memory_efficiency.append(memory_efficiency)

                # 记录失败的DAG
                if completion_rate < 1.0:
                    self.failed_dags.append({
                        'episode': episode,
                        'dag_idx': dag_idx,
                        'completion_rate': completion_rate,
                        'makespan': makespan,
                        'failed_tasks': info.get('failed_tasks', 0)
                    })
                else:
                    successful_episodes += 1

                # 打印进度
                if (episode + 1) % 20 == 0 or episode < 10:
                    recent_completion = np.mean(self.episode_completion_rates[-20:])
                    # 修复makespan计算：只计算成功DAG的makespan
                    recent_successful_makespans = [m for m, cr in zip(self.episode_makespans[-20:], self.episode_completion_rates[-20:])
                                                 if cr >= 1.0 and m < 1000]
                    recent_makespan = np.mean(recent_successful_makespans) if recent_successful_makespans else 0.0
                    recent_reward = np.mean(self.episode_rewards[-20:])
                    success_rate = successful_episodes / (episode + 1)

                    # 获取训练统计
                    training_stats = self.agent.get_training_stats()

                    print(f"Episode {episode+1:4d} | "
                          f"DAG {dag_idx:2d} | "
                          f"完成率: {completion_rate:.1%} (平均: {recent_completion:.1%}) | "
                          f"Makespan: {makespan:6.1f}s (平均: {recent_makespan:6.1f}s) | "
                          f"奖励: {episode_reward:6.2f} (平均: {recent_reward:6.2f}) | "
                          f"成功率: {success_rate:.1%}")

                    if (episode + 1) % 100 == 0:
                        print(f"  训练统计: Actor损失={training_stats['avg_actor_loss']:.4f}, "
                              f"Critic损失={training_stats['avg_critic_loss']:.4f}, "
                              f"更新次数={training_stats['update_count']}")

                # 保存检查点
                if (episode + 1) % save_interval == 0:
                    checkpoint_dir = f"{model_dir}/checkpoint_{episode+1}"
                    os.makedirs(checkpoint_dir, exist_ok=True)
                    self.agent.save_models(f"{checkpoint_dir}/agent.pth")

                    # 保存训练统计
                    self._save_training_stats(checkpoint_dir, episode + 1)

                    print(f"💾 已保存检查点: {checkpoint_dir}")

            except Exception as e:
                print(f"❌ Episode {episode} 出错: {e}")
                continue

        # 保存最终模型
        final_dir = f"{model_dir}/final"
        os.makedirs(final_dir, exist_ok=True)
        self.agent.save_models(f"{final_dir}/agent.pth")
        self._save_training_stats(final_dir, num_episodes)

        # 分析和可视化结果
        self._analyze_and_visualize_results(model_dir, num_episodes)

        training_time = time.time() - start_time

        return {
            'model_dir': model_dir,
            'success_rate': successful_episodes / num_episodes,
            'avg_completion_rate': np.mean(self.episode_completion_rates),
            'avg_makespan': np.mean([m for m in self.episode_makespans if m < 1000]),
            'training_time': training_time,
            'failed_dags': len(self.failed_dags)
        }

    def _save_training_stats(self, save_dir, episode_count):
        """保存训练统计"""
        stats_df = pd.DataFrame({
            'Episode': range(len(self.episode_rewards)),
            'Reward': self.episode_rewards,
            'Completion_Rate': self.episode_completion_rates,
            'Makespan': self.episode_makespans,
            'Memory_Efficiency': self.episode_memory_efficiency
        })
        stats_df.to_csv(f'{save_dir}/training_stats.csv', index=False)

        # 保存失败DAG信息
        if self.failed_dags:
            failed_df = pd.DataFrame(self.failed_dags)
            failed_df.to_csv(f'{save_dir}/failed_dags.csv', index=False)

    def _analyze_and_visualize_results(self, model_dir, num_episodes):
        """分析和可视化结果"""
        print(f"\n📈 训练完成分析:")

        # 基本统计
        successful_episodes = sum(1 for cr in self.episode_completion_rates if cr >= 1.0)
        success_rate = successful_episodes / num_episodes

        print(f"  总回合数: {num_episodes}")
        print(f"  成功回合: {successful_episodes}")
        print(f"  成功率: {success_rate:.1%}")
        print(f"  失败DAG数: {len(self.failed_dags)}")

        # 性能统计
        successful_makespans = [m for m, cr in zip(self.episode_makespans, self.episode_completion_rates)
                               if cr >= 1.0 and m < 1000]
        if successful_makespans:
            print(f"  最佳Makespan: {min(successful_makespans):.2f}秒")
            print(f"  平均Makespan: {np.mean(successful_makespans):.2f}秒")
            print(f"  Makespan标准差: {np.std(successful_makespans):.2f}秒")

        avg_memory_efficiency = np.mean(self.episode_memory_efficiency)
        print(f"  平均内存效率: {avg_memory_efficiency:.3f}")

        # 创建可视化
        self._create_training_plots(model_dir)

        print(f"\n💾 结果已保存到: {model_dir}")
        print(f"🎉 统一调度系统训练完成!")

    def _create_training_plots(self, model_dir):
        """创建训练图表"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle(f'统一调度系统训练结果 ({self.config.NETWORK_TYPE.upper()}-{self.config.ACTION_MODE.upper()})',
                     fontsize=16, fontweight='bold')

        episodes = range(len(self.episode_rewards))

        # 奖励曲线
        axes[0, 0].plot(episodes, self.episode_rewards, alpha=0.6, color='blue')
        # 移动平均
        if len(self.episode_rewards) > 50:
            moving_avg = pd.Series(self.episode_rewards).rolling(50).mean()
            axes[0, 0].plot(episodes, moving_avg, color='red', linewidth=2, label='移动平均(50)')
        axes[0, 0].set_title('训练奖励')
        axes[0, 0].set_xlabel('回合')
        axes[0, 0].set_ylabel('奖励')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # 完成率曲线
        axes[0, 1].plot(episodes, self.episode_completion_rates, alpha=0.6, color='green')
        if len(self.episode_completion_rates) > 50:
            moving_avg = pd.Series(self.episode_completion_rates).rolling(50).mean()
            axes[0, 1].plot(episodes, moving_avg, color='red', linewidth=2, label='移动平均(50)')
        axes[0, 1].set_title('DAG完成率')
        axes[0, 1].set_xlabel('回合')
        axes[0, 1].set_ylabel('完成率')
        axes[0, 1].set_ylim(0, 1.1)
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)

        # Makespan曲线（只显示成功的）
        successful_makespans = [m if cr >= 1.0 else np.nan for m, cr in zip(self.episode_makespans, self.episode_completion_rates)]
        axes[1, 0].plot(episodes, successful_makespans, alpha=0.6, color='orange', marker='o', markersize=2)
        axes[1, 0].set_title('Makespan (仅成功DAG)')
        axes[1, 0].set_xlabel('回合')
        axes[1, 0].set_ylabel('Makespan (秒)')
        axes[1, 0].grid(True, alpha=0.3)

        # 内存效率曲线
        axes[1, 1].plot(episodes, self.episode_memory_efficiency, alpha=0.6, color='purple')
        if len(self.episode_memory_efficiency) > 50:
            moving_avg = pd.Series(self.episode_memory_efficiency).rolling(50).mean()
            axes[1, 1].plot(episodes, moving_avg, color='red', linewidth=2, label='移动平均(50)')
        axes[1, 1].set_title('内存利用效率')
        axes[1, 1].set_xlabel('回合')
        axes[1, 1].set_ylabel('效率')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f'{model_dir}/training_results.png', dpi=300, bbox_inches='tight')
        plt.close()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='统一调度系统训练')
    parser.add_argument('--episodes', type=int, default=1000, help='训练回合数')
    parser.add_argument('--network_type', type=str, default='mlp',
                       choices=['mlp', 'lstm', 'xlstm'], help='网络类型')
    parser.add_argument('--action_mode', type=str, default='single',
                       choices=['single', 'dual'], help='动作模式')
    parser.add_argument('--device_num', type=int, default=5, help='边缘设备数量')
    parser.add_argument('--data_path', type=str,
                       default='../enhanced_llm_scheduling/dataset/training/DAG_30_layered_fat0.4_density0.5_5devices',
                       help='训练数据路径')
    parser.add_argument('--save_interval', type=int, default=250, help='保存间隔')

    args = parser.parse_args()

    # 创建配置
    config = UnifiedConfig()
    config.set_network_type(args.network_type)
    config.set_action_mode(args.action_mode)
    config.update_edge_servers(args.device_num)
    config.set_data_path(args.data_path)

    print(f"🎯 统一调度系统训练参数:")
    print(f"  回合数: {args.episodes}")
    print(f"  网络类型: {args.network_type}")
    print(f"  动作模式: {args.action_mode}")
    print(f"  设备数: {args.device_num}")
    print(f"  数据路径: {args.data_path}")

    # 开始训练
    trainer = UnifiedTrainer(config)
    results = trainer.train(
        num_episodes=args.episodes,
        save_interval=args.save_interval
    )

    print(f"\n🎉 训练完成!")
    print(f"  成功率: {results['success_rate']:.1%}")
    print(f"  平均完成率: {results['avg_completion_rate']:.1%}")
    print(f"  平均Makespan: {results['avg_makespan']:.2f}秒")
    print(f"  训练时间: {results['training_time']:.1f}秒")
    print(f"  模型保存路径: {results['model_dir']}")

if __name__ == "__main__":
    main()
