#!/usr/bin/env python3
"""
测试统一调度系统的状态维度
"""

from unified_config import UnifiedConfig

def test_state_dimensions():
    """测试不同设备数的状态维度"""
    print("🔍 测试统一调度系统状态维度")
    print("="*50)
    
    for num_devices in [2, 3, 4, 5, 6]:
        config = UnifiedConfig()
        config.update_edge_servers(num_devices)
        
        expected_dim = (
            config.STATIC_TASK_DIM +      # 3
            config.DYNAMIC_FEATURES_DIM + # 6  
            config.STATE_COMM_DIM +       # 1
            config.STATE_MEM_DIM +        # num_devices + 1
            config.STATE_COMPUTE_DIM +    # num_devices + 1
            config.STATE_TTFT_DIM +       # num_devices + 1
            config.STATE_TPOT_DIM +       # num_devices + 1
            config.STATE_DAG_DIM +        # 6
            1                             # 1
        )
        
        print(f"{num_devices}台设备:")
        print(f"  计算维度: 3+6+1+{num_devices+1}+{num_devices+1}+{num_devices+1}+{num_devices+1}+6+1 = {expected_dim}")
        print(f"  实际维度: {config.ENHANCED_STATE_DIM}")
        print(f"  匹配: {'✅' if config.ENHANCED_STATE_DIM == expected_dim else '❌'}")
        print()

if __name__ == "__main__":
    test_state_dimensions()
