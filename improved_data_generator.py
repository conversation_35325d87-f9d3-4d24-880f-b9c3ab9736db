#!/usr/bin/env python3
"""
改进的数据生成器
融合别人的分层DAG生成算法 + 我们的任务特征 + 离散机器类型
"""

import numpy as np
import pandas as pd
import os
import networkx as nx
import matplotlib.pyplot as plt
import argparse
from pathlib import Path
import random
from typing import List, Tuple

class ImprovedDataGenerator:
    def __init__(self, config=None):
        """
        改进的数据生成器

        主要改进：
        1. 使用别人的分层DAG生成算法
        2. 保持我们的任务特征生成
        3. 引入离散的机器类型
        """
        # 基本参数设置
        self.config = {
            # 任务设置
            'num_tasks': 30,
            'num_datasets': 200,
            'llm_ratio': 0.4,

            # DAG分层生成参数 (来自别人的算法)
            'dag_fat': 0.4,              # 层宽度因子
            'dag_density': 0.5,          # 额外边密度
            'dag_layers': 'auto',        # 自动计算层数或指定层数

            # 任务特征范围 (保持我们的设计)
            'memory_req_range': {
                'normal': (0.5, 15.0),     # 普通任务内存需求范围 (GB)
                'llm': (12.0, 20.0)        # LLM任务内存需求范围 (GB)
            },
            'token_count_range': (500, 2000),  # LLM模型token数量范围
            'compute_intensity_range': (10, 100),  # 计算强度
            'data_size_range': (3, 100),          # 数据大小

            # 离散机器类型设计
            'machine_types': {
                'type_A': {  # 高性能机器
                    'memory': 48.0,
                    'cpu_freq': 14.0,
                    'ttft': 0.016,      # Time To First Token
                    'tpot': 0.1,        # Time Per Output Token
                    'description': 'High Performance'
                },
                'type_B': {  # 中等性能机器
                    'memory': 32.0,
                    'cpu_freq': 12.0,
                    'ttft': 0.045,
                    'tpot': 0.12,
                    'description': 'Medium Performance'
                },
                'type_C': {  # 低性能机器
                    'memory': 24.0,
                    'cpu_freq': 10.0,
                    'ttft': 0.075,
                    'tpot': 0.15,
                    'description': 'Low Performance'
                }
            },

            # 用户设备 (固定)
            'user_device': {
                'memory': 16.0,
                'cpu_freq': 2.4,
                'ttft': 0.73,
                'tpot': 0.013,
                'description': 'User Device'
            },

            # 系统设置
            'num_edge_servers': 5,
            'output_dir': 'dataset/training'
        }

        # 更新配置
        if config:
            self.config.update(config)

        # 创建输出目录
        os.makedirs(self.config['output_dir'], exist_ok=True)
        self._create_output_directory()

    def _create_output_directory(self):
        """创建输出目录"""
        num_tasks = self.config['num_tasks']
        num_edge_servers = self.config['num_edge_servers']
        dag_fat = self.config['dag_fat']
        dag_density = self.config['dag_density']

        # 创建目录名
        dir_name = f"DAG_{num_tasks}_layered_fat{dag_fat}_density{dag_density}_{num_edge_servers}devices"

        self.config['full_output_dir'] = os.path.join(self.config['output_dir'], dir_name)
        os.makedirs(self.config['full_output_dir'], exist_ok=True)
        print(f"数据将保存到目录: {self.config['full_output_dir']}")

    def daggen_layered(self, n: int, fat: float, density: float = 0.0, seed: int = None) -> np.ndarray:
        """
        分层DAG生成算法 (来自别人的实现)

        参数:
            n: 节点数量
            fat: 层宽度因子 (0-1)
            density: 额外边密度
            seed: 随机种子
        """
        if seed is not None:
            np.random.seed(seed)
            random.seed(seed)

        dag = np.zeros((n, n), dtype=int)
        layers = []
        total = 0

        # 生成分层结构
        while total < n:
            layer_size = int(fat * n * random.random())
            layer_size = max(1, layer_size)
            if total + layer_size > n:
                layer_size = n - total
            layer = list(range(total, total + layer_size))
            layers.append(layer)
            total += layer_size

        # 在相邻层之间添加边
        for i in range(len(layers) - 1):
            src_layer = layers[i]
            dst_layer = layers[i + 1]
            for src in src_layer:
                out_deg = random.randint(1, len(dst_layer))
                selected = random.sample(dst_layer, out_deg)
                for dst in selected:
                    dag[src][dst] = 1

        # 添加额外的边以增加密度
        if density > 0:
            max_edges = n * (n - 1) // 2
            target_edges = int(density * max_edges)
            current_edges = int(np.sum(dag))

            attempts = 0
            max_attempts = target_edges * 10

            while current_edges < target_edges and attempts < max_attempts:
                u = random.randint(0, n - 2)
                v = random.randint(u + 1, n - 1)
                if dag[u][v] == 0:
                    dag[u][v] = 1
                    current_edges += 1
                attempts += 1

        return dag

    def generate_task_features(self, num_tasks):
        """
        生成任务特征 (与原始data_generator.py兼容)

        返回:
            task_features: [5, num_tasks] 数组
                - [0]: CPU周期 (对应原始的Cpu_Cycles)
                - [1]: 数据传输大小 (对应原始的Data_Transfer_MB)
                - [2]: 是否LLM任务 (0/1)
                - [3]: Token数量 (对应原始的Token_Count)
                - [4]: 内存需求 (对应原始的Memory_Req)
        """
        # 计算LLM任务数量
        llm_count = int(num_tasks * self.config['llm_ratio'])
        normal_count = num_tasks - llm_count

        # 任务类型
        task_types = np.concatenate([
            np.zeros(normal_count),
            np.ones(llm_count)
        ])
        np.random.shuffle(task_types)

        # 初始化特征数组 (与原始data_generator.py兼容)
        cpu_cycles = np.zeros(num_tasks)
        data_transfer_mb = np.zeros(num_tasks)
        is_llm = np.zeros(num_tasks)
        token_count = np.zeros(num_tasks)
        memory_req = np.zeros(num_tasks)

        for i in range(num_tasks):
            is_llm[i] = task_types[i]

            if is_llm[i] == 1:  # LLM任务
                # 使用计算强度范围作为CPU周期
                cpu_cycles[i] = np.random.uniform(*self.config['compute_intensity_range'])
                # 使用数据大小范围作为数据传输大小
                data_transfer_mb[i] = np.random.uniform(*self.config['data_size_range'])
                # Token数量
                token_count[i] = np.random.randint(*self.config['token_count_range'])
                # 内存需求
                memory_req[i] = np.random.uniform(*self.config['memory_req_range']['llm'])
            else:  # 普通任务
                # 普通任务的CPU周期
                cpu_cycles[i] = np.random.uniform(*self.config['compute_intensity_range'])
                # 普通任务的数据传输大小
                data_transfer_mb[i] = np.random.uniform(*self.config['data_size_range'])
                # 普通任务没有token
                token_count[i] = 0
                # 普通任务内存需求
                memory_req[i] = np.random.uniform(*self.config['memory_req_range']['normal'])

        # 构建特征数组 (与原始data_generator.py的顺序一致)
        task_features = np.vstack([
            cpu_cycles,        # [0]: Cpu_Cycles
            data_transfer_mb,  # [1]: Data_Transfer_MB
            is_llm,           # [2]: Is_LLM
            token_count,      # [3]: Token_Count
            memory_req        # [4]: Memory_Req
        ])

        return task_features

    def generate_discrete_machines(self):
        """
        生成离散类型的机器配置

        当设备数为5时，从3种类型机器中抽取5个
        """
        num_edge_servers = self.config['num_edge_servers']
        machine_types = list(self.config['machine_types'].keys())

        # 为边缘设备随机选择机器类型
        selected_types = np.random.choice(machine_types, size=num_edge_servers, replace=True)

        # 构建机器资源数据
        machines_data = []

        # 添加用户设备 (索引0) - 使用与原始data_generator.py兼容的列名
        user_device = self.config['user_device']
        machines_data.append({
            'CPU_frequence': user_device['cpu_freq'],
            'Memory_Available': user_device['memory'],
            'token_per_second': 1.0 / user_device['tpot'],  # 转换TPOT为token_per_second
            'base_execution_time': user_device['ttft']
        })

        # 添加边缘设备 - 使用与原始data_generator.py兼容的列名
        for i, machine_type in enumerate(selected_types):
            machine_config = self.config['machine_types'][machine_type]
            machines_data.append({
                'CPU_frequence': machine_config['cpu_freq'],
                'Memory_Available': machine_config['memory'],
                'token_per_second': 1.0 / machine_config['tpot'],  # 转换TPOT为token_per_second
                'base_execution_time': machine_config['ttft']
            })

        return pd.DataFrame(machines_data)

    def generate_communication_speed(self):
        """生成通信速度矩阵"""
        num_machines = 1 + self.config['num_edge_servers']

        # 基础通信速度 (Mbps)
        base_speed = 100.0

        # 生成对称的通信速度矩阵
        comm_speed = np.zeros((num_machines, num_machines))

        for i in range(num_machines):
            for j in range(i + 1, num_machines):
                # 用户设备到边缘设备通信稍慢
                if i == 0 or j == 0:
                    speed = np.random.uniform(50, 100)
                else:
                    # 边缘设备之间通信较快
                    speed = np.random.uniform(80, 120)

                comm_speed[i, j] = speed
                comm_speed[j, i] = speed

        return pd.DataFrame(comm_speed)

    def save_dataset(self, idx, task_features, adj_matrix):
        """保存数据集 (与原始data_generator.py兼容)"""
        output_path = self.config['full_output_dir']

        # 使用与原始data_generator.py兼容的列名
        feature_names = [
            'Cpu_Cycles',        # 对应 task_features[0]
            'Data_Transfer_MB',  # 对应 task_features[1]
            'Is_LLM',           # 对应 task_features[2]
            'Token_Count',      # 对应 task_features[3]
            'Memory_Req'        # 对应 task_features[4]
        ]

        # 转置任务特征，使每行是一个任务，每列是一个特征
        task_ids = [str(i) for i in range(task_features.shape[1])]
        task_features_df = pd.DataFrame(
            task_features.T,  # 转置，使每行是一个任务，每列是一个特征
            columns=feature_names,  # 将特征名称设为列名
            index=task_ids  # 将任务ID设为行索引
        )

        # 保存为Excel (与原始data_generator.py的格式一致)
        excel_task_feature_path = os.path.join(output_path, f'task_{idx}_features.xlsx')
        task_features_df.to_excel(excel_task_feature_path)

        # 保存邻接矩阵
        np.save(os.path.join(self.config['full_output_dir'], f'adjacency_{idx}_matrix.npy'), adj_matrix)

    def visualize_dag(self, adj_matrix, task_features, idx):
        """可视化分层DAG"""
        G = nx.DiGraph(adj_matrix)
        plt.figure(figsize=(12, 8))

        # 使用分层布局
        try:
            pos = nx.nx_agraph.graphviz_layout(G, prog='dot')
        except:
            pos = nx.spring_layout(G, seed=42)

        # 节点颜色：蓝色=普通任务，红色=LLM任务
        is_llm = task_features[2]
        colors = ['lightblue' if llm == 0 else 'lightcoral' for llm in is_llm]

        # 绘制图
        nx.draw(G, pos, with_labels=True, node_color=colors,
                node_size=800, arrowsize=20, font_weight='bold',
                font_size=10, arrows=True)

        plt.title(f'Layered DAG #{idx} - Blue: Normal, Red: LLM Tasks')

        # 保存
        vis_dir = os.path.join(self.config['full_output_dir'], 'visualizations')
        os.makedirs(vis_dir, exist_ok=True)
        plt.savefig(os.path.join(vis_dir, f'layered_dag_{idx}.png'), dpi=300, bbox_inches='tight')
        plt.close()

    def generate_datasets(self):
        """生成完整数据集"""
        print("🚀 开始生成改进的数据集")
        print("=" * 60)
        print("📋 改进特点:")
        print("  ✅ 分层DAG生成 (来自别人的算法)")
        print("  ✅ 保持任务特征设计")
        print("  ✅ 离散机器类型")
        print("=" * 60)

        # 生成离散机器配置
        machine_resources = self.generate_discrete_machines()
        machine_resources.to_excel(
            os.path.join(self.config['full_output_dir'], 'machines_resource1.xlsx'),
            index=False
        )
        print(f"✅ 机器配置已生成: {len(machine_resources)} 台机器")

        # 显示机器类型分布
        type_counts = machine_resources['Type'].value_counts()
        print("📊 机器类型分布:")
        for machine_type, count in type_counts.items():
            print(f"  {machine_type}: {count} 台")

        # 生成通信速度 (使用与原始data_generator.py兼容的文件名)
        comm_speed = self.generate_communication_speed()
        comm_speed.to_excel(
            os.path.join(self.config['full_output_dir'], 'machine_commu_speed1.xlsx'),
            index=False, header=False
        )
        print(f"✅ 通信速度矩阵已生成")

        # 生成DAG数据集
        print(f"\n🔄 开始生成 {self.config['num_datasets']} 个分层DAG...")

        dag_stats = {
            'layers': [],
            'edges': [],
            'llm_ratios': []
        }

        for i in range(self.config['num_datasets']):
            # 生成分层DAG
            adj_matrix = self.daggen_layered(
                n=self.config['num_tasks'],
                fat=self.config['dag_fat'],
                density=self.config['dag_density'],
                seed=i  # 使用索引作为种子确保可重现
            )

            # 生成任务特征
            task_features = self.generate_task_features(self.config['num_tasks'])

            # 统计信息
            num_edges = np.sum(adj_matrix)
            llm_ratio = np.mean(task_features[2])

            dag_stats['edges'].append(num_edges)
            dag_stats['llm_ratios'].append(llm_ratio)

            # 可视化前几个DAG
            if i < 5:
                self.visualize_dag(adj_matrix, task_features, i)

            # 保存数据
            self.save_dataset(i, task_features, adj_matrix)

            if (i + 1) % 50 == 0:
                print(f"  已生成: {i + 1}/{self.config['num_datasets']}")

        # 输出统计信息
        print(f"\n📈 数据集统计:")
        print(f"  平均边数: {np.mean(dag_stats['edges']):.1f}")
        print(f"  边数范围: {min(dag_stats['edges'])} - {max(dag_stats['edges'])}")
        print(f"  平均LLM比例: {np.mean(dag_stats['llm_ratios']):.1%}")

        # 保存配置
        self._save_config()

        print(f"\n🎉 数据集生成完成!")
        print(f"📁 保存位置: {self.config['full_output_dir']}")

    def _save_config(self):
        """保存配置信息"""
        config_path = os.path.join(self.config['full_output_dir'], 'generation_config.txt')

        with open(config_path, 'w', encoding='utf-8') as f:
            f.write("改进数据集生成配置\n")
            f.write("=" * 40 + "\n\n")

            f.write("DAG生成参数:\n")
            f.write(f"  任务数量: {self.config['num_tasks']}\n")
            f.write(f"  数据集数量: {self.config['num_datasets']}\n")
            f.write(f"  分层因子: {self.config['dag_fat']}\n")
            f.write(f"  密度: {self.config['dag_density']}\n")
            f.write(f"  LLM任务比例: {self.config['llm_ratio']}\n\n")

            f.write("机器类型配置:\n")
            for type_name, type_config in self.config['machine_types'].items():
                f.write(f"  {type_name}: {type_config['description']}\n")
                f.write(f"    内存: {type_config['memory']} GB\n")
                f.write(f"    CPU: {type_config['cpu_freq']} GHz\n")
                f.write(f"    TTFT: {type_config['ttft']} s\n")
                f.write(f"    TPOT: {type_config['tpot']} s\n\n")

            f.write("用户设备配置:\n")
            user_device = self.config['user_device']
            f.write(f"  内存: {user_device['memory']} GB\n")
            f.write(f"  CPU: {user_device['cpu_freq']} GHz\n")
            f.write(f"  TTFT: {user_device['ttft']} s\n")
            f.write(f"  TPOT: {user_device['tpot']} s\n")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='改进的DAG数据集生成器')
    parser.add_argument('--num_tasks', type=int, default=30, help='任务数量')
    parser.add_argument('--num_datasets', type=int, default=200, help='数据集数量')
    parser.add_argument('--num_edge_servers', type=int, default=5, help='边缘设备数量')
    parser.add_argument('--dag_fat', type=float, default=0.4, help='DAG分层因子')
    parser.add_argument('--dag_density', type=float, default=0.5, help='DAG密度')
    parser.add_argument('--llm_ratio', type=float, default=0.4, help='LLM任务比例')
    parser.add_argument('--output_dir', type=str, default='dataset/training', help='输出目录')

    args = parser.parse_args()

    # 创建配置
    config = {
        'num_tasks': args.num_tasks,
        'num_datasets': args.num_datasets,
        'num_edge_servers': args.num_edge_servers,
        'dag_fat': args.dag_fat,
        'dag_density': args.dag_density,
        'llm_ratio': args.llm_ratio,
        'output_dir': args.output_dir
    }

    # 生成数据集
    generator = ImprovedDataGenerator(config)
    generator.generate_datasets()

if __name__ == "__main__":
    main()
