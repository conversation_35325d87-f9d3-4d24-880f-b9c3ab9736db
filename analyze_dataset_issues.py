#!/usr/bin/env python3
"""
分析数据集中可能导致周期性失败的问题DAG
"""

import pandas as pd
import numpy as np
import os
import matplotlib.pyplot as plt
from collections import defaultdict

def analyze_dag_characteristics(dataset_dir):
    """分析DAG特征，找出可能导致调度失败的问题"""

    print("=== 分析DAG数据集特征 ===")

    # 读取机器资源配置
    machines_file = os.path.join(dataset_dir, 'machines_resource1.xlsx')
    try:
        df_machines = pd.read_excel(machines_file)
        print(f"机器配置:")
        print(df_machines)

        # 使用正确的列名
        memory_column = 'Memory_Available' if 'Memory_Available' in df_machines.columns else 'memory_capacity'

        total_memory = df_machines[memory_column].sum()
        max_single_memory = df_machines[memory_column].max()
        min_single_memory = df_machines[memory_column].min()

        print(f"\n总内存: {total_memory:.2f} GB")
        print(f"单机最大内存: {max_single_memory:.2f} GB")
        print(f"单机最小内存: {min_single_memory:.2f} GB")

    except Exception as e:
        print(f"读取机器配置失败: {e}")
        return None, None

    # 分析所有DAG
    problematic_dags = []
    dag_stats = []

    for dag_id in range(200):  # 假设有200个DAG
        task_file = os.path.join(dataset_dir, f'task_{dag_id}_features.xlsx')
        adj_file = os.path.join(dataset_dir, f'adjacency_{dag_id}_matrix.npy')

        if not os.path.exists(task_file) or not os.path.exists(adj_file):
            continue

        try:
            # 读取任务特征
            df_tasks = pd.read_excel(task_file)
            adj_matrix = np.load(adj_file)

            # 统计基本信息
            num_tasks = len(df_tasks)
            llm_tasks = df_tasks[df_tasks['Is_LLM'] == 1]
            normal_tasks = df_tasks[df_tasks['Is_LLM'] == 0]

            # 内存分析
            max_memory_req = df_tasks['Memory_Req'].max()
            total_memory_req = df_tasks['Memory_Req'].sum()
            avg_memory_req = df_tasks['Memory_Req'].mean()

            # LLM任务内存分析
            llm_memory_reqs = llm_tasks['Memory_Req'].tolist() if len(llm_tasks) > 0 else []
            max_llm_memory = max(llm_memory_reqs) if llm_memory_reqs else 0

            # DAG结构分析
            edges = np.sum(adj_matrix)
            density = edges / (num_tasks * (num_tasks - 1)) if num_tasks > 1 else 0

            # 计算入度和出度
            in_degrees = np.sum(adj_matrix, axis=0)
            out_degrees = np.sum(adj_matrix, axis=1)
            max_in_degree = np.max(in_degrees)
            max_out_degree = np.max(out_degrees)

            # 检查潜在问题
            issues = []

            # 1. 内存问题
            if max_memory_req > max_single_memory:
                issues.append(f"超大内存任务({max_memory_req:.2f}GB > {max_single_memory:.2f}GB)")

            if total_memory_req > total_memory * 0.8:  # 总内存需求超过80%
                issues.append(f"总内存需求过高({total_memory_req:.2f}GB / {total_memory:.2f}GB)")

            # 2. LLM任务集中问题
            if len(llm_tasks) > 15:  # 超过一半是LLM任务
                issues.append(f"LLM任务过多({len(llm_tasks)}/{num_tasks})")

            # 3. DAG结构问题
            if density < 0.1:  # 密度过低，可能导致并行度不足
                issues.append(f"DAG密度过低({density:.3f})")

            if max_in_degree > 5:  # 入度过高，可能导致依赖瓶颈
                issues.append(f"最大入度过高({max_in_degree})")

            # 记录统计信息
            dag_stat = {
                'dag_id': dag_id,
                'num_tasks': num_tasks,
                'num_llm': len(llm_tasks),
                'num_normal': len(normal_tasks),
                'max_memory': max_memory_req,
                'total_memory': total_memory_req,
                'avg_memory': avg_memory_req,
                'max_llm_memory': max_llm_memory,
                'edges': edges,
                'density': density,
                'max_in_degree': max_in_degree,
                'max_out_degree': max_out_degree,
                'issues': issues
            }

            dag_stats.append(dag_stat)

            if issues:
                problematic_dags.append((dag_id, issues))

        except Exception as e:
            print(f"分析DAG {dag_id}失败: {e}")
            continue

    # 输出分析结果
    print(f"\n=== 分析结果 ===")
    print(f"总DAG数: {len(dag_stats)}")
    print(f"问题DAG数: {len(problematic_dags)}")

    if problematic_dags:
        print(f"\n问题DAG列表:")
        for dag_id, issues in problematic_dags[:10]:  # 只显示前10个
            print(f"  DAG {dag_id}: {', '.join(issues)}")

        if len(problematic_dags) > 10:
            print(f"  ... 还有{len(problematic_dags) - 10}个问题DAG")

    # 统计分析
    if dag_stats:
        df_stats = pd.DataFrame(dag_stats)

        print(f"\n=== 统计摘要 ===")
        print(f"最大内存需求: {df_stats['max_memory'].max():.2f} GB")
        print(f"平均最大内存需求: {df_stats['max_memory'].mean():.2f} GB")
        print(f"总内存需求范围: {df_stats['total_memory'].min():.2f} - {df_stats['total_memory'].max():.2f} GB")
        print(f"LLM任务数范围: {df_stats['num_llm'].min()} - {df_stats['num_llm'].max()}")
        print(f"DAG密度范围: {df_stats['density'].min():.3f} - {df_stats['density'].max():.3f}")

        # 找出可能导致周期性失败的模式
        print(f"\n=== 周期性失败分析 ===")

        # 按DAG ID的模式分组
        pattern_groups = defaultdict(list)
        for stat in dag_stats:
            pattern = stat['dag_id'] % 20  # 每20个DAG为一个周期
            pattern_groups[pattern].append(stat)

        # 分析每个模式的问题率
        for pattern in range(20):
            if pattern in pattern_groups:
                group = pattern_groups[pattern]
                problem_count = sum(1 for stat in group if stat['issues'])
                total_count = len(group)
                problem_rate = problem_count / total_count if total_count > 0 else 0

                if problem_rate > 0.3:  # 问题率超过30%
                    print(f"  模式 {pattern}: {problem_count}/{total_count} ({problem_rate:.1%}) 有问题")

                    # 分析这个模式的共同特征
                    avg_max_memory = np.mean([stat['max_memory'] for stat in group])
                    avg_llm_count = np.mean([stat['num_llm'] for stat in group])
                    avg_density = np.mean([stat['density'] for stat in group])

                    print(f"    平均最大内存: {avg_max_memory:.2f} GB")
                    print(f"    平均LLM任务数: {avg_llm_count:.1f}")
                    print(f"    平均密度: {avg_density:.3f}")

    return dag_stats, problematic_dags

def suggest_improvements(dag_stats, problematic_dags):
    """基于分析结果提出改进建议"""

    print(f"\n=== 改进建议 ===")

    if not dag_stats:
        print("无法提供建议：没有有效的DAG统计数据")
        return

    df_stats = pd.DataFrame(dag_stats)

    # 1. 死锁检测参数调整
    max_memory = df_stats['max_memory'].max()
    avg_llm_count = df_stats['num_llm'].mean()

    print("1. 死锁检测参数调整:")
    if len(problematic_dags) > len(dag_stats) * 0.1:  # 超过10%的DAG有问题
        print(f"   - 增加MAX_DEADLOCK_ATTEMPTS: 从 num_tasks*2 改为 num_tasks*3")
        print(f"   - 原因: {len(problematic_dags)}/{len(dag_stats)} ({len(problematic_dags)/len(dag_stats):.1%}) DAG有潜在调度困难")

    # 2. 内存管理改进
    print("\n2. 内存管理改进:")
    if max_memory > 60:  # 假设最大机器内存是64GB
        print(f"   - 实现更智能的内存分配策略")
        print(f"   - 考虑任务拆分或流水线执行")
        print(f"   - 最大内存需求: {max_memory:.2f} GB")

    # 3. 任务选择策略改进
    print("\n3. 任务选择策略改进:")
    print(f"   - 修复任务选择器的偏向性问题")
    print(f"   - 实现基于优先级的任务选择")
    print(f"   - 考虑内存需求和依赖关系的平衡")

    # 4. 奖励函数调整
    print("\n4. 奖励函数调整:")
    print(f"   - 增加对内存效率的奖励权重")
    print(f"   - 减少死锁惩罚的严厉程度")
    print(f"   - 添加渐进式奖励，避免稀疏奖励问题")

    # 5. 数据集质量改进
    print("\n5. 数据集质量改进:")
    if len(problematic_dags) > 0:
        print(f"   - 过滤或修复{len(problematic_dags)}个问题DAG")
        print(f"   - 增加数据集的多样性")
        print(f"   - 平衡LLM任务和普通任务的比例")

if __name__ == "__main__":
    dataset_dir = "dataset/training/DAG_30_edges_5_mem_57-64GB_density_0.2_0.6"

    if not os.path.exists(dataset_dir):
        print(f"数据集目录不存在: {dataset_dir}")
        exit(1)

    dag_stats, problematic_dags = analyze_dag_characteristics(dataset_dir)
    suggest_improvements(dag_stats, problematic_dags)
