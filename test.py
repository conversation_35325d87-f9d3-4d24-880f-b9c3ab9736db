#!/user/bin/env python3
# -*- coding: utf-8 -*-
import os
import numpy as np

# 测试数据集路径
file_path = 'dataset/training/DAG_30_edges_5_mem_57-64GB_density_0.2_0.6'  # 测试数据集路径

def analyze_adjacency_matrix(adj_matrix):
    """分析邻接矩阵的依赖关系"""
    num_tasks = adj_matrix.shape[0]

    # 检查入度为0的节点（初始就绪任务）
    initial_ready_tasks = []
    for j in range(num_tasks):
        has_dependencies = False
        for i in range(num_tasks):
            if adj_matrix[i, j] > 0:  # j依赖于i
                has_dependencies = True
                break
        if not has_dependencies:
            initial_ready_tasks.append(j)

    print(f"初始就绪任务（入度为0的节点）: {initial_ready_tasks}")

    # 检查每个任务的依赖关系
    for j in range(num_tasks):
        dependencies = []
        for i in range(num_tasks):
            if adj_matrix[i, j] > 0:  # j依赖于i
                dependencies.append(i)

        if dependencies:
            print(f"任务 {j} 依赖于任务: {dependencies}")

    # 检查是否存在循环依赖
    def has_cycle(node, visited, rec_stack):
        visited[node] = True
        rec_stack[node] = True

        # 检查所有依赖于当前节点的任务
        for j in range(num_tasks):
            if adj_matrix[node, j] > 0:  # j依赖于node
                if not visited[j]:
                    if has_cycle(j, visited, rec_stack):
                        return True
                elif rec_stack[j]:
                    return True

        rec_stack[node] = False
        return False

    # 检查图中是否存在循环
    def is_cyclic():
        visited = [False] * num_tasks
        rec_stack = [False] * num_tasks

        for i in range(num_tasks):
            if not visited[i]:
                if has_cycle(i, visited, rec_stack):
                    return True
        return False

    if is_cyclic():
        print("警告: 邻接矩阵中存在循环依赖！")
    else:
        print("邻接矩阵表示一个有效的有向无环图(DAG)，没有循环依赖。")

    # 检查任务完成后的就绪任务更新
    def get_ready_tasks_after_completion(completed_tasks):
        ready_tasks = []
        for j in range(num_tasks):
            if j in completed_tasks:
                continue

            all_deps_completed = True
            for i in range(num_tasks):
                if adj_matrix[i, j] > 0 and i not in completed_tasks:  # j依赖于i，但i尚未完成
                    all_deps_completed = False
                    break

            if all_deps_completed:
                ready_tasks.append(j)

        return ready_tasks

    # 模拟任务执行过程
    completed_tasks = set()
    ready_tasks = initial_ready_tasks.copy()

    print("\n模拟任务执行过程:")
    step = 1

    while ready_tasks:
        print(f"\n步骤 {step}:")
        print(f"当前就绪任务: {ready_tasks}")

        # 执行第一个就绪任务
        task_to_complete = ready_tasks.pop(0)
        completed_tasks.add(task_to_complete)
        print(f"完成任务: {task_to_complete}")
        print(f"已完成任务: {completed_tasks}")

        # 更新就绪任务
        new_ready_tasks = get_ready_tasks_after_completion(completed_tasks)
        for task in new_ready_tasks:
            if task not in ready_tasks and task not in completed_tasks:
                ready_tasks.append(task)

        print(f"更新后的就绪任务: {ready_tasks}")
        step += 1

        # 防止无限循环
        if step > num_tasks * 2:
            print("警告: 可能存在问题，任务执行超过预期步骤数！")
            break

    if len(completed_tasks) == num_tasks:
        print(f"\n所有 {num_tasks} 个任务都已完成，依赖关系处理正确。")
    else:
        print(f"\n只完成了 {len(completed_tasks)}/{num_tasks} 个任务，可能存在依赖关系问题！")
        print(f"未完成的任务: {set(range(num_tasks)) - completed_tasks}")

# 分析多个邻接矩阵
task_size = []
for i in range(5):  # 只分析前5个矩阵
    file1 = os.path.join(file_path, 'adjacency_{}_matrix.npy'.format(i))
    try:
        adj_matrix = np.load(file1)
        print(f"\n\n===== 分析邻接矩阵 {i} =====")
        print(f"矩阵形状: {adj_matrix.shape}")

        # 打印矩阵（如果太大，只打印部分）
        if adj_matrix.shape[0] <= 10:
            print("邻接矩阵:")
            print(adj_matrix)
        else:
            print("邻接矩阵太大，只显示部分:")
            print(adj_matrix[:10, :10])

        # 分析邻接矩阵
        analyze_adjacency_matrix(adj_matrix)

        task_size.append(adj_matrix.shape[0])
    except Exception as e:
        print(f"无法加载邻接矩阵 {i}: {str(e)}")

print("\n任务大小列表:", task_size)
