#!/usr/bin/env python3
import pandas as pd
import numpy as np

# 检查机器资源文件
machine_file = "dataset/training/DAG_30_edges_6_mem_64GB_density_0.4_0.6/machines_resource1.xlsx"
print("机器资源文件列名:")
df = pd.read_excel(machine_file)
print(df.columns.tolist())
print("\n前几行数据:")
print(df.head())

# 检查任务特征文件
task_file = "dataset/training/DAG_30_edges_6_mem_64GB_density_0.4_0.6/task_1_features.xlsx"
print("\n\n任务特征文件列名:")
df_task = pd.read_excel(task_file)
print(df_task.columns.tolist())
print("\n前几行数据:")
print(df_task.head())
